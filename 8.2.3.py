import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, Menu
import threading
import requests
import json
import re
import time
from datetime import datetime, timedelta
from urllib.parse import urljoin
import random
import logging
import os
import pytz # For timezone handling in news timestamps and global market times

# Third-party library imports
# Ensure these are installed: pip install requests feedparser beautifulsoup4 nltk yfinance newsapi-python google-generativeai matplotlib pytz
import feedparser
from bs4 import BeautifulSoup
import nltk
from nltk.sentiment.vader import SentimentIntensityAnalyzer
import yfinance as yf
from newsapi import NewsApiClient
import google.generativeai as genai

# Imports for Matplotlib Charting
import matplotlib
matplotlib.use('TkAgg') # Specify the backend for tkinter
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk # Added NavigationToolbar2Tk

# ==============================================================================
# SECTION 2: INITIAL SETUP AND CONFIGURATION
# ==============================================================================

# --- Logging Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s',
    handlers=[
        logging.FileHandler("teu_8_8_financial_analysis.log"),
        logging.StreamHandler()
    ]
)

# --- NLTK Downloader ---
try:
    nltk.data.find('sentiment/vader_lexicon.zip')
except Exception: # Changed from nltk.downloader.DownloadError to Exception for robustness
    logging.info("Downloading NLTK VADER lexicon...")
    nltk.download('vader_lexicon')
    logging.info("NLTK VADER lexicon downloaded successfully.")

# --- API Key Configuration ---
# WARNING: Hardcoding API keys is insecure. Use environment variables for production.
GEMINI_API_KEY = "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og" # As requested, the provided Gemini API key is embedded
NEWSAPI_KEY = os.environ.get("NEWSAPI_KEY", "********************************") # Using a default key from your previous files

# Configure the Google Gemini AI client
if GEMINI_API_KEY and "YOUR_KEY" not in GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        logging.info("Gemini AI client configured successfully.")
    except Exception as e:
        logging.error(f"Failed to configure Gemini API with provided key: {e}")
else:
    logging.warning("Gemini API key is not configured. AI features will be limited.")

# ==============================================================================
# SECTION 3: DEDICATED ANALYSIS TOOLS CLASS
# ==============================================================================

class AnalysisTools:
    """A dedicated class to encapsulate various financial analysis methods."""
    def __init__(self):
        self.sentiment_analyzer = SentimentIntensityAnalyzer()
        logging.info("AnalysisTools class initialized.")
        self.gemini_model_summarizer = None
        if GEMINI_API_KEY and "YOUR_KEY" not in GEMINI_API_KEY:
            try:
                # Use a different model for summarization if performance is an issue
                self.gemini_model_summarizer = genai.GenerativeModel('gemini-1.0-pro')
                logging.info("Gemini 1.0 Pro model for summarization initialized successfully.")
            except Exception as e:
                logging.error(f"Failed to initialize Gemini summarizer model: {e}")

    def get_sentiment_score(self, text: str) -> float:
        if not isinstance(text, str): return 0.0
        return self.sentiment_analyzer.polarity_scores(text)['compound']

    def summarize_text_with_gemini(self, text: str, num_sentences: int = 3) -> str:
        """Summarizes text using Gemini AI."""
        if not self.gemini_model_summarizer:
            return text[:200] + "..." if len(text) > 200 else text # Fallback if summarizer not available

        prompt = f"Summarize the following text into approximately {num_sentences} concise sentences, focusing on key financial or market implications:\n\n{text}"
        try:
            response = self.gemini_model_summarizer.generate_content(prompt)
            return response.text.strip()
        except Exception as e:
            logging.error(f"Gemini summarization failed: {e}")
            return text[:200] + "..." if len(text) > 200 else text # Fallback on error

    def analyze_speech_impact_with_gemini(self, speech_text: str) -> str:
        """Analyzes a speech text for its potential market impact using Gemini AI."""
        if not self.gemini_model_summarizer: # Using summarizer model for this as well
            return "Cannot analyze speech impact: Gemini model not available."

        prompt = f"""
        Analyze the following speech or statement for its potential impact on financial markets (e.g., specific sectors, currencies, commodities, or general market sentiment). Identify the key market-moving points and briefly explain the likely effect.
        
        Speech/Statement:
        {speech_text[:2000]} # Limit input length to prevent API errors
        
        Provide a concise analysis (2-4 sentences):
        """
        try:
            response = self.gemini_model_summarizer.generate_content(prompt)
            return response.text.strip()
        except Exception as e:
            logging.error(f"Gemini speech impact analysis failed: {e}")
            return f"Failed to analyze speech impact due to AI error: {e}"

# ==============================================================================
# SECTION 4: CORE LOGIC - The FinancialAnalyzer Class
# ==============================================================================

class FinancialAnalyzer:
    """The core engine for fetching, processing, and analyzing financial data."""
    def __init__(self):
        logging.info("Initializing FinancialAnalyzer...")
        # Data storage
        self.news_data = []
        self.speech_data = []
        self.economic_events = [] # This will be phased out / repurposed
        self.global_market_times = [] # New list for 40 countries' market times
        self.predictions = []
        self.aggregated_probabilities = {}
        self.gemini_full_analysis = "Analysis has not been run yet."
        self.asset_name_map = {
            "BTC-USD": "Bitcoin (USD)", "ETH-USD": "Ethereum (USD)", "CL=F": "WTI Crude (USD)",
            "BZ=F": "Brent Crude (USD)", "GC=F": "Gold", "SI=F": "Silver",
            "^IXIC": "NASDAQ Composite", "^GSPC": "S&P 500", "^DJI": "Dow Jones Industrial Average",
            "CAD=X": "USD/CAD", "USDVND=X": "USD/VND", "USDRUB=X": "USD/RUB", "EURUSD=X": "EUR/USD",
            "GBPUSD=X": "GBP/USD", "JPY=X": "USD/JPY", "CNY=X": "USD/CNY", "AUD=X": "AUD/USD",
            "NZD=X": "NZD/USD", "CHF=X": "USD/CHF", "SEK=X": "USD/SEK", "NOK=X": "USD/NOK",
            "DKK=X": "USD/DKK", "ZAR=X": "USD/ZAR", "INR=X": "USD/INR", "BRL=X": "USD/BRL",
            "MXN=X": "USD/MXN", "TRY=X": "USD/TRY", "KRW=X": "USD/KRW", "SGD=X": "USD/SGD",
            "HKD=X": "USD/HKD", "MYR=X": "USD/MYR", "IDR=X": "USD/IDR", "PHP=X": "USD/PHP",
            "THB=X": "USD/THB", "PLN=X": "USD/PLN", "HUF=X": "USD/HUF", "CZK=X": "USD/CZK",
            "ILS=X": "USD/ILS", "EGP=X": "USD/EGP", "SAR=X": "USD/SAR", "AED=X": "USD/AED",
            "QAR=X": "USD/QAR", "KWD=X": "USD/KWD", "BHD=X": "USD/BHD", "OMR=X": "USD/OMR",
            "JOD=X": "USD/JOD", "LBP=X": "USD/LBP", "PKR=X": "USD/PKR", "LKR=X": "USD/LKR",
            "BDT=X": "USD/BDT", "NPR=X": "USD/NPR", "AFN=X": "USD/AFN", "KZT=X": "USD/KZT",
            "UZS=X": "USD/UZS", "GEL=X": "USD/GEL", "AMD=X": "USD/AMD", "AZN=X": "USD/AZN",
            "BYN=X": "USD/BYN", "UAH=X": "USD/UAH", "MDL=X": "USD/MDL"
        }
        self.asset_list = list(self.asset_name_map.keys())


        # Market data placeholders
        self.bitcoin_price = 0
        self.price_change_24h = 0
        self.market_data = {} # To store fetched prices

        # API Clients and Tools
        self.newsapi_client = NewsApiClient(api_key=NEWSAPI_KEY)
        self.analysis_tools = AnalysisTools() # Initialize AnalysisTools here
        self.gemini_model = None
        try:
            self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
            logging.info("Gemini 1.5 Flash model initialized successfully.")
        except Exception as e:
            logging.error(f"Failed to initialize Gemini model: {e}")
            logging.warning("Gemini AI features will be limited due to model initialization failure.")
        
        # --- Hardcoded 40 Countries for Global Market Times ---
        # Prioritized for "20 main countries on top" + Vietnam, Russia
        self.country_market_data = [
            # Tier 1: Major Global Markets (approx. 20)
            {"country": "United States", "timezone": "America/New_York", "exchange": "NYSE/NASDAQ", "open": "09:30", "close": "16:00", "currency_pair": "USD"},
            {"country": "United Kingdom", "timezone": "Europe/London", "exchange": "LSE", "open": "08:00", "close": "16:30", "currency_pair": "GBP=X"},
            {"country": "Japan", "timezone": "Asia/Tokyo", "exchange": "TSE", "open": "09:00", "close": "15:00", "currency_pair": "JPY=X"},
            {"country": "Germany", "timezone": "Europe/Berlin", "exchange": "XETRA", "open": "09:00", "close": "17:30", "currency_pair": "EURUSD=X"}, # EUR/USD for Eurozone
            {"country": "China", "timezone": "Asia/Shanghai", "exchange": "SSE/SZSE", "open": "09:30", "close": "15:00", "currency_pair": "CNY=X"},
            {"country": "Canada", "timezone": "America/Toronto", "exchange": "TSX", "open": "09:30", "close": "16:00", "currency_pair": "CAD=X"},
            {"country": "Australia", "timezone": "Australia/Sydney", "exchange": "ASX", "open": "10:00", "close": "16:00", "currency_pair": "AUD=X"},
            {"country": "India", "timezone": "Asia/Kolkata", "exchange": "NSE/BSE", "open": "09:15", "close": "15:30", "currency_pair": "INR=X"},
            {"country": "France", "timezone": "Europe/Paris", "exchange": "Euronext Paris", "open": "09:00", "close": "17:30", "currency_pair": "EURUSD=X"},
            {"country": "Switzerland", "timezone": "Europe/Zurich", "exchange": "SIX Swiss", "open": "09:00", "close": "17:30", "currency_pair": "CHF=X"},
            {"country": "Hong Kong", "timezone": "Asia/Hong_Kong", "exchange": "HKEX", "open": "09:30", "close": "16:00", "currency_pair": "HKD=X"},
            {"country": "Singapore", "timezone": "Asia/Singapore", "exchange": "SGX", "open": "09:00", "close": "17:00", "currency_pair": "SGD=X"},
            {"country": "South Korea", "timezone": "Asia/Seoul", "exchange": "KRX", "open": "09:00", "close": "15:30", "currency_pair": "KRW=X"},
            {"country": "Brazil", "timezone": "America/Sao_Paulo", "exchange": "B3", "open": "10:00", "close": "17:00", "currency_pair": "BRL=X"},
            {"country": "South Africa", "timezone": "Africa/Johannesburg", "exchange": "JSE", "open": "09:00", "close": "17:00", "currency_pair": "ZAR=X"},
            {"country": "Sweden", "timezone": "Europe/Stockholm", "exchange": "Nasdaq Stockholm", "open": "09:00", "close": "17:30", "currency_pair": "SEK=X"},
            {"country": "Netherlands", "timezone": "Europe/Amsterdam", "exchange": "Euronext Amsterdam", "open": "09:00", "close": "17:30", "currency_pair": "EURUSD=X"},
            {"country": "Italy", "timezone": "Europe/Rome", "exchange": "Borsa Italiana", "open": "09:00", "close": "17:30", "currency_pair": "EURUSD=X"},
            {"country": "Spain", "timezone": "Europe/Madrid", "exchange": "BME Spanish", "open": "09:00", "close": "17:30", "currency_pair": "EURUSD=X"},
            {"country": "Mexico", "timezone": "America/Mexico_City", "exchange": "BMV", "open": "08:30", "close": "15:00", "currency_pair": "MXN=X"},
            # Tier 2: Additional Countries (including Vietnam and Russia)
            {"country": "Vietnam", "timezone": "Asia/Ho_Chi_Minh", "exchange": "HOSE/HNX", "open": "09:00", "close": "15:00", "currency_pair": "USDVND=X"}, # Lunch break 11:30-13:00, but often listed as 9-3 continuous for main hours
            {"country": "Russia", "timezone": "Europe/Moscow", "exchange": "MOEX", "open": "09:50", "close": "18:50", "currency_pair": "USDRUB=X"},
            {"country": "Poland", "timezone": "Europe/Warsaw", "exchange": "WSE", "open": "09:00", "close": "17:00", "currency_pair": "PLN=X"},
            {"country": "Norway", "timezone": "Europe/Oslo", "exchange": "Oslo Børs", "open": "09:00", "close": "16:20", "currency_pair": "NOK=X"},
            {"country": "Denmark", "timezone": "Europe/Copenhagen", "exchange": "Nasdaq Copenhagen", "open": "09:00", "close": "17:00", "currency_pair": "DKK=X"},
            {"country": "Turkey", "timezone": "Europe/Istanbul", "exchange": "Borsa Istanbul", "open": "09:40", "close": "18:00", "currency_pair": "TRY=X"},
            {"country": "Israel", "timezone": "Asia/Jerusalem", "exchange": "TASE", "open": "09:45", "close": "16:15", "currency_pair": "ILS=X"},
            {"country": "Egypt", "timezone": "Africa/Cairo", "exchange": "EGX", "open": "10:00", "close": "14:30", "currency_pair": "EGP=X"},
            {"country": "Saudi Arabia", "timezone": "Asia/Riyadh", "exchange": "Tadawul", "open": "10:00", "close": "15:00", "currency_pair": "SAR=X"},
            {"country": "United Arab Emirates", "timezone": "Asia/Dubai", "exchange": "DFM/ADX", "open": "10:00", "close": "15:00", "currency_pair": "AED=X"},
            {"country": "Qatar", "timezone": "Asia/Qatar", "exchange": "QSE", "open": "09:00", "close": "13:15", "currency_pair": "QAR=X"},
            {"country": "Kuwait", "timezone": "Asia/Kuwait", "exchange": "Boursa Kuwait", "open": "09:00", "close": "13:30", "currency_pair": "KWD=X"},
            {"country": "Pakistan", "timezone": "Asia/Karachi", "exchange": "PSX", "open": "09:30", "close": "15:30", "currency_pair": "PKR=X"},
            {"country": "Malaysia", "timezone": "Asia/Kuala_Lumpur", "exchange": "Bursa Malaysia", "open": "09:00", "close": "17:00", "currency_pair": "MYR=X"},
            {"country": "Indonesia", "timezone": "Asia/Jakarta", "exchange": "IDX", "open": "09:00", "close": "16:00", "currency_pair": "IDR=X"},
            {"country": "Philippines", "timezone": "Asia/Manila", "exchange": "PSE", "open": "09:30", "close": "15:00", "currency_pair": "PHP=X"},
            {"country": "Thailand", "timezone": "Asia/Bangkok", "exchange": "SET", "open": "10:00", "close": "16:30", "currency_pair": "THB=X"},
            {"country": "New Zealand", "timezone": "Pacific/Auckland", "exchange": "NZX", "open": "10:00", "close": "16:45", "currency_pair": "NZD=X"},
            {"country": "Argentina", "timezone": "America/Argentina/Buenos_Aires", "exchange": "BYMA", "open": "11:00", "close": "17:00", "currency_pair": "ARS=X"},
            {"country": "Chile", "timezone": "America/Santiago", "exchange": "Santiago Stock Exchange", "open": "09:30", "close": "16:00", "currency_pair": "CLP=X"},
        ]
        # Common pre/after market definitions (can be adjusted)
        self.pre_market_hours = timedelta(hours=1) # 1 hour before open
        self.after_hours = timedelta(hours=2) # 2 hours after close

    # --------------------------------------------------------------------------
    # Subsection 4.1: Data Fetching Methods (Full Implementations)
    # --------------------------------------------------------------------------

    def fetch_all_data(self) -> bool:
        """Orchestrates fetching of all data in parallel."""
        logging.info("Starting to fetch all data sources.")
        self.news_data = [] # Clear previous data
        self.speech_data = [] # Clear previous data
        self.global_market_times = [] # Clear previous data

        threads = [
            threading.Thread(target=self.fetch_market_prices, name="MarketPriceFetcher"),
            threading.Thread(target=self.fetch_globe_and_mail_news, name="GlobeNewsFetcher"),
            threading.Thread(target=self.fetch_newsapi_news, name="NewsAPIFetcher"),
            threading.Thread(target=self.fetch_speeches, name="SpeechFetcher"),
            threading.Thread(target=self.get_global_market_times, name="GlobalMarketTimesFetcher")
        ]
        for t in threads:
            t.start()
        for t in threads:
            t.join()

        if self.market_data.get("Bitcoin (USD)"):
            self.bitcoin_price = self.market_data["Bitcoin (USD)"][0]
            self.price_change_24h = self.market_data["Bitcoin (USD)"][1]
        
        # Post-processing for news summaries
        for i, article in enumerate(self.news_data):
            num_sentences = 10 if i < 10 else 3
            original_summary = article.get('summary', '') or article.get('description', '')
            if original_summary:
                article['ai_summary'] = self.analysis_tools.summarize_text_with_gemini(original_summary, num_sentences)
            else:
                article['ai_summary'] = "No summary available."
        
        logging.info("All data fetching processes have completed.")
        return len(self.news_data) > 0 or len(self.market_data) > 0


    def fetch_market_prices(self) -> None:
        """Fetches live market prices and currency exchange rates using yfinance."""
        logging.info("Fetching live market prices and currency rates...")
        temp_market_data = {}
        for symbol in self.asset_list:
            name = self.asset_name_map.get(symbol, symbol)
            try:
                ticker = yf.Ticker(symbol)
                data = ticker.info
                curr_price = data.get('regularMarketPrice', data.get('currentPrice', 0))
                # For currencies, 'previousClose' might be more reliable for 24h change
                prev_close = data.get('previousClose', 0)
                change = ((curr_price - prev_close) / prev_close) * 100 if curr_price and prev_close else 0
                temp_market_data[name] = (curr_price, change)
            except Exception as e:
                logging.error(f"Error fetching market data for {name}: {e}")
                temp_market_data[name] = (0, 0)
        self.market_data = temp_market_data

    def fetch_globe_and_mail_news(self, max_articles: int = 20) -> None:
        """Fetches news articles from The Globe and Mail RSS feeds."""
        logging.info("Fetching news from The Globe and Mail RSS feeds...")
        rss_feeds = [
            'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/',
            'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/',
            'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/economy/'
        ]
        fetched_articles = []
        for feed_url in rss_feeds:
            if len(fetched_articles) >= max_articles: break
            try:
                feed = feedparser.parse(feed_url)
                for entry in feed.entries:
                    clean_desc = re.sub(r'<[^>]+>', '', entry.get('summary', ''))
                    # Attempt to get published date and author
                    published_date = entry.get('published', '')
                    author = entry.get('author', 'Unknown Author')
                    
                    fetched_articles.append({
                        'title': entry.get('title', 'No Title'), 'summary': clean_desc.strip(),
                        'link': entry.get('link', ''), 'source': 'The Globe and Mail',
                        'published': published_date, 'author': author
                    })
                    if len(fetched_articles) >= max_articles: break
            except Exception as e:
                logging.error(f"Error parsing Globe and Mail RSS feed {feed_url}: {e}")
        self.news_data.extend(fetched_articles)

    def fetch_newsapi_news(self, max_articles: int = 50) -> None:
        """Fetches top financial news using NewsAPI."""
        logging.info("Fetching top financial news from NewsAPI...")
        if NEWSAPI_KEY == "YOUR_NEWSAPI_KEY": # Check if the key is still a placeholder
            logging.warning("NewsAPI key is not configured. Skipping NewsAPI fetch.")
            return

        try:
            top_headlines = self.newsapi_client.get_top_headlines(
                q='finance OR economy OR market', language='en', country='us', page_size=max_articles
            )
            for article in top_headlines.get('articles', []):
                # NewsAPI usually provides publishedAt and author
                published_at_str = article.get('publishedAt', '')
                author_str = article.get('author', 'Unknown Author')
                self.news_data.append({
                    'title': article.get('title', 'No Title'), 'summary': article.get('description', '') or article.get('content', ''),
                    'link': article.get('url', ''), 'source': article.get('source', {}).get('name', 'Unknown'),
                    'published': published_at_str, 'author': author_str
                })
        except Exception as e:
            logging.error(f"Failed to fetch news from NewsAPI: {e}")

    def fetch_speeches(self, max_speeches: int = 10) -> None:
        """
        Scrapes recent speeches and statements from key financial institutions,
        focusing on highly impactful figures.
        """
        logging.info("Fetching speeches from financial institutions...")
        # Prioritize central banks, major economic organizations, and government statements
        speech_sources = [
            ('Federal Reserve', 'https://www.federalreserve.gov/newsevents/speeches.htm'),
            ('IMF', 'https://www.imf.org/en/News/Speeches'),
            ('ECB', 'https://www.ecb.europa.eu/press/key/html/index.en.html'),
            ('Bank of Canada', 'https://www.bankofcanada.ca/2024/speeches/'),
            ('White House Briefings', 'https://www.whitehouse.gov/briefing-room/statements-releases/'),
            ('Treasury Department', 'https://home.treasury.gov/news/press-releases'), # Added Treasury
        ]
        fetched_speeches = []
        headers = {'User-Agent': 'Mozilla/5.0 FinancialAnalysisBot/1.0'}
        
        for name, url in speech_sources:
            if len(fetched_speeches) >= max_speeches: break
            try:
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Generalized patterns for finding links to speeches/statements
                # Look for common tags that might contain links to articles/speeches
                elements_with_links = soup.find_all(['a', 'h2', 'h3', 'div', 'li', 'p'], class_=re.compile(r'news|press|speech|statement|release|briefing|item', re.IGNORECASE))
                
                for element in elements_with_links:
                    link_tag = element if element.name == 'a' else element.find('a', href=True)
                    if link_tag and link_tag.get('href'):
                        full_url = urljoin(url, link_tag['href'])
                        title = link_tag.get_text(strip=True) or element.get_text(strip=True) # Try parent text if link is empty

                        # Filter for relevance to speeches/statements by keywords in title or link
                        if not re.search(r'speech|statement|remarks|briefing|press conference|release|transcript', title, re.IGNORECASE) \
                            and not re.search(r'speech|statement|remarks|briefing|press', full_url, re.IGNORECASE):
                            continue # Skip if title/link don't seem like a speech

                        if full_url not in [s['link'] for s in fetched_speeches]:
                            # Attempt to fetch content for analysis - crucial for impact
                            speech_content = self._fetch_speech_content(full_url)
                            impact_analysis = self.analysis_tools.analyze_speech_impact_with_gemini(speech_content) if speech_content and speech_content != "No readable content found on page." else "Content not accessible for detailed analysis or too short."

                            fetched_speeches.append({
                                'title': title,
                                'link': full_url,
                                'source': name,
                                'impact_analysis': impact_analysis,
                                'full_content_preview': speech_content[:500] + "..." if speech_content else "No content preview."
                            })
                            if len(fetched_speeches) >= max_speeches: break
            except requests.exceptions.RequestException as e:
                logging.error(f"Error fetching speeches page for {name} ({url}): {e}")
            except Exception as e:
                logging.error(f"General error processing speech source {name}: {e}")
        self.speech_data = fetched_speeches

    def _fetch_speech_content(self, url: str, max_chars: int = 2000) -> str:
        """Attempts to fetch the full text content of a speech from its URL."""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')
            # Look for common article/body tags, prioritizing main content areas
            content_div = soup.find(['article', 'div', 'main', 'section'], class_=re.compile(r'content|body|main|text|article-body|post-content', re.IGNORECASE))
            if content_div:
                # Remove script, style tags to clean text
                for script_or_style in content_div(['script', 'style', 'nav', 'footer', 'header']):
                    script_or_style.extract()
                text = content_div.get_text(separator=' ', strip=True)
                return text[:max_chars] # Return a limited portion
            return "No readable content found on page."
        except requests.exceptions.RequestException as e:
            logging.warning(f"Could not fetch full speech content from {url}: {e}")
            return "Failed to fetch speech content."
        except Exception as e:
            logging.warning(f"Error parsing speech content from {url}: {e}")
            return "Error processing speech content."


    def get_global_market_times(self) -> None:
        """
        Calculates and stores live local times and stock market status for 40 countries.
        """
        logging.info("Calculating global market times and status for 40 countries.")
        self.global_market_times = []
        now_utc = datetime.now(pytz.utc)

        for country_info in self.country_market_data:
            country = country_info["country"]
            timezone_str = country_info["timezone"]
            exchange = country_info["exchange"]
            open_time_str = country_info["open"]
            close_time_str = country_info["close"]
            currency_pair_symbol = country_info["currency_pair"]

            try:
                tz = pytz.timezone(timezone_str)
                current_local_time = now_utc.astimezone(tz)

                # Parse open and close times
                # Assuming market open/close is on current date in that timezone
                open_dt_today = tz.localize(datetime.strptime(f"{current_local_time.date()} {open_time_str}", "%Y-%m-%d %H:%M"))
                close_dt_today = tz.localize(datetime.strptime(f"{current_local_time.date()} {close_time_str}", "%Y-%m-%d %H:%M"))

                # Handle cases where market closes past midnight (e.g., some Asian markets, though rare for main sessions)
                if close_dt_today < open_dt_today:
                    close_dt_today += timedelta(days=1)

                status = "Closed"
                # Check if it's a weekend (Saturday=5, Sunday=6)
                if current_local_time.weekday() in [5, 6]: # Monday is 0, Sunday is 6
                    status = "Closed (Weekend)"
                else:
                    # Calculate pre-market, open, after-hours
                    pre_market_start = open_dt_today - self.pre_market_hours
                    after_hours_end = close_dt_today + self.after_hours

                    if open_dt_today <= current_local_time <= close_dt_today:
                        status = "Open"
                    elif pre_market_start <= current_local_time < open_dt_today:
                        status = "Pre-Market (Scheduled)"
                    elif close_dt_today < current_local_time <= after_hours_end:
                        status = "After-Hours (Scheduled)"
                    else:
                        status = "Closed" # During off-hours, overnight, or before pre-market

                # Fetch currency exchange rate
                currency_rate = "N/A"
                if currency_pair_symbol in self.market_data:
                    currency_rate = f"${self.market_data[currency_pair_symbol][0]:,.4f}"
                elif currency_pair_symbol == "USD": # For US, show current USD value vs itself or major index
                     currency_rate = "N/A (Base)"

                self.global_market_times.append({
                    "country": country,
                    "current_time": current_local_time.strftime("%A, %B %d, %Y %H:%M:%S %Z"),
                    "exchange": exchange,
                    "trading_hours": f"{open_time_str} - {close_time_str}",
                    "status": status,
                    "usd_currency_rate": currency_rate
                })
            except pytz.UnknownTimeZoneError:
                logging.error(f"Unknown timezone: {timezone_str} for {country}. Skipping.")
            except Exception as e:
                logging.error(f"Error processing market time for {country}: {e}")
                self.global_market_times.append({
                    "country": country,
                    "current_time": "N/A",
                    "exchange": exchange,
                    "trading_hours": "N/A",
                    "status": "Error",
                    "usd_currency_rate": "N/A"
                })

    def fetch_historical_data(self, symbol: str, period: str = "1y") -> object:
        """Fetches historical data for a given symbol for charting."""
        logging.info(f"Fetching historical data for {symbol} over period {period}...")
        try:
            # Using interval='1d' for daily data
            data = yf.Ticker(symbol).history(period=period, interval='1d')
            if data.empty:
                logging.warning(f"No historical data found for {symbol} in period {period}.")
            return data
        except Exception as e:
            logging.error(f"Failed to fetch historical data for {symbol}: {e}")
            return None

    # --------------------------------------------------------------------------
    # Subsection 4.2: AI-Powered Analysis and Prediction
    # --------------------------------------------------------------------------

    def analyze_with_gemini(self, prompt: str, use_context: bool = True) -> str:
        """Generic function to send a prompt to the Gemini AI model."""
        if not self.gemini_model:
            return "Error: Gemini AI model is not configured."
        
        full_prompt = prompt
        if use_context:
            # Building a concise context summary of the app's current state
            context_summary = "--- CURRENT APP STATE & CONTEXT ---\n"
            
            # Market Data Summary
            context_summary += "Market Data:\n"
            for k, v in self.market_data.items():
                if v[0]: # If price is not zero
                    context_summary += f"- {k}: ${v[0]:,.2f} ({v[1]:+.2f}% change)\n"
            context_summary += "\n"

            # News Summary (top 5 AI-summarized)
            context_summary += "Recent News Headlines (AI Summarized):\n"
            if self.news_data:
                for i, n in enumerate(self.news_data[:5]):
                    summary_text = n.get('ai_summary', n.get('summary', ''))
                    context_summary += f"- {n['title']} (Source: {n['source']}): {summary_text}\n"
            else:
                context_summary += "No recent news available.\n"
            context_summary += "\n"

            # Speeches Summary (top 3 with impact analysis)
            context_summary += "Key Speeches/Statements (AI Impact Analysis):\n"
            if self.speech_data:
                for s in self.speech_data[:3]:
                    context_summary += f"- {s['title']} (Source: {s['source']})\n  Impact: {s.get('impact_analysis', 'N/A')}\n"
            else:
                context_summary += "No recent speeches available.\n"
            context_summary += "\n"

            # Global Market Times Highlights (top 5 by display order)
            context_summary += "Global Market Times & Status:\n"
            if self.global_market_times:
                for entry in self.global_market_times[:5]:
                    context_summary += f"- {entry.get('country')}: {entry.get('current_time')} | Exchange: {entry.get('exchange')} | Status: {entry.get('status')} | USD Rate: {entry.get('usd_currency_rate')}\n"
            else:
                context_summary += "Global market times not available.\n"
            context_summary += "\n"

            # Previous Gemini Analysis (if available)
            if "Analysis has not been run yet." not in self.gemini_full_analysis and self.predictions:
                context_summary += "Previous Bitcoin Prediction:\n"
                pred = self.predictions[0] # Assuming first prediction is the main one
                context_summary += f"- Direction: {pred['direction']}, Range: {pred['price_range']}, Probability: {pred['probability']}%\n"
            context_summary += "\n"

            full_prompt = context_summary + "\n--- USER PROMPT ---\n" + prompt
        
        try:
            logging.info(f"Sending request to Gemini AI with context. Prompt length: {len(full_prompt)} chars.")
            response = self.gemini_model.generate_content(full_prompt)
            return response.text
        except Exception as e:
            logging.error(f"Gemini API request failed: {e}")
            return f"Gemini AI Error: {str(e)}"

    def generate_main_predictions(self) -> bool:
        """Generates the main Bitcoin price prediction using a detailed prompt for Gemini."""
        logging.info("Generating main predictions with Gemini AI...")
        prompt = f"""
        As an expert financial analyst AI, provide a comprehensive market analysis and Bitcoin price prediction based on the context provided.
        Current Bitcoin Price: ${self.bitcoin_price:,.2f} (24h Change: {self.price_change_24h:.2f}%)
        Structure your response with Markdown headings for:
        1. Overall Market Sentiment (e.g., Bullish, Bearish, Neutral)
        2. Bitcoin Price Prediction (24-48 Hours), including Direction, Price Range, and Probability (%).
        3. Key Drivers & Catalysts (Top 3-5 factors, referencing specific news/speeches/events from context if relevant).
        4. Risk Factors.
        """
        analysis_text = self.analyze_with_gemini(prompt, use_context=True) # Ensure context is used
        self.gemini_full_analysis = analysis_text
        self.parse_gemini_analysis(analysis_text)
        return True
        
    def parse_gemini_analysis(self, analysis_text: str) -> None:
        """Parses the structured Markdown response from Gemini."""
        self.predictions = []
        try:
            direction_match = re.search(r"Direction:\s*\*?([^\*\n]+)", analysis_text, re.IGNORECASE)
            range_match = re.search(r"Price Range:\s*\*?([^\*\n]+)", analysis_text, re.IGNORECASE)
            prob_match = re.search(r"Probability:\s*\*?(\d+)%\*?", analysis_text, re.IGNORECASE)

            direction = direction_match.group(1).strip() if direction_match else "Sideways"
            price_range = range_match.group(1).strip() if range_match else "N/A"
            probability = int(prob_match.group(1)) if prob_match else 50
            
            self.predictions.append({
                'direction': direction, 'price_range': price_range,
                'probability': probability, 'reasons': analysis_text,
                'source': 'Gemini AI Analysis'
            })
        except Exception as e:
            logging.error(f"Error parsing Gemini's analysis text: {e}")

    def ask_chat_gemini(self, user_text: str, chat_history_display_format: list) -> str:
        """
        Handles the interactive chat by sending user queries to Gemini.
        `chat_history_display_format` is the list of messages in display format ("Sender: Message").
        """
        # We need to reconstruct a concise chat history for the AI to understand the flow
        # The AI needs to see who said what clearly.
        concise_chat_history = []
        for entry in chat_history_display_format[-5:]: # Send last 5 turns to keep context relevant but short
            if entry.startswith("You: "):
                concise_chat_history.append(f"User: {entry[5:]}")
            elif entry.startswith("AI: "):
                concise_chat_history.append(f"Assistant: {entry[4:]}")
        
        history_prompt = "\n".join(concise_chat_history)
        
        prompt = f"""
        You are Teu AI, a sophisticated financial analysis assistant.
        You have access to current market data, news, economic events (if available), global market times, and financial speeches.
        You also generate Bitcoin price predictions.
        
        Based on the current app state (provided in context) and the chat history, please respond to the user's query.
        Keep responses concise and directly address the user's question, leveraging the information you 'know'.
        If the user asks about something specific like "the latest news" or "Bitcoin's prediction", refer to the data you have.
        
        Chat History:
        {history_prompt}
        
        User's message: {user_text}
        
        Your response:
        """
        return self.analyze_with_gemini(prompt, use_context=True) # Crucially, use_context=True here

# ==============================================================================
# SECTION 5: GUI - The TeuFinancialApp Class
# ==============================================================================

class TeuFinancialApp:
    """The main GUI application class, built with tkinter."""
    def __init__(self, root_widget: tk.Tk):
        logging.info("Initializing the GUI application.")
        self.root = root_widget
        self.root.title("Teu 8.8 - Unified AI Financial Platform")
        self.root.geometry("1600x950")
        
        self.style = ttk.Style()
        self.style.theme_use('clam')

        self.analyzer = FinancialAnalyzer()
        self.status_var = tk.StringVar(value="Ready. Click 'Refresh All' to begin.")
        self.chat_history_list = [] # Stores history for passing to AI

        self._setup_ui()
        self.refresh_all_threaded() # Auto-refresh on startup
        self._start_clock_update() # Start live clock for global market times

    def _setup_ui(self) -> None:
        """Constructs the main UI layout and widgets."""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Top Control Bar
        control_bar = ttk.Frame(main_frame)
        control_bar.pack(fill=tk.X, pady=(0, 10))
        ttk.Button(control_bar, text="🔄 Refresh All", command=self.refresh_all_threaded).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_bar, text="🤖 Generate AI Analysis", command=self.generate_predictions_threaded).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_bar, text="📊 Export Report", command=self.export_report).pack(side=tk.LEFT, padx=5)
        
        # Main Paned Window
        main_paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        main_paned_window.pack(fill=tk.BOTH, expand=True)

        # --- Left Pane (Data Tabs) ---
        left_pane = ttk.Frame(main_paned_window, padding=5)
        notebook = ttk.Notebook(left_pane)
        notebook.pack(fill=tk.BOTH, expand=True)

        self.tab_market = ttk.Frame(notebook)
        self.tab_news = ttk.Frame(notebook)
        self.tab_speeches = ttk.Frame(notebook)
        self.tab_global_markets = ttk.Frame(notebook) # Renamed from tab_calendar
        self.tab_charts = ttk.Frame(notebook)

        notebook.add(self.tab_market, text="📈 Market")
        notebook.add(self.tab_news, text="📰 News")
        notebook.add(self.tab_speeches, text="🏛️ Speeches")
        notebook.add(self.tab_global_markets, text="🌍 Global Markets") # Renamed text
        notebook.add(self.tab_charts, text="📊 Charts")
        
        self._setup_market_tab()
        self._setup_news_tab()
        self._setup_speeches_tab()
        self._setup_global_markets_tab() # Renamed setup method
        self._setup_charts_tab()
        
        main_paned_window.add(left_pane, weight=1)

        # --- Right Pane (AI Tabs) ---
        right_pane = ttk.PanedWindow(main_paned_window, orient=tk.VERTICAL)
        self._setup_predictions_panel(right_pane)
        self._setup_chat_panel(right_pane)
        main_paned_window.add(right_pane, weight=2)
        
        # Status Bar
        status_bar = ttk.Frame(self.root, relief=tk.SUNKEN, padding=(5, 2))
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        ttk.Label(status_bar, textvariable=self.status_var).pack(fill=tk.X)

    # --- UI Setup Methods for each tab ---

    def _setup_market_tab(self):
        """Sets up the widgets for the Market Data tab."""
        frame = ttk.LabelFrame(self.tab_market, text="Live Prices & Currencies", padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        self.market_labels = {}
        
        # Create a scrollable canvas for market data if there are many entries
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        for i, name in enumerate(self.analyzer.asset_name_map.values()):
            f = ttk.Frame(scrollable_frame)
            f.grid(row=i, column=0, sticky="ew", pady=2)
            ttk.Label(f, text=f"{name}:", font=("Segoe UI", 10, "bold")).pack(side=tk.LEFT)
            value_label = ttk.Label(f, text="Loading...", font=("Segoe UI", 10))
            value_label.pack(side=tk.RIGHT)
            self.market_labels[name] = value_label
        scrollable_frame.grid_columnconfigure(0, weight=1)


    def _setup_news_tab(self):
        """Sets up the scrolled text widget for the News tab."""
        frame = ttk.LabelFrame(self.tab_news, text="Latest Articles", padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        self.news_text = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=("Segoe UI", 9))
        self.news_text.pack(fill=tk.BOTH, expand=True)
        self.news_text.config(state='disabled')
        # Define tags for formatting
        self.news_text.tag_config('title', font=("Segoe UI", 10, "bold"), spacing3=5)
        self.news_text.tag_config('meta', font=("Segoe UI", 8, "italic"), foreground="gray")
        self.news_text.tag_config('summary', font=("Segoe UI", 9), spacing1=2, spacing3=5)

    def _setup_speeches_tab(self):
        """Sets up the scrolled text widget for the Speeches tab with impact analysis."""
        frame = ttk.LabelFrame(self.tab_speeches, text="Official Statements & Impact", padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        self.speech_text = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=("Segoe UI", 9))
        self.speech_text.pack(fill=tk.BOTH, expand=True)
        self.speech_text.config(state='disabled')
        self.speech_text.tag_config('title', font=("Segoe UI", 10, "bold"), spacing3=5)
        self.speech_text.tag_config('source', font=("Segoe UI", 9, "italic"), foreground="darkblue")
        self.speech_text.tag_config('impact', font=("Segoe UI", 9), foreground="purple", spacing1=2, spacing3=5)
        self.speech_text.tag_config('link', foreground="blue", underline=True)


    def _setup_global_markets_tab(self):
        """Sets up the treeview for the Global Markets tab."""
        frame = ttk.LabelFrame(self.tab_global_markets, text="Global Market & Key Times", padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        cols = ('Country', 'Current Local Time', 'Exchange', 'Trading Hours', 'Status', 'USD/Currency Rate')
        self.global_markets_tree = ttk.Treeview(frame, columns=cols, show='headings')
        for col in cols: 
            self.global_markets_tree.heading(col, text=col)
            # Adjust column widths for better readability
            self.global_markets_tree.column(col, width=120, anchor='center')

        self.global_markets_tree.column('Country', width=120, anchor='w')
        self.global_markets_tree.column('Current Local Time', width=200, anchor='w')
        self.global_markets_tree.column('Exchange', width=120, anchor='w')
        self.global_markets_tree.column('Trading Hours', width=120, anchor='center')
        self.global_markets_tree.column('Status', width=150, anchor='center')
        self.global_markets_tree.column('USD/Currency Rate', width=120, anchor='e')

        self.global_markets_tree.pack(fill=tk.BOTH, expand=True)

        # Add a scrollbar
        vsb = ttk.Scrollbar(frame, orient="vertical", command=self.global_markets_tree.yview)
        vsb.pack(side='right', fill='y')
        self.global_markets_tree.configure(yscrollcommand=vsb.set)

    def _setup_charts_tab(self):
        """Sets up the widgets for the new Charting tab."""
        frame = ttk.LabelFrame(self.tab_charts, text="Historical Price Chart", padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        controls = ttk.Frame(frame)
        controls.pack(fill=tk.X, pady=5)
        ttk.Label(controls, text="Select Asset:").pack(side=tk.LEFT)
        self.chart_asset_selector = ttk.Combobox(controls, values=[self.analyzer.asset_name_map[s] for s in self.analyzer.asset_list], state="readonly")
        self.chart_asset_selector.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.chart_asset_selector.set(self.analyzer.asset_name_map["BTC-USD"]) # Set default to Bitcoin
        
        ttk.Label(controls, text="Period:").pack(side=tk.LEFT, padx=(10, 2))
        self.chart_period_selector = ttk.Combobox(controls, values=["1d", "5d", "1mo", "3mo", "6mo", "1y", "2y", "5y", "10y", "ytd", "max"], state="readonly")
        self.chart_period_selector.pack(side=tk.LEFT, padx=5)
        self.chart_period_selector.set("1y") # Default period
        
        ttk.Button(controls, text="Generate Chart", command=self.generate_chart_threaded).pack(side=tk.LEFT)
        
        self.chart_display_frame = ttk.Frame(frame)
        self.chart_display_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self.chart_canvas = None
        self.chart_toolbar = None # To store the matplotlib toolbar

    def _setup_predictions_panel(self, parent):
        """Sets up the panel for displaying AI predictions."""
        frame = ttk.LabelFrame(parent, text="🤖 Gemini AI Analysis & Predictions", padding=10)
        self.pred_text = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=("Segoe UI", 10))
        self.pred_text.pack(fill=tk.BOTH, expand=True)
        self.pred_text.config(state='disabled')
        parent.add(frame, weight=3)

    def _setup_chat_panel(self, parent):
        """Sets up the interactive chat panel."""
        frame = ttk.LabelFrame(parent, text="💬 Chat with Teu AI", padding=10)
        self.chat_display = scrolledtext.ScrolledText(frame, wrap=tk.WORD, height=10, font=("Segoe UI", 10))
        self.chat_display.pack(fill=tk.BOTH, expand=True)
        self.chat_display.tag_config('user', foreground='#0040E0', font=("Segoe UI", 10, "bold"))
        self.chat_display.tag_config('ai', foreground='#006400')
        self.chat_display.config(state='disabled')
        input_frame = ttk.Frame(frame, padding=(0, 5, 0, 0))
        input_frame.pack(fill=tk.X)
        self.chat_input = ttk.Entry(input_frame, font=("Segoe UI", 10))
        self.chat_input.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.chat_input.bind("<Return>", self.handle_chat_threaded)
        ttk.Button(input_frame, text="Send", command=self.handle_chat_threaded).pack(side=tk.RIGHT)
        parent.add(frame, weight=2)
    
    def _start_clock_update(self):
        """Starts a recurring update for the global market times display."""
        # Update every second for live clock effect
        self.update_global_market_times_display() 
        self.root.after(1000, self._start_clock_update)
        
    # --- Threading and Workers ---

    def _run_in_thread(self, target_func, on_complete=None, *args):
        """Generic function to run a task in a background thread."""
        def worker():
            try:
                result = target_func(*args)
                if on_complete: self.root.after(0, on_complete, result)
            except Exception as e:
                logging.error(f"Error in threaded task {target_func.__name__}: {e}", exc_info=True)
                self.root.after(0, lambda: self.status_var.set(f"❌ Error in task: {e}"))
        threading.Thread(target=worker, daemon=True).start()

    def refresh_all_threaded(self):
        self.status_var.set("🔄 Refreshing all data sources...")
        self._run_in_thread(self.analyzer.fetch_all_data, self._on_refresh_complete)

    def _on_refresh_complete(self, success):
        self.update_all_displays()
        self.status_var.set("✅ Data refreshed. Generating AI analysis...")
        self.generate_predictions_threaded()

    def _on_predictions_complete(self, success):
        self.display_predictions()
        self.status_var.set("✅ AI Analysis Complete.")

    def handle_chat_threaded(self, event=None):
        user_text = self.chat_input.get().strip()
        if not user_text: return
        self.add_message_to_chat("You", user_text) # Add user message to display
        self.chat_input.delete(0, tk.END)
        self.status_var.set("🤖 Teu AI is thinking...")
        # Pass the chat_history_list to the analyzer for AI context
        self._run_in_thread(self.analyzer.ask_chat_gemini, self._on_chat_response_complete, user_text, self.chat_history_list)

    def _on_chat_response_complete(self, ai_response):
        self.add_message_to_chat("AI", ai_response) # Add AI response to display
        self.status_var.set("Ready.")
        
    def generate_chart_threaded(self):
        selected_asset_name = self.chart_asset_selector.get()
        # Find the symbol from the name
        symbol = next((s for s, name in self.analyzer.asset_name_map.items() if name == selected_asset_name), None)
        period = self.chart_period_selector.get()
        if not symbol:
            messagebox.showerror("Chart Error", "Please select a valid asset for charting.")
            return
        self.status_var.set(f"📊 Generating chart for {selected_asset_name} ({period})...")
        self._run_in_thread(self.analyzer.fetch_historical_data, self.display_chart, symbol, period)

    # --- UI Display and Update Methods ---

    def update_all_displays(self):
        self.display_market_data()
        self.display_news()
        self.display_speeches()
        # self.display_economic_calendar() # Old call, no longer needed
        self.update_global_market_times_display() # New call

    def _update_scrolled_text(self, widget, content):
        widget.config(state='normal')
        widget.delete(1.0, tk.END)
        widget.insert(tk.END, content)
        widget.config(state='disabled')

    def display_market_data(self):
        for name, label in self.market_labels.items():
            price, change = self.analyzer.market_data.get(name, (0, 0))
            if price and price > 0:
                color = "green" if change >= 0 else "red"
                arrow = '▲' if change >= 0 else '▼'
                # Format for currencies might be slightly different, but this works generally
                label.config(text=f"${price:,.4f} ({arrow} {change:+.2f}%)" if "USD/" in name else f"${price:,.2f} ({arrow} {change:+.2f}%)", foreground=color)
            else:
                label.config(text="N/A", foreground="gray")


    def display_news(self):
        self.news_text.config(state='normal')
        self.news_text.delete(1.0, tk.END)
        
        for article in self.analyzer.news_data:
            title = article.get('title', 'No Title')
            ai_summary = article.get('ai_summary', "No summary available.")
            source = article.get('source', 'Unknown Source')
            published_str = article.get('published', '')
            author = article.get('author', 'Unknown Author')
            link = article.get('link', '#')

            formatted_published = ""
            if published_str:
                try:
                    # NewsAPI dates are ISO format, RSS feeds can vary
                    dt_obj = datetime.fromisoformat(published_str.replace('Z', '+00:00')) # Handle 'Z' for UTC
                    local_tz = pytz.timezone('America/Halifax') # Cole Harbour, Nova Scotia timezone for display
                    dt_local = dt_obj.astimezone(local_tz)
                    formatted_published = dt_local.strftime("%A, %B %d, %Y at %I:%M %p %Z")
                except ValueError:
                    formatted_published = published_str # Fallback if parsing fails

            self.news_text.insert(tk.END, f"{title}\n", 'title')
            self.news_text.insert(tk.END, f"By {author} | {source} | {formatted_published}\n", 'meta')
            self.news_text.insert(tk.END, f"{ai_summary}\n", 'summary')
            self.news_text.insert(tk.END, f"Read more: {link}\n\n", 'link')
            self.news_text.tag_bind('link', '<Button-1>', lambda e, url=link: self._open_url(url))

        self.news_text.config(state='disabled')


    def display_speeches(self):
        self.speech_text.config(state='normal')
        self.speech_text.delete(1.0, tk.END)

        if not self.analyzer.speech_data:
            self.speech_text.insert(tk.END, "No recent impactful speeches found or analyzed.")
        else:
            for speech in self.analyzer.speech_data:
                title = speech.get('title', 'No Title')
                source = speech.get('source', 'Unknown Source')
                link = speech.get('link', '#')
                impact = speech.get('impact_analysis', 'No AI impact analysis available.')
                
                self.speech_text.insert(tk.END, f"{title}\n", 'title')
                self.speech_text.insert(tk.END, f"Source: {source}\n", 'source')
                self.speech_text.insert(tk.END, f"AI Impact: {impact}\n", 'impact')
                self.speech_text.insert(tk.END, f"Full Statement: {link}\n\n", 'link')
                self.speech_text.tag_bind('link', '<Button-1>', lambda e, url=link: self._open_url(url))

        self.speech_text.config(state='disabled')

    def _open_url(self, url):
        """Helper to open URLs in a web browser."""
        import webbrowser
        try:
            webbrowser.open_new_tab(url)
        except Exception as e:
            logging.error(f"Failed to open URL {url}: {e}")
            messagebox.showerror("Browser Error", f"Could not open link: {url}\n\nError: {e}")


    def update_global_market_times_display(self):
        """Updates the Treeview with current global market times and status."""
        for i in self.global_markets_tree.get_children():
            self.global_markets_tree.delete(i)

        # Re-run the calculation every second for live clock effect
        # This is efficient because it's mostly local datetime calculations, not network calls
        self.analyzer.get_global_market_times()
        
        if not self.analyzer.global_market_times:
            self.global_markets_tree.insert('', 'end', values=("N/A", "No market data available.", "N/A", "N/A", "N/A", "N/A"))
            return

        for entry in self.analyzer.global_market_times:
            values = (
                entry.get('country', 'N/A'),
                entry.get('current_time', 'N/A'),
                entry.get('exchange', 'N/A'),
                entry.get('trading_hours', 'N/A'),
                entry.get('status', 'N/A'),
                entry.get('usd_currency_rate', 'N/A')
            )
            self.global_markets_tree.insert('', 'end', values=values)
            
    def display_chart(self, symbol, period): # Now accepts symbol and period
        # Find the actual yfinance symbol from the selected name
        yf_symbol = next((s for s, name in self.analyzer.asset_name_map.items() if name == symbol), symbol) # Ensure it's the ticker
        
        hist_data = self.analyzer.fetch_historical_data(yf_symbol, period)
        
        if hist_data is None or hist_data.empty:
            messagebox.showerror("Chart Error", f"No historical data found for {symbol} for period {period}.")
            self.status_var.set(f"❌ Failed to generate chart for {symbol}.")
            return
        
        # Clear previous chart and toolbar if they exist
        if self.chart_canvas: self.chart_canvas.get_tk_widget().destroy()
        if self.chart_toolbar: self.chart_toolbar.destroy()
        
        fig = Figure(figsize=(10, 5), dpi=100) # Increased size for better view
        plot = fig.add_subplot(1, 1, 1)
        plot.plot(hist_data.index, hist_data['Close'], label=f'{symbol} Close Price')
        plot.set_title(f'Historical Price for {symbol} ({period})')
        plot.set_xlabel("Date")
        plot.set_ylabel("Price (USD)")
        plot.grid(True)
        fig.autofmt_xdate() # Auto-format x-axis dates for better readability
        fig.tight_layout()
        
        self.chart_canvas = FigureCanvasTkAgg(fig, master=self.chart_display_frame)
        self.chart_canvas.draw()
        self.chart_canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        self.chart_toolbar = NavigationToolbar2Tk(self.chart_canvas, self.chart_display_frame)
        self.chart_toolbar.update()
        self.chart_canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        self.status_var.set(f"✅ Chart for {symbol} generated for {period}.")

    def display_predictions(self):
        self._update_scrolled_text(self.pred_text, self.analyzer.gemini_full_analysis)
        
    def add_message_to_chat(self, sender, message):
        tag = 'user' if sender == "You" else 'ai'
        self.chat_display.config(state='normal')
        self.chat_display.insert(tk.END, f"{sender}: {message}\n\n", tag)
        self.chat_display.config(state='disabled')
        self.chat_display.see(tk.END)
        self.chat_history_list.append(f"{sender}: {message}") # Store for AI context
        if len(self.chat_history_list) > 20: self.chat_history_list.pop(0) # Keep history concise


    def export_report(self):
        """Exports the current analysis to a text file."""
        if not self.analyzer.predictions and "Analysis has not been run yet." in self.analyzer.gemini_full_analysis:
            messagebox.showinfo("Export Warning", "No AI analysis has been generated yet to export.")
            return

        filepath = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("Text files", "*.txt"), ("All files", "*.*")])
        if not filepath: return
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"Teu Financial Analysis Report - {datetime.now():%Y-%m-%d %H:%M:%S}\n\n")
                f.write("="*50 + "\n")
                f.write("--- CURRENT MARKET DATA ---\n")
                f.write("="*50 + "\n")
                for name, data in self.analyzer.market_data.items():
                    if data[0]:
                        f.write(f"{name}: ${data[0]:,.2f} ({data[1]:+.2f}%)\n")
                f.write("\n")

                f.write("="*50 + "\n")
                f.write("--- RECENT NEWS HEADLINES (AI Summarized) ---\n")
                f.write("="*50 + "\n")
                for article in self.analyzer.news_data:
                    f.write(f"Title: {article.get('title', 'N/A')}\n")
                    f.write(f"Source: {article.get('source', 'N/A')} | Author: {article.get('author', 'N/A')} | Published: {article.get('published', 'N/A')}\n")
                    f.write(f"Summary: {article.get('ai_summary', 'N/A')}\n")
                    f.write(f"Link: {article.get('link', 'N/A')}\n\n")
                f.write("\n")
                
                f.write("="*50 + "\n")
                f.write("--- KEY SPEECHES & STATEMENTS (AI Impact Analysis) ---\n")
                f.write("="*50 + "\n")
                for speech in self.analyzer.speech_data:
                    f.write(f"Title: {speech.get('title', 'N/A')}\n")
                    f.write(f"Source: {speech.get('source', 'N/A')}\n")
                    f.write(f"AI Impact: {speech.get('impact_analysis', 'N/A')}\n")
                    f.write(f"Link: {speech.get('link', 'N/A')}\n\n")
                f.write("\n")

                f.write("="*50 + "\n")
                f.write("--- GLOBAL MARKET & KEY TIMES ---\n")
                f.write("="*50 + "\n")
                for entry in self.analyzer.global_market_times:
                    f.write(f"Country: {entry.get('country', 'N/A')}\n")
                    f.write(f"Current Local Time: {entry.get('current_time', 'N/A')}\n")
                    f.write(f"Exchange: {entry.get('exchange', 'N/A')}\n")
                    f.write(f"Trading Hours: {entry.get('trading_hours', 'N/A')}\n")
                    f.write(f"Status: {entry.get('status', 'N/A')}\n")
                    f.write(f"USD/Currency Rate: {entry.get('usd_currency_rate', 'N/A')}\n\n")
                f.write("\n")

                f.write("="*50 + "\n")
                f.write("--- GEMINI AI FULL ANALYSIS & PREDICTION ---\n")
                f.write("="*50 + "\n")
                f.write(self.analyzer.gemini_full_analysis)
                f.write("\n")

            messagebox.showinfo("Export Successful", f"Report saved to:\n{filepath}")
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to save report: {e}")
            logging.error(f"Error saving report: {e}", exc_info=True)


# ==============================================================================
# SECTION 6: MAIN EXECUTION BLOCK
# ==============================================================================
if __name__ == "__main__":
    try:
        logging.info("Application starting up...")
        root = tk.Tk()
        app = TeuFinancialApp(root)
        root.mainloop()
        logging.info("Application shutting down.")
    except Exception as e:
        logging.critical(f"A critical error occurred on startup: {e}", exc_info=True)
        messagebox.showerror("Fatal Error", f"The application encountered a fatal error and must close.\n\nDetails: {e}")