# Teu Brain.py - Comprehensive Crypto-Stock Analysis Engine
# Version: 1.0.1 - Final Integration from Brain Files 0-9
# Author: AI Generated from Teu Brain Analysis
# Date: 2025-06-17
#
# DESCRIPTION:
# This is the ultimate comprehensive AI application that combines ALL analysis engines
# from the Teu brain files (0-9) into a unified crypto-stock market analysis system.
# It analyzes the complex interconnected relationships between cryptocurrency and
# stock markets as described in the Vietnamese research document.
#
# COMPREHENSIVE ANALYSIS ENGINES INTEGRATED FROM BRAIN FILES 0-9:
# - CryptoEquityBetaEngine: Calculates beta relationships between crypto and stocks
# - LiquidityFlowEngine: Tracks institutional and retail capital movements
# - RiskSentimentEngine: Generates Risk-On/Risk-Off (RORO) sentiment scores
# - MacroEconomicShockEngine: Simulates macroeconomic event impacts
# - VolatilityContagionEngine: Models volatility spread across asset classes
# - NarrativeAnalysisEngine: Tracks dominant market narratives using NLP
# - LaggedEventImpactEngine: Analyzes delayed market reactions over multiple timeframes
# - RegulatoryImpactEngine: Assesses regulatory impact on markets
# - OnChainAnalyticsEngine: Interprets blockchain data for market insights
# - CrossMarketArbitrageEngine: Identifies pricing inefficiencies across markets
# - GeopoliticalRiskEngine: Integrates geopolitical event analysis
# - BehavioralFinanceEngine: Models cognitive biases and herd mentality
# - BlackSwanMitigationEngine: Identifies extreme tail risks and hedging strategies
# - AIInferenceEngine: Advanced AI-driven predictive analytics and anomaly detection
# - CorrelationMatrixEngine: Dynamic correlation analysis between all assets
# - TechnicalAnalysisEngine: Advanced technical indicators and pattern recognition
# - FundamentalAnalysisEngine: Deep fundamental analysis of crypto and equity assets
# - SentimentAggregationEngine: Aggregates sentiment from multiple sources
# - PortfolioOptimizationEngine: Modern portfolio theory optimization
# - RiskManagementEngine: Comprehensive risk assessment and management
#
# USAGE:
# python "Teu brain.py" --analysis  (Run comprehensive analysis)
# python "Teu brain.py" --test      (Run test mode)
# python "Teu brain.py" --dashboard (Run with analysis dashboard)
# python "Teu brain.py" --backtest  (Run historical backtesting)

import time
import logging
import sys
import threading
import collections
import random
import json
import datetime
import math
import statistics
import itertools
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple, Union
from enum import Enum
import warnings

# Try to import advanced libraries, fallback to basic implementations
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    warnings.warn("NumPy not available, using basic math operations")

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    warnings.warn("Pandas not available, using basic data structures")

try:
    import scipy.stats as stats
    from scipy.optimize import minimize
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    warnings.warn("SciPy not available, using simplified statistical methods")

try:
    import sklearn.cluster as cluster
    from sklearn.decomposition import PCA
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    warnings.warn("Scikit-learn not available, using basic clustering")

# Market Regime Enumeration
class MarketRegime(Enum):
    NORMAL = "normal"
    BULL = "bull"
    BEAR = "bear"
    VOLATILE = "volatile"
    CRISIS = "crisis"
    RECOVERY = "recovery"

# Risk Level Enumeration
class RiskLevel(Enum):
    VERY_LOW = 1
    LOW = 2
    MODERATE = 3
    HIGH = 4
    VERY_HIGH = 5
    EXTREME = 6

# Asset Type Enumeration
class AssetType(Enum):
    CRYPTO = "crypto"
    EQUITY = "equity"
    COMMODITY = "commodity"
    FOREX = "forex"
    BOND = "bond"

# --- Enhanced Configuration ---
@dataclass
class TeuBrainConfig:
    """Comprehensive configuration for Teu Brain Analysis system"""
    # Core System Configuration
    INITIAL_DATA_WAIT: int = 5
    ANALYSIS_INTERVAL: int = 10
    MAX_HISTORY_SIZE: int = 5000
    RISK_THRESHOLD: float = 0.8
    VOLATILITY_THRESHOLD: float = 0.05
    CORRELATION_THRESHOLD: float = 0.7

    # Analysis Windows
    SHORT_WINDOW: int = 10
    MEDIUM_WINDOW: int = 30
    LONG_WINDOW: int = 100
    VOLATILITY_WINDOW: int = 20
    BETA_WINDOW: int = 60
    SENTIMENT_WINDOW: int = 15

    # Risk Management
    MAX_POSITION_SIZE: float = 0.1
    STOP_LOSS_THRESHOLD: float = 0.05
    TAKE_PROFIT_THRESHOLD: float = 0.15
    VAR_CONFIDENCE: float = 0.95

    # Market Regime Detection
    REGIME_LOOKBACK: int = 252  # Trading days in a year
    REGIME_THRESHOLD: float = 0.02

    # Market data storage
    MARKET_DATA: Dict = field(default_factory=lambda: collections.defaultdict(list))
    ANALYSIS_RESULTS: Dict = field(default_factory=dict)

config = TeuBrainConfig()

# --- Enhanced Logging Setup ---
class TeuLogger:
    """Enhanced logging system for Teu Brain"""

    def __init__(self):
        self.setup_logging()
        self.performance_log = collections.deque(maxlen=10000)
        self.error_log = collections.deque(maxlen=1000)
        self.analysis_log = collections.deque(maxlen=5000)

    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s',
            handlers=[
                logging.FileHandler('teu_brain_analysis.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

teu_logger = TeuLogger()
logger = teu_logger.logger

# --- Comprehensive Asset Configuration ---
COMPREHENSIVE_ASSETS = {
    # Major Cryptocurrencies
    "BTC": {
        "type": AssetType.CRYPTO, "data_source": "sim_crypto", "weight": 0.25,
        "sector": "digital_gold", "market_cap_tier": "large", "volatility_tier": "high",
        "correlation_group": "crypto_major", "fundamental_score": 9.5, "liquidity_tier": "very_high",
        "adoption_score": 10.0, "technology_score": 8.5, "regulatory_risk": "medium"
    },
    "ETH": {
        "type": AssetType.CRYPTO, "data_source": "sim_crypto", "weight": 0.20,
        "sector": "smart_contracts", "market_cap_tier": "large", "volatility_tier": "high",
        "correlation_group": "crypto_major", "fundamental_score": 9.0, "liquidity_tier": "very_high",
        "adoption_score": 9.5, "technology_score": 9.8, "regulatory_risk": "medium"
    },
    "SOL": {
        "type": AssetType.CRYPTO, "data_source": "sim_crypto", "weight": 0.08,
        "sector": "high_performance", "market_cap_tier": "medium", "volatility_tier": "very_high",
        "correlation_group": "crypto_alt", "fundamental_score": 8.0, "liquidity_tier": "high",
        "adoption_score": 7.5, "technology_score": 9.0, "regulatory_risk": "low"
    },
    "ADA": {
        "type": AssetType.CRYPTO, "data_source": "sim_crypto", "weight": 0.05,
        "sector": "academic", "market_cap_tier": "medium", "volatility_tier": "high",
        "correlation_group": "crypto_alt", "fundamental_score": 7.5, "liquidity_tier": "medium",
        "adoption_score": 6.5, "technology_score": 8.5, "regulatory_risk": "low"
    },
    "DOT": {
        "type": AssetType.CRYPTO, "data_source": "sim_crypto", "weight": 0.04,
        "sector": "interoperability", "market_cap_tier": "medium", "volatility_tier": "high",
        "correlation_group": "crypto_alt", "fundamental_score": 7.8, "liquidity_tier": "medium",
        "adoption_score": 6.8, "technology_score": 9.2, "regulatory_risk": "low"
    },
    "LINK": {
        "type": AssetType.CRYPTO, "data_source": "sim_crypto", "weight": 0.03,
        "sector": "oracles", "market_cap_tier": "medium", "volatility_tier": "very_high",
        "correlation_group": "crypto_defi", "fundamental_score": 8.2, "liquidity_tier": "medium",
        "adoption_score": 8.0, "technology_score": 8.8, "regulatory_risk": "low"
    },
    "MATIC": {
        "type": AssetType.CRYPTO, "data_source": "sim_crypto", "weight": 0.03,
        "sector": "scaling", "market_cap_tier": "medium", "volatility_tier": "very_high",
        "correlation_group": "crypto_scaling", "fundamental_score": 7.0, "liquidity_tier": "medium",
        "adoption_score": 7.2, "technology_score": 8.0, "regulatory_risk": "low"
    },
    "AVAX": {
        "type": AssetType.CRYPTO, "data_source": "sim_crypto", "weight": 0.02,
        "sector": "smart_contracts", "market_cap_tier": "medium", "volatility_tier": "very_high",
        "correlation_group": "crypto_alt", "fundamental_score": 7.5, "liquidity_tier": "medium",
        "adoption_score": 6.5, "technology_score": 8.5, "regulatory_risk": "low"
    },

    # Crypto-related Equities
    "COIN": {
        "type": AssetType.EQUITY, "data_source": "sim_equity", "crypto_asset": "BTC", "weight": 0.15,
        "sector": "exchange", "market_cap_tier": "large", "volatility_tier": "high",
        "correlation_group": "crypto_equity", "fundamental_score": 7.0, "beta_to_market": 1.8,
        "revenue_growth": 0.25, "profit_margin": 0.15, "regulatory_risk": "high"
    },
    "MSTR": {
        "type": AssetType.EQUITY, "data_source": "sim_equity", "crypto_asset": "BTC", "weight": 0.10,
        "sector": "treasury", "market_cap_tier": "medium", "volatility_tier": "very_high",
        "correlation_group": "crypto_equity", "fundamental_score": 6.5, "beta_to_market": 2.5,
        "revenue_growth": 0.05, "profit_margin": 0.08, "regulatory_risk": "medium"
    },
    "TSLA": {
        "type": AssetType.EQUITY, "data_source": "sim_equity", "crypto_asset": "BTC", "weight": 0.08,
        "sector": "automotive", "market_cap_tier": "large", "volatility_tier": "high",
        "correlation_group": "tech_equity", "fundamental_score": 8.0, "beta_to_market": 1.6,
        "revenue_growth": 0.35, "profit_margin": 0.12, "regulatory_risk": "medium"
    },
    "NVDA": {
        "type": AssetType.EQUITY, "data_source": "sim_equity", "crypto_asset": "ETH", "weight": 0.07,
        "sector": "semiconductors", "market_cap_tier": "large", "volatility_tier": "high",
        "correlation_group": "tech_equity", "fundamental_score": 9.0, "beta_to_market": 1.4,
        "revenue_growth": 0.45, "profit_margin": 0.25, "regulatory_risk": "low"
    },
    "HOOD": {
        "type": AssetType.EQUITY, "data_source": "sim_equity", "crypto_asset": "BTC", "weight": 0.05,
        "sector": "fintech", "market_cap_tier": "small", "volatility_tier": "very_high",
        "correlation_group": "fintech_equity", "fundamental_score": 5.5, "beta_to_market": 2.0,
        "revenue_growth": 0.15, "profit_margin": 0.05, "regulatory_risk": "high"
    },
    "SQ": {
        "type": AssetType.EQUITY, "data_source": "sim_equity", "crypto_asset": "BTC", "weight": 0.04,
        "sector": "payments", "market_cap_tier": "medium", "volatility_tier": "high",
        "correlation_group": "fintech_equity", "fundamental_score": 7.5, "beta_to_market": 1.3,
        "revenue_growth": 0.20, "profit_margin": 0.10, "regulatory_risk": "medium"
    },
    "PYPL": {
        "type": AssetType.EQUITY, "data_source": "sim_equity", "crypto_asset": "BTC", "weight": 0.03,
        "sector": "payments", "market_cap_tier": "large", "volatility_tier": "medium",
        "correlation_group": "fintech_equity", "fundamental_score": 7.8, "beta_to_market": 1.1,
        "revenue_growth": 0.18, "profit_margin": 0.18, "regulatory_risk": "medium"
    }
}

# Update config with comprehensive assets
config.ASSETS_TO_TRACK = COMPREHENSIVE_ASSETS

# --- Abstract Base Classes ---
class Engine(ABC):
    """Abstract base class for all analysis engines"""

    def __init__(self, name: str, priority: int = 5):
        self.name = name
        self.priority = priority  # 1-10, higher is more important
        self.enabled = True
        self.last_run = None
        self.performance_metrics = {}
        self.historical_results = collections.deque(maxlen=1000)
        self.dependencies = []  # Other engines this depends on
        self.outputs = {}  # Current analysis outputs
        logger.info(f"{self.name} initialized with priority {priority}")

    @abstractmethod
    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        """Run the analysis engine"""
        pass

    def get_performance_metrics(self) -> Dict:
        """Get performance metrics for this engine"""
        return self.performance_metrics

    def update_performance_metrics(self, execution_time: float, results_count: int):
        """Update performance metrics"""
        self.performance_metrics.update({
            "last_execution_time": execution_time,
            "results_count": results_count,
            "last_run": datetime.datetime.now().isoformat(),
            "total_runs": self.performance_metrics.get("total_runs", 0) + 1,
            "avg_execution_time": (
                (self.performance_metrics.get("avg_execution_time", 0) *
                 self.performance_metrics.get("total_runs", 0) + execution_time) /
                (self.performance_metrics.get("total_runs", 0) + 1)
            )
        })

    def add_dependency(self, engine_name: str):
        """Add a dependency on another engine"""
        if engine_name not in self.dependencies:
            self.dependencies.append(engine_name)

    def check_dependencies(self, global_analysis: Dict) -> bool:
        """Check if all dependencies are satisfied"""
        for dep in self.dependencies:
            if dep not in global_analysis:
                logger.warning(f"{self.name} dependency {dep} not satisfied")
                return False
        return True

class EventDispatcher:
    """Enhanced event dispatcher for system-wide communication"""

    def __init__(self):
        self._listeners = collections.defaultdict(list)
        self._event_history = collections.deque(maxlen=10000)
        self._event_stats = collections.defaultdict(int)
        self._event_filters = {}

    def subscribe(self, event_type: str, listener, filter_func=None):
        """Subscribe to events with optional filtering"""
        self._listeners[event_type].append((listener, filter_func))
        logger.debug(f"Subscribed listener to {event_type}")

    def dispatch(self, event_type: str, data: Any, metadata: Dict = None):
        """Dispatch event with metadata"""
        timestamp = datetime.datetime.now()
        event_entry = {
            'type': event_type,
            'timestamp': timestamp,
            'data_size': len(str(data)) if data else 0,
            'metadata': metadata or {}
        }
        self._event_history.append(event_entry)
        self._event_stats[event_type] += 1

        for listener, filter_func in self._listeners[event_type]:
            try:
                if filter_func is None or filter_func(data, metadata):
                    listener(data, metadata)
            except Exception as e:
                logger.error(f"Error in event listener for {event_type}: {e}")

    def get_event_history(self, event_type: str = None, limit: int = 100) -> List[Dict]:
        """Get event history with optional filtering"""
        if event_type:
            return [e for e in list(self._event_history)[-limit:] if e['type'] == event_type]
        return list(self._event_history)[-limit:]

    def get_event_stats(self) -> Dict:
        """Get event statistics"""
        return dict(self._event_stats)

# --- Enhanced Data Simulator ---
class ComprehensiveMarketSimulator:
    """Advanced market data simulator with realistic patterns and correlations"""

    def __init__(self, event_dispatcher: EventDispatcher):
        self._event_dispatcher = event_dispatcher
        self._running = False
        self._thread = None
        self._data_counter = 0
        self._market_regime = MarketRegime.NORMAL
        self._correlation_matrix = self._initialize_correlations()
        self._volatility_clusters = self._initialize_volatility_clusters()
        self._trend_components = self._initialize_trend_components()
        self._news_events = collections.deque(maxlen=100)
        self._macro_indicators = self._initialize_macro_indicators()

        # Base prices for realistic simulation
        self._base_prices = {
            "BTC": 45000, "ETH": 3000, "SOL": 100, "ADA": 0.5, "DOT": 7.0,
            "LINK": 15.0, "MATIC": 1.0, "AVAX": 35.0,
            "COIN": 200, "MSTR": 800, "TSLA": 250, "NVDA": 500,
            "HOOD": 15, "SQ": 80, "PYPL": 70
        }

        # Volatility parameters by asset
        self._volatility_params = {
            "BTC": {"base_vol": 0.04, "vol_of_vol": 0.3},
            "ETH": {"base_vol": 0.05, "vol_of_vol": 0.35},
            "SOL": {"base_vol": 0.08, "vol_of_vol": 0.5},
            "ADA": {"base_vol": 0.06, "vol_of_vol": 0.4},
            "DOT": {"base_vol": 0.07, "vol_of_vol": 0.45},
            "LINK": {"base_vol": 0.09, "vol_of_vol": 0.6},
            "MATIC": {"base_vol": 0.10, "vol_of_vol": 0.7},
            "AVAX": {"base_vol": 0.09, "vol_of_vol": 0.6},
            "COIN": {"base_vol": 0.06, "vol_of_vol": 0.4},
            "MSTR": {"base_vol": 0.08, "vol_of_vol": 0.5},
            "TSLA": {"base_vol": 0.05, "vol_of_vol": 0.3},
            "NVDA": {"base_vol": 0.04, "vol_of_vol": 0.25},
            "HOOD": {"base_vol": 0.07, "vol_of_vol": 0.45},
            "SQ": {"base_vol": 0.05, "vol_of_vol": 0.3},
            "PYPL": {"base_vol": 0.04, "vol_of_vol": 0.25}
        }

    def _initialize_correlations(self):
        """Initialize realistic correlation matrix"""
        assets = list(config.ASSETS_TO_TRACK.keys())
        n_assets = len(assets)

        if NUMPY_AVAILABLE:
            corr_matrix = np.eye(n_assets)

            for i, asset1 in enumerate(assets):
                for j, asset2 in enumerate(assets):
                    if i != j:
                        asset1_info = config.ASSETS_TO_TRACK[asset1]
                        asset2_info = config.ASSETS_TO_TRACK[asset2]

                        # Same correlation group = higher correlation
                        if asset1_info.get("correlation_group") == asset2_info.get("correlation_group"):
                            corr_matrix[i, j] = random.uniform(0.7, 0.9)
                        # Same asset type = medium correlation
                        elif asset1_info["type"] == asset2_info["type"]:
                            corr_matrix[i, j] = random.uniform(0.4, 0.7)
                        # Cross-asset = lower correlation
                        else:
                            corr_matrix[i, j] = random.uniform(0.2, 0.5)

            return corr_matrix
        else:
            # Fallback correlation structure
            return {(i, j): random.uniform(0.3, 0.8) for i in range(n_assets) for j in range(n_assets) if i != j}

    def _initialize_volatility_clusters(self):
        """Initialize volatility clustering parameters"""
        return {asset: {
            "current_vol": params["base_vol"],
            "vol_persistence": random.uniform(0.85, 0.95),
            "vol_mean_reversion": random.uniform(0.05, 0.15)
        } for asset, params in self._volatility_params.items()}

    def _initialize_trend_components(self):
        """Initialize trend components for each asset"""
        return {asset: {
            "short_trend": 0.0,
            "medium_trend": 0.0,
            "long_trend": 0.0,
            "trend_strength": random.uniform(0.1, 0.3)
        } for asset in config.ASSETS_TO_TRACK.keys()}

    def _initialize_macro_indicators(self):
        """Initialize macroeconomic indicators"""
        return {
            "interest_rate": 0.05,
            "inflation_rate": 0.03,
            "gdp_growth": 0.025,
            "unemployment_rate": 0.04,
            "vix": 20.0,
            "dollar_index": 100.0,
            "gold_price": 2000.0,
            "oil_price": 80.0
        }

    def _update_market_regime(self):
        """Update market regime based on various factors"""
        # Regime transition probabilities
        transition_probs = {
            MarketRegime.NORMAL: {MarketRegime.BULL: 0.05, MarketRegime.BEAR: 0.03, MarketRegime.VOLATILE: 0.02},
            MarketRegime.BULL: {MarketRegime.NORMAL: 0.08, MarketRegime.VOLATILE: 0.04, MarketRegime.CRISIS: 0.01},
            MarketRegime.BEAR: {MarketRegime.NORMAL: 0.06, MarketRegime.RECOVERY: 0.05, MarketRegime.CRISIS: 0.02},
            MarketRegime.VOLATILE: {MarketRegime.NORMAL: 0.10, MarketRegime.BULL: 0.03, MarketRegime.BEAR: 0.03},
            MarketRegime.CRISIS: {MarketRegime.BEAR: 0.15, MarketRegime.RECOVERY: 0.08},
            MarketRegime.RECOVERY: {MarketRegime.NORMAL: 0.12, MarketRegime.BULL: 0.08}
        }

        current_transitions = transition_probs.get(self._market_regime, {})
        for new_regime, prob in current_transitions.items():
            if random.random() < prob:
                old_regime = self._market_regime
                self._market_regime = new_regime
                logger.info(f"Market regime changed from {old_regime.value} to {new_regime.value}")
                self._event_dispatcher.dispatch("regime_change", {
                    "old_regime": old_regime.value,
                    "new_regime": new_regime.value,
                    "timestamp": datetime.datetime.now()
                })
                break

    def _generate_news_events(self):
        """Generate random news events that affect markets"""
        event_types = [
            "regulatory_announcement", "institutional_adoption", "technical_upgrade",
            "partnership_news", "market_manipulation", "security_breach",
            "economic_data", "central_bank_decision", "geopolitical_event"
        ]

        if random.random() < 0.05:  # 5% chance of news event
            event_type = random.choice(event_types)
            impact_magnitude = random.uniform(-0.1, 0.1)
            affected_assets = random.sample(list(config.ASSETS_TO_TRACK.keys()),
                                          random.randint(1, 5))

            news_event = {
                "type": event_type,
                "impact": impact_magnitude,
                "affected_assets": affected_assets,
                "timestamp": datetime.datetime.now(),
                "duration": random.randint(1, 10)  # Effect duration in cycles
            }

            self._news_events.append(news_event)
            self._event_dispatcher.dispatch("news_event", news_event)
            logger.info(f"News event: {event_type} affecting {affected_assets} with impact {impact_magnitude:.3f}")

    def _update_macro_indicators(self):
        """Update macroeconomic indicators"""
        # Simple random walk with mean reversion for macro indicators
        for indicator, current_value in self._macro_indicators.items():
            if indicator == "interest_rate":
                change = random.gauss(0, 0.001)  # 0.1% daily volatility
                self._macro_indicators[indicator] = max(0, current_value + change)
            elif indicator == "inflation_rate":
                change = random.gauss(0, 0.0005)
                self._macro_indicators[indicator] = max(0, current_value + change)
            elif indicator == "vix":
                change = random.gauss(0, 1.0)
                self._macro_indicators[indicator] = max(5, min(80, current_value + change))
            # Add more macro indicator updates as needed

    def _generate_correlated_returns(self) -> Dict[str, float]:
        """Generate correlated returns with regime-dependent characteristics"""
        assets = list(config.ASSETS_TO_TRACK.keys())
        n_assets = len(assets)

        # Update volatility clustering
        for asset in assets:
            vol_cluster = self._volatility_clusters[asset]
            vol_params = self._volatility_params[asset]

            # GARCH-like volatility updating
            vol_shock = random.gauss(0, vol_params["vol_of_vol"])
            vol_cluster["current_vol"] = (
                vol_cluster["vol_persistence"] * vol_cluster["current_vol"] +
                vol_cluster["vol_mean_reversion"] * vol_params["base_vol"] +
                abs(vol_shock) * 0.1
            )

        if NUMPY_AVAILABLE and isinstance(self._correlation_matrix, np.ndarray):
            # Generate base returns using multivariate normal
            volatilities = [self._volatility_clusters[asset]["current_vol"] for asset in assets]
            cov_matrix = np.outer(volatilities, volatilities) * self._correlation_matrix

            base_returns = np.random.multivariate_normal(np.zeros(n_assets), cov_matrix)
        else:
            # Fallback method
            base_returns = [random.gauss(0, self._volatility_clusters[asset]["current_vol"])
                          for asset in assets]

        # Apply regime effects
        regime_multipliers = {
            MarketRegime.NORMAL: 1.0,
            MarketRegime.BULL: 1.3,
            MarketRegime.BEAR: -0.8,
            MarketRegime.VOLATILE: random.uniform(-1.5, 1.5),
            MarketRegime.CRISIS: -2.0,
            MarketRegime.RECOVERY: 1.8
        }

        regime_mult = regime_multipliers[self._market_regime]

        # Apply trend components
        returns = {}
        for i, asset in enumerate(assets):
            trend_comp = self._trend_components[asset]
            trend_effect = (
                trend_comp["short_trend"] * 0.1 +
                trend_comp["medium_trend"] * 0.05 +
                trend_comp["long_trend"] * 0.02
            ) * trend_comp["trend_strength"]

            # Apply news event effects
            news_effect = 0.0
            for event in self._news_events:
                if asset in event["affected_assets"] and event["duration"] > 0:
                    news_effect += event["impact"] * 0.5
                    event["duration"] -= 1

            returns[asset] = base_returns[i] * regime_mult + trend_effect + news_effect

        # Clean up expired news events
        self._news_events = collections.deque(
            [event for event in self._news_events if event["duration"] > 0],
            maxlen=100
        )

        return returns

    def _generate_data(self):
        """Main data generation loop"""
        while self._running:
            self._data_counter += 1

            # Update market conditions
            self._update_market_regime()
            self._generate_news_events()
            self._update_macro_indicators()

            # Generate correlated returns
            returns = self._generate_correlated_returns()

            # Update prices
            for asset, base_price in self._base_prices.items():
                if asset in returns:
                    # Get last price or use base price
                    last_price = (config.MARKET_DATA[asset][-1]
                                if config.MARKET_DATA[asset] else base_price)

                    # Apply return to get new price
                    new_price = last_price * (1 + returns[asset])
                    new_price = max(new_price, base_price * 0.01)  # Prevent extreme crashes

                    config.MARKET_DATA[asset].append(new_price)

                    # Maintain history size
                    if len(config.MARKET_DATA[asset]) > config.MAX_HISTORY_SIZE:
                        config.MARKET_DATA[asset].pop(0)

            # Add comprehensive market metadata
            config.MARKET_DATA["_metadata"] = {
                "timestamp": datetime.datetime.now().isoformat(),
                "regime": self._market_regime.value,
                "data_counter": self._data_counter,
                "macro_indicators": self._macro_indicators.copy(),
                "active_news_events": len(self._news_events),
                "volatility_levels": {asset: cluster["current_vol"]
                                    for asset, cluster in self._volatility_clusters.items()}
            }

            logger.info(f"Generated data point {self._data_counter} - Regime: {self._market_regime.value}")
            self._event_dispatcher.dispatch("new_market_data", dict(config.MARKET_DATA))
            time.sleep(1)

    def start(self):
        logger.info("Comprehensive Market Simulator starting...")
        self._running = True
        self._thread = threading.Thread(target=self._generate_data)
        self._thread.daemon = True
        self._thread.start()

    def stop(self):
        logger.info("Comprehensive Market Simulator stopping...")
        self._running = False
        if self._thread:
            self._thread.join(timeout=5)
        logger.info("Comprehensive Market Simulator stopped.")

# --- COMPREHENSIVE ANALYSIS ENGINES FROM BRAIN FILES 0-9 ---

class CryptoEquityBetaEngine(Engine):
    """Enhanced crypto-equity beta calculation engine from brain files"""

    def __init__(self):
        super().__init__("CryptoEquityBetaEngine", priority=8)
        self.beta_history = collections.defaultdict(lambda: collections.deque(maxlen=200))
        self.rolling_window = config.BETA_WINDOW
        self.beta_models = {}  # Store different beta models

    def calculate_rolling_beta(self, equity_prices: List[float],
                             crypto_prices: List[float], method: str = "ols") -> Optional[float]:
        """Calculate rolling beta with multiple methods"""
        if len(equity_prices) < self.rolling_window or len(crypto_prices) < self.rolling_window:
            return None

        # Use only the most recent window
        equity_window = equity_prices[-self.rolling_window:]
        crypto_window = crypto_prices[-self.rolling_window:]

        # Calculate log returns for better statistical properties
        if NUMPY_AVAILABLE:
            equity_returns = np.diff(np.log(equity_window))
            crypto_returns = np.diff(np.log(crypto_window))
        else:
            equity_returns = [(equity_window[i] / equity_window[i-1] - 1)
                            for i in range(1, len(equity_window))]
            crypto_returns = [(crypto_window[i] / crypto_window[i-1] - 1)
                            for i in range(1, len(crypto_window))]

        if len(equity_returns) == 0 or len(crypto_returns) == 0:
            return None

        try:
            if method == "ols" and NUMPY_AVAILABLE:
                # Ordinary Least Squares beta
                covariance = np.cov(equity_returns, crypto_returns)[0, 1]
                crypto_variance = np.var(crypto_returns)
                if crypto_variance == 0:
                    return None
                return covariance / crypto_variance

            elif method == "robust" and SCIPY_AVAILABLE:
                # Robust regression beta (less sensitive to outliers)
                from scipy import stats
                slope, _, _, _, _ = stats.linregress(crypto_returns, equity_returns)
                return slope

            else:
                # Fallback method
                if NUMPY_AVAILABLE:
                    covariance = np.cov(equity_returns, crypto_returns)[0, 1]
                    crypto_variance = np.var(crypto_returns)
                else:
                    # Manual covariance calculation
                    equity_mean = sum(equity_returns) / len(equity_returns)
                    crypto_mean = sum(crypto_returns) / len(crypto_returns)

                    covariance = sum((e - equity_mean) * (c - crypto_mean)
                                   for e, c in zip(equity_returns, crypto_returns)) / len(equity_returns)
                    crypto_variance = sum((c - crypto_mean) ** 2 for c in crypto_returns) / len(crypto_returns)

                if crypto_variance == 0:
                    return None
                return covariance / crypto_variance

        except Exception as e:
            logger.error(f"Error calculating beta: {e}")
            return None

    def calculate_conditional_beta(self, equity_prices: List[float],
                                 crypto_prices: List[float], market_regime: str) -> Optional[float]:
        """Calculate beta conditional on market regime"""
        # This would implement regime-dependent beta calculation
        # For now, apply a regime adjustment factor
        base_beta = self.calculate_rolling_beta(equity_prices, crypto_prices)
        if base_beta is None:
            return None

        regime_adjustments = {
            "normal": 1.0,
            "bull": 1.2,
            "bear": 1.5,
            "volatile": 1.8,
            "crisis": 2.5,
            "recovery": 1.1
        }

        return base_beta * regime_adjustments.get(market_regime, 1.0)

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced CryptoEquityBetaEngine...")

        betas = {}
        beta_trends = {}
        conditional_betas = {}
        beta_statistics = {}

        current_regime = market_data.get("_metadata", {}).get("regime", "normal")

        for asset_symbol, asset_config in config.ASSETS_TO_TRACK.items():
            if asset_config["type"] == AssetType.EQUITY and "crypto_asset" in asset_config:
                equity_prices = market_data.get(asset_symbol, [])
                crypto_prices = market_data.get(asset_config["crypto_asset"], [])

                if equity_prices and crypto_prices:
                    # Calculate multiple beta measures
                    beta_ols = self.calculate_rolling_beta(equity_prices, crypto_prices, "ols")
                    beta_robust = self.calculate_rolling_beta(equity_prices, crypto_prices, "robust")
                    beta_conditional = self.calculate_conditional_beta(equity_prices, crypto_prices, current_regime)

                    if beta_ols is not None:
                        beta_key = f"{asset_symbol}_vs_{asset_config['crypto_asset']}"

                        betas[f"{beta_key}_beta_ols"] = beta_ols
                        if beta_robust is not None:
                            betas[f"{beta_key}_beta_robust"] = beta_robust
                        if beta_conditional is not None:
                            conditional_betas[f"{beta_key}_beta_conditional"] = beta_conditional

                        # Store in history for trend analysis
                        self.beta_history[beta_key].append(beta_ols)

                        # Calculate beta statistics
                        if len(self.beta_history[beta_key]) >= 10:
                            recent_betas = list(self.beta_history[beta_key])[-10:]
                            beta_statistics[f"{beta_key}_beta_volatility"] = (
                                statistics.stdev(recent_betas) if len(recent_betas) > 1 else 0
                            )
                            beta_statistics[f"{beta_key}_beta_mean"] = statistics.mean(recent_betas)

                            # Calculate beta trend
                            if len(recent_betas) >= 5:
                                if NUMPY_AVAILABLE:
                                    trend = np.polyfit(range(len(recent_betas)), recent_betas, 1)[0]
                                else:
                                    # Simple linear trend calculation
                                    x_mean = (len(recent_betas) - 1) / 2
                                    y_mean = sum(recent_betas) / len(recent_betas)
                                    numerator = sum((i - x_mean) * (y - y_mean)
                                                  for i, y in enumerate(recent_betas))
                                    denominator = sum((i - x_mean) ** 2 for i in range(len(recent_betas)))
                                    trend = numerator / denominator if denominator != 0 else 0

                                beta_trends[f"{beta_key}_trend"] = trend

                        logger.info(f"Calculated {beta_key}: OLS={beta_ols:.4f}, Conditional={beta_conditional:.4f}")

        # Calculate execution time and update metrics
        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(betas))

        # Store results
        global_analysis["crypto_equity_betas"] = betas
        global_analysis["beta_trends"] = beta_trends
        global_analysis["conditional_betas"] = conditional_betas
        global_analysis["beta_statistics"] = beta_statistics

        return global_analysis

class LiquidityFlowEngine(Engine):
    """Enhanced liquidity flow tracking engine from brain files"""

    def __init__(self):
        super().__init__("LiquidityFlowEngine", priority=7)
        self.institutional_flow_indicator = 0.0
        self.retail_flow_indicator = 0.0
        self.whale_flow_indicator = 0.0
        self.etf_flow_indicator = 0.0
        self.historical_flows = collections.deque(maxlen=config.LONG_WINDOW)
        self.flow_regimes = collections.deque(maxlen=50)

    def _simulate_institutional_flows(self, market_data: Dict) -> float:
        """Simulate institutional capital flows"""
        # Get price changes for institutional proxies
        btc_change = 0
        coin_change = 0

        if len(market_data.get("BTC", [])) > 1:
            btc_prices = market_data["BTC"]
            btc_change = (btc_prices[-1] - btc_prices[-2]) / btc_prices[-2]

        if len(market_data.get("COIN", [])) > 1:
            coin_prices = market_data["COIN"]
            coin_change = (coin_prices[-1] - coin_prices[-2]) / coin_prices[-2]

        # Institutional flows are more stable and trend-following
        institutional_bias = 0.3 if btc_change > 0 and coin_change > 0 else -0.2

        # Add momentum component
        momentum = self.institutional_flow_indicator * 0.15

        # Add volatility-based component (institutions reduce exposure in high vol)
        volatility_penalty = 0
        if "_metadata" in market_data and "volatility_levels" in market_data["_metadata"]:
            btc_vol = market_data["_metadata"]["volatility_levels"].get("BTC", 0.04)
            if btc_vol > 0.06:  # High volatility threshold
                volatility_penalty = -0.5 * (btc_vol - 0.06)

        flow_change = institutional_bias + momentum + volatility_penalty + random.gauss(0, 0.1)
        self.institutional_flow_indicator += flow_change
        self.institutional_flow_indicator = max(-15.0, min(15.0, self.institutional_flow_indicator))

        return self.institutional_flow_indicator

    def _simulate_retail_flows(self, market_data: Dict) -> float:
        """Simulate retail investor flows"""
        # Retail flows are more volatile and sentiment-driven
        btc_change = 0
        if len(market_data.get("BTC", [])) > 1:
            btc_prices = market_data["BTC"]
            btc_change = (btc_prices[-1] - btc_prices[-2]) / btc_prices[-2]

        # Retail shows FOMO/FUD behavior
        if abs(btc_change) > 0.05:  # Large price movement
            retail_bias = 2.0 if btc_change > 0 else -2.5  # Stronger negative reaction
        else:
            retail_bias = 0.5 if btc_change > 0 else -0.3

        # Add herding component
        herding = self.retail_flow_indicator * 0.08

        flow_change = retail_bias + herding + random.gauss(0, 0.3)
        self.retail_flow_indicator += flow_change
        self.retail_flow_indicator = max(-25.0, min(25.0, self.retail_flow_indicator))

        return self.retail_flow_indicator

    def _simulate_whale_flows(self, market_data: Dict) -> float:
        """Simulate whale/large holder flows"""
        # Whales are contrarian and strategic
        btc_change = 0
        if len(market_data.get("BTC", [])) > 10:
            btc_prices = market_data["BTC"]
            # Look at longer-term trend
            short_ma = sum(btc_prices[-5:]) / 5
            long_ma = sum(btc_prices[-20:]) / 20 if len(btc_prices) >= 20 else short_ma
            btc_change = (short_ma - long_ma) / long_ma if long_ma > 0 else 0

        # Whales are contrarian - buy when others sell
        contrarian_signal = -self.retail_flow_indicator * 0.1

        # Strategic accumulation during dips
        if btc_change < -0.1:  # Significant dip
            accumulation_signal = 1.5
        elif btc_change > 0.15:  # Significant pump
            accumulation_signal = -1.0  # Take profits
        else:
            accumulation_signal = 0

        flow_change = contrarian_signal + accumulation_signal + random.gauss(0, 0.2)
        self.whale_flow_indicator += flow_change
        self.whale_flow_indicator = max(-10.0, min(10.0, self.whale_flow_indicator))

        return self.whale_flow_indicator

    def _detect_flow_regime(self, flows: Dict) -> str:
        """Detect current liquidity flow regime"""
        institutional = flows["institutional"]
        retail = flows["retail"]
        whale = flows["whale"]

        if institutional > 5 and retail > 5:
            return "euphoria"
        elif institutional < -5 and retail < -10:
            return "panic"
        elif whale > 3 and retail < -5:
            return "accumulation"
        elif institutional > 2 and whale < -2:
            return "distribution"
        elif abs(institutional) < 2 and abs(retail) < 3:
            return "consolidation"
        else:
            return "mixed"

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced LiquidityFlowEngine...")

        # Calculate all flow types
        institutional_flow = self._simulate_institutional_flows(market_data)
        retail_flow = self._simulate_retail_flows(market_data)
        whale_flow = self._simulate_whale_flows(market_data)

        current_flows = {
            "institutional": institutional_flow,
            "retail": retail_flow,
            "whale": whale_flow,
            "net_flow": institutional_flow + retail_flow + whale_flow
        }

        # Detect flow regime
        flow_regime = self._detect_flow_regime(current_flows)
        self.flow_regimes.append(flow_regime)

        # Store historical data
        self.historical_flows.append(current_flows.copy())

        # Calculate flow statistics
        flow_statistics = {}
        if len(self.historical_flows) >= 10:
            recent_flows = list(self.historical_flows)[-10:]

            for flow_type in ["institutional", "retail", "whale", "net_flow"]:
                values = [f[flow_type] for f in recent_flows]
                flow_statistics[f"{flow_type}_mean"] = statistics.mean(values)
                flow_statistics[f"{flow_type}_volatility"] = statistics.stdev(values) if len(values) > 1 else 0
                flow_statistics[f"{flow_type}_trend"] = (values[-1] - values[0]) / len(values) if values[0] != 0 else 0

        # Calculate flow divergences (important for market timing)
        flow_divergences = {}
        if len(self.historical_flows) >= 5:
            recent_institutional = [f["institutional"] for f in list(self.historical_flows)[-5:]]
            recent_retail = [f["retail"] for f in list(self.historical_flows)[-5:]]

            inst_trend = (recent_institutional[-1] - recent_institutional[0]) / len(recent_institutional)
            retail_trend = (recent_retail[-1] - recent_retail[0]) / len(recent_retail)

            flow_divergences["institutional_retail_divergence"] = inst_trend - retail_trend
            flow_divergences["divergence_strength"] = abs(inst_trend - retail_trend)

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(current_flows))

        global_analysis["liquidity_flows"] = {
            "current": current_flows,
            "regime": flow_regime,
            "statistics": flow_statistics,
            "divergences": flow_divergences,
            "regime_history": list(self.flow_regimes)[-10:]  # Last 10 regimes
        }

        logger.info(f"Liquidity Flows - Regime: {flow_regime}, Institutional: {institutional_flow:.2f}, "
                   f"Retail: {retail_flow:.2f}, Whale: {whale_flow:.2f}")

        return global_analysis

class RiskSentimentEngine(Engine):
    """Enhanced Risk-On/Risk-Off sentiment engine from brain files"""

    def __init__(self):
        super().__init__("RiskSentimentEngine", priority=9)
        self.current_roro_score = 0.0  # -1 (risk-off) to 1 (risk-on)
        self.historical_roro_scores = collections.deque(maxlen=config.LONG_WINDOW)
        self.sentiment_components = {
            "price_momentum": 0.0,
            "volatility_sentiment": 0.0,
            "correlation_sentiment": 0.0,
            "flow_sentiment": 0.0,
            "macro_sentiment": 0.0
        }
        self.sentiment_weights = {
            "price_momentum": 0.3,
            "volatility_sentiment": 0.25,
            "correlation_sentiment": 0.2,
            "flow_sentiment": 0.15,
            "macro_sentiment": 0.1
        }

    def _calculate_price_momentum_sentiment(self, market_data: Dict) -> float:
        """Calculate sentiment based on price momentum across assets"""
        momentum_scores = []

        for asset, asset_config in config.ASSETS_TO_TRACK.items():
            prices = market_data.get(asset, [])
            if len(prices) >= config.SHORT_WINDOW:
                # Calculate short-term momentum
                recent_prices = prices[-config.SHORT_WINDOW:]
                momentum = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]

                # Weight by asset importance
                weighted_momentum = momentum * asset_config.get("weight", 0.01)
                momentum_scores.append(weighted_momentum)

        if momentum_scores:
            avg_momentum = sum(momentum_scores) / len(momentum_scores)
            # Normalize to -1 to 1 range
            return max(-1.0, min(1.0, avg_momentum * 20))  # Scale factor
        return 0.0

    def _calculate_volatility_sentiment(self, market_data: Dict) -> float:
        """Calculate sentiment based on volatility patterns"""
        if "_metadata" not in market_data or "volatility_levels" not in market_data["_metadata"]:
            return 0.0

        volatility_levels = market_data["_metadata"]["volatility_levels"]

        # High volatility generally indicates risk-off sentiment
        avg_volatility = sum(volatility_levels.values()) / len(volatility_levels)

        # Normalize volatility to sentiment score
        # Low vol (< 0.03) = risk-on, High vol (> 0.08) = risk-off
        if avg_volatility < 0.03:
            return 0.5  # Risk-on
        elif avg_volatility > 0.08:
            return -0.8  # Risk-off
        else:
            # Linear interpolation between thresholds
            return 0.5 - (avg_volatility - 0.03) / (0.08 - 0.03) * 1.3

    def _calculate_correlation_sentiment(self, market_data: Dict) -> float:
        """Calculate sentiment based on asset correlations"""
        # High correlations during stress = risk-off
        # Low correlations during normal times = risk-on

        assets = [asset for asset in config.ASSETS_TO_TRACK.keys() if asset != "_metadata"]
        if len(assets) < 3:
            return 0.0

        # Calculate recent correlations
        correlations = []
        window = min(config.MEDIUM_WINDOW, min(len(market_data.get(asset, [])) for asset in assets))

        if window < 10:
            return 0.0

        for i, asset1 in enumerate(assets):
            for j, asset2 in enumerate(assets[i+1:], i+1):
                prices1 = market_data[asset1][-window:]
                prices2 = market_data[asset2][-window:]

                if len(prices1) == len(prices2) and len(prices1) >= 10:
                    # Calculate correlation of returns
                    returns1 = [(prices1[k] / prices1[k-1] - 1) for k in range(1, len(prices1))]
                    returns2 = [(prices2[k] / prices2[k-1] - 1) for k in range(1, len(prices2))]

                    if NUMPY_AVAILABLE:
                        corr = np.corrcoef(returns1, returns2)[0, 1]
                        if not np.isnan(corr):
                            correlations.append(abs(corr))
                    else:
                        # Manual correlation calculation
                        mean1 = sum(returns1) / len(returns1)
                        mean2 = sum(returns2) / len(returns2)

                        numerator = sum((r1 - mean1) * (r2 - mean2) for r1, r2 in zip(returns1, returns2))
                        denom1 = sum((r1 - mean1) ** 2 for r1 in returns1) ** 0.5
                        denom2 = sum((r2 - mean2) ** 2 for r2 in returns2) ** 0.5

                        if denom1 > 0 and denom2 > 0:
                            corr = numerator / (denom1 * denom2)
                            correlations.append(abs(corr))

        if correlations:
            avg_correlation = sum(correlations) / len(correlations)
            # High correlation (> 0.7) = risk-off, Low correlation (< 0.3) = risk-on
            if avg_correlation > 0.7:
                return -0.6  # Risk-off
            elif avg_correlation < 0.3:
                return 0.4   # Risk-on
            else:
                return 0.4 - (avg_correlation - 0.3) / (0.7 - 0.3) * 1.0

        return 0.0

    def _calculate_flow_sentiment(self, global_analysis: Dict) -> float:
        """Calculate sentiment based on liquidity flows"""
        if "liquidity_flows" not in global_analysis:
            return 0.0

        flows = global_analysis["liquidity_flows"]["current"]
        regime = global_analysis["liquidity_flows"]["regime"]

        # Map flow regimes to sentiment
        regime_sentiment = {
            "euphoria": 0.8,
            "accumulation": 0.4,
            "consolidation": 0.0,
            "mixed": 0.0,
            "distribution": -0.3,
            "panic": -0.9
        }

        base_sentiment = regime_sentiment.get(regime, 0.0)

        # Adjust based on net flow magnitude
        net_flow = flows.get("net_flow", 0)
        flow_adjustment = max(-0.3, min(0.3, net_flow / 20))  # Normalize

        return base_sentiment + flow_adjustment

    def _calculate_macro_sentiment(self, market_data: Dict) -> float:
        """Calculate sentiment based on macro indicators"""
        if "_metadata" not in market_data or "macro_indicators" not in market_data["_metadata"]:
            return 0.0

        macro = market_data["_metadata"]["macro_indicators"]

        # VIX-like indicator (higher = more risk-off)
        vix_sentiment = -0.5 if macro.get("vix", 20) > 30 else 0.2

        # Interest rate sentiment (higher rates = risk-off for risk assets)
        rate_sentiment = -0.3 if macro.get("interest_rate", 0.05) > 0.06 else 0.1

        # Combine macro factors
        return (vix_sentiment + rate_sentiment) / 2

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced RiskSentimentEngine...")

        # Calculate all sentiment components
        self.sentiment_components["price_momentum"] = self._calculate_price_momentum_sentiment(market_data)
        self.sentiment_components["volatility_sentiment"] = self._calculate_volatility_sentiment(market_data)
        self.sentiment_components["correlation_sentiment"] = self._calculate_correlation_sentiment(market_data)
        self.sentiment_components["flow_sentiment"] = self._calculate_flow_sentiment(global_analysis)
        self.sentiment_components["macro_sentiment"] = self._calculate_macro_sentiment(market_data)

        # Calculate weighted RORO score
        new_roro_score = sum(
            component * self.sentiment_weights[name]
            for name, component in self.sentiment_components.items()
        )

        # Apply smoothing to reduce noise
        smoothing_factor = 0.3
        self.current_roro_score = (
            (1 - smoothing_factor) * self.current_roro_score +
            smoothing_factor * new_roro_score
        )

        # Ensure bounds
        self.current_roro_score = max(-1.0, min(1.0, self.current_roro_score))

        # Store in history
        self.historical_roro_scores.append(self.current_roro_score)

        # Calculate RORO statistics
        roro_statistics = {}
        if len(self.historical_roro_scores) >= 10:
            recent_scores = list(self.historical_roro_scores)[-10:]
            roro_statistics["mean"] = statistics.mean(recent_scores)
            roro_statistics["volatility"] = statistics.stdev(recent_scores) if len(recent_scores) > 1 else 0
            roro_statistics["trend"] = (recent_scores[-1] - recent_scores[0]) / len(recent_scores)

            # Regime classification
            if self.current_roro_score > 0.5:
                roro_regime = "strong_risk_on"
            elif self.current_roro_score > 0.1:
                roro_regime = "mild_risk_on"
            elif self.current_roro_score > -0.1:
                roro_regime = "neutral"
            elif self.current_roro_score > -0.5:
                roro_regime = "mild_risk_off"
            else:
                roro_regime = "strong_risk_off"

            roro_statistics["regime"] = roro_regime

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, 1)

        global_analysis["risk_sentiment"] = {
            "current_roro_score": self.current_roro_score,
            "components": self.sentiment_components.copy(),
            "statistics": roro_statistics,
            "historical_scores": list(self.historical_roro_scores)[-20:]  # Last 20 scores
        }

        logger.info(f"Risk Sentiment - RORO Score: {self.current_roro_score:.4f}, "
                   f"Regime: {roro_statistics.get('regime', 'unknown')}")

        return global_analysis

class MacroEconomicShockEngine(Engine):
    """Enhanced macroeconomic shock simulation engine from brain files"""

    def __init__(self):
        super().__init__("MacroEconomicShockEngine", priority=6)
        self.macro_impact_score = 0.0
        self.historical_macro_impacts = collections.deque(maxlen=config.LONG_WINDOW)
        self.shock_history = collections.deque(maxlen=50)
        self.shock_types = [
            "interest_rate_shock", "inflation_shock", "gdp_shock", "employment_shock",
            "currency_shock", "commodity_shock", "geopolitical_shock", "financial_crisis",
            "central_bank_policy", "trade_war", "pandemic_shock", "tech_disruption"
        ]
        self.shock_persistence = {}

    def _generate_macro_shock(self, market_data: Dict) -> Dict:
        """Generate various types of macroeconomic shocks"""
        shock_event = None

        # Base probability of shock occurrence
        base_shock_prob = 0.05

        # Increase probability during volatile periods
        if "_metadata" in market_data and "volatility_levels" in market_data["_metadata"]:
            avg_vol = sum(market_data["_metadata"]["volatility_levels"].values()) / len(market_data["_metadata"]["volatility_levels"])
            if avg_vol > 0.06:
                base_shock_prob *= 1.5

        if random.random() < base_shock_prob:
            shock_type = random.choice(self.shock_types)

            # Different shock types have different characteristics
            shock_params = {
                "interest_rate_shock": {"magnitude": random.uniform(-0.3, 0.5), "persistence": random.randint(5, 20)},
                "inflation_shock": {"magnitude": random.uniform(-0.2, 0.6), "persistence": random.randint(10, 30)},
                "gdp_shock": {"magnitude": random.uniform(-0.8, 0.4), "persistence": random.randint(15, 40)},
                "employment_shock": {"magnitude": random.uniform(-0.4, 0.3), "persistence": random.randint(8, 25)},
                "currency_shock": {"magnitude": random.uniform(-0.6, 0.6), "persistence": random.randint(3, 15)},
                "commodity_shock": {"magnitude": random.uniform(-0.5, 0.7), "persistence": random.randint(5, 20)},
                "geopolitical_shock": {"magnitude": random.uniform(-1.0, 0.2), "persistence": random.randint(2, 30)},
                "financial_crisis": {"magnitude": random.uniform(-2.0, -0.5), "persistence": random.randint(20, 60)},
                "central_bank_policy": {"magnitude": random.uniform(-0.4, 0.4), "persistence": random.randint(10, 25)},
                "trade_war": {"magnitude": random.uniform(-0.8, -0.1), "persistence": random.randint(15, 50)},
                "pandemic_shock": {"magnitude": random.uniform(-1.5, -0.3), "persistence": random.randint(30, 100)},
                "tech_disruption": {"magnitude": random.uniform(-0.3, 0.8), "persistence": random.randint(5, 40)}
            }

            params = shock_params.get(shock_type, {"magnitude": random.uniform(-0.5, 0.5), "persistence": random.randint(5, 20)})

            shock_event = {
                "type": shock_type,
                "magnitude": params["magnitude"],
                "persistence": params["persistence"],
                "timestamp": datetime.datetime.now(),
                "affected_sectors": self._get_affected_sectors(shock_type)
            }

            # Store shock for persistence tracking
            self.shock_persistence[shock_type] = {
                "magnitude": params["magnitude"],
                "remaining_periods": params["persistence"]
            }

            logger.info(f"Macro shock generated: {shock_type} with magnitude {params['magnitude']:.3f}")

        return shock_event

    def _get_affected_sectors(self, shock_type: str) -> List[str]:
        """Get sectors affected by different shock types"""
        sector_mapping = {
            "interest_rate_shock": ["fintech", "treasury", "exchange"],
            "inflation_shock": ["all"],
            "gdp_shock": ["all"],
            "employment_shock": ["fintech", "automotive", "semiconductors"],
            "currency_shock": ["exchange", "payments"],
            "commodity_shock": ["automotive", "semiconductors"],
            "geopolitical_shock": ["all"],
            "financial_crisis": ["all"],
            "central_bank_policy": ["fintech", "treasury", "exchange"],
            "trade_war": ["automotive", "semiconductors", "payments"],
            "pandemic_shock": ["all"],
            "tech_disruption": ["semiconductors", "smart_contracts", "high_performance"]
        }

        return sector_mapping.get(shock_type, ["all"])

    def _calculate_shock_impact(self, market_data: Dict) -> float:
        """Calculate current macro shock impact"""
        total_impact = 0.0

        # Apply persistent shocks
        expired_shocks = []
        for shock_type, shock_data in self.shock_persistence.items():
            if shock_data["remaining_periods"] > 0:
                # Decay factor for shock impact over time
                decay_factor = shock_data["remaining_periods"] / 20  # Normalize
                current_impact = shock_data["magnitude"] * decay_factor
                total_impact += current_impact

                shock_data["remaining_periods"] -= 1
                if shock_data["remaining_periods"] <= 0:
                    expired_shocks.append(shock_type)

        # Remove expired shocks
        for shock_type in expired_shocks:
            del self.shock_persistence[shock_type]

        # Add market volatility-based impact
        if "_metadata" in market_data and "volatility_levels" in market_data["_metadata"]:
            vol_levels = market_data["_metadata"]["volatility_levels"]
            avg_vol = sum(vol_levels.values()) / len(vol_levels)

            # High volatility suggests macro uncertainty
            if avg_vol > 0.08:
                volatility_impact = -(avg_vol - 0.08) * 5  # Negative impact
                total_impact += volatility_impact

        return total_impact

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced MacroEconomicShockEngine...")

        # Generate new shock if applicable
        new_shock = self._generate_macro_shock(market_data)
        if new_shock:
            self.shock_history.append(new_shock)

        # Calculate current impact
        current_impact = self._calculate_shock_impact(market_data)

        # Update macro impact score with persistence
        self.macro_impact_score = (
            self.macro_impact_score * 0.9 +  # Persistence
            current_impact * 0.1  # New impact
        )

        # Bound the impact score
        self.macro_impact_score = max(-5.0, min(5.0, self.macro_impact_score))

        # Store in history
        self.historical_macro_impacts.append(self.macro_impact_score)

        # Calculate macro statistics
        macro_statistics = {}
        if len(self.historical_macro_impacts) >= 10:
            recent_impacts = list(self.historical_macro_impacts)[-10:]
            macro_statistics["mean_impact"] = statistics.mean(recent_impacts)
            macro_statistics["impact_volatility"] = statistics.stdev(recent_impacts) if len(recent_impacts) > 1 else 0
            macro_statistics["impact_trend"] = (recent_impacts[-1] - recent_impacts[0]) / len(recent_impacts)

        # Active shocks summary
        active_shocks = {
            shock_type: {
                "magnitude": data["magnitude"],
                "remaining_periods": data["remaining_periods"]
            }
            for shock_type, data in self.shock_persistence.items()
        }

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(active_shocks) + 1)

        global_analysis["macro_economic_shock"] = {
            "current_impact_score": self.macro_impact_score,
            "new_shock": new_shock,
            "active_shocks": active_shocks,
            "statistics": macro_statistics,
            "shock_history": list(self.shock_history)[-10:]  # Last 10 shocks
        }

        logger.info(f"Macro Impact Score: {self.macro_impact_score:.4f}, Active Shocks: {len(active_shocks)}")

        return global_analysis

class VolatilityContagionEngine(Engine):
    """Enhanced volatility contagion modeling engine from brain files"""

    def __init__(self):
        super().__init__("VolatilityContagionEngine", priority=7)
        self.historical_volatilities = collections.defaultdict(lambda: collections.deque(maxlen=config.LONG_WINDOW))
        self.contagion_scores = {}
        self.volatility_regimes = collections.deque(maxlen=50)
        self.contagion_networks = {}

    def _calculate_realized_volatility(self, prices: List[float], window: int = 20) -> float:
        """Calculate realized volatility using various methods"""
        if len(prices) < window + 1:
            return 0.0

        recent_prices = prices[-window-1:]

        if NUMPY_AVAILABLE:
            # Log returns method
            log_returns = np.diff(np.log(recent_prices))
            return np.std(log_returns) * np.sqrt(252)  # Annualized
        else:
            # Simple returns method
            returns = [(recent_prices[i] / recent_prices[i-1] - 1) for i in range(1, len(recent_prices))]
            if not returns:
                return 0.0

            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            return (variance ** 0.5) * (252 ** 0.5)  # Annualized

    def _detect_volatility_regime(self, volatilities: Dict[str, float]) -> str:
        """Detect current volatility regime"""
        if not volatilities:
            return "unknown"

        avg_vol = sum(volatilities.values()) / len(volatilities)
        max_vol = max(volatilities.values())
        vol_dispersion = statistics.stdev(volatilities.values()) if len(volatilities) > 1 else 0

        if avg_vol > 0.15:
            return "extreme_volatility"
        elif avg_vol > 0.10:
            return "high_volatility"
        elif avg_vol > 0.06:
            return "elevated_volatility"
        elif vol_dispersion > 0.03:
            return "mixed_volatility"
        else:
            return "low_volatility"

    def _calculate_volatility_contagion(self, current_volatilities: Dict[str, float]) -> Dict[str, float]:
        """Calculate volatility contagion between asset classes"""
        contagion_measures = {}

        # Group assets by type
        crypto_vols = []
        equity_vols = []

        for asset, vol in current_volatilities.items():
            asset_config = config.ASSETS_TO_TRACK.get(asset, {})
            if asset_config.get("type") == AssetType.CRYPTO:
                crypto_vols.append(vol)
            elif asset_config.get("type") == AssetType.EQUITY:
                equity_vols.append(vol)

        if crypto_vols and equity_vols:
            avg_crypto_vol = sum(crypto_vols) / len(crypto_vols)
            avg_equity_vol = sum(equity_vols) / len(equity_vols)

            # Store historical averages for trend analysis
            self.historical_volatilities["crypto_avg"].append(avg_crypto_vol)
            self.historical_volatilities["equity_avg"].append(avg_equity_vol)

            # Calculate contagion indicators
            if len(self.historical_volatilities["crypto_avg"]) >= 5:
                crypto_history = list(self.historical_volatilities["crypto_avg"])[-5:]
                equity_history = list(self.historical_volatilities["equity_avg"])[-5:]

                # Volatility correlation
                if NUMPY_AVAILABLE:
                    vol_correlation = np.corrcoef(crypto_history, equity_history)[0, 1]
                    if not np.isnan(vol_correlation):
                        contagion_measures["volatility_correlation"] = vol_correlation

                # Volatility spillover (Granger-like causality proxy)
                crypto_change = crypto_history[-1] - crypto_history[-2] if len(crypto_history) >= 2 else 0
                equity_change = equity_history[-1] - equity_history[-2] if len(equity_history) >= 2 else 0

                # Simple spillover measure
                if abs(crypto_change) > 0.01 and abs(equity_change) > 0.005:
                    spillover_strength = min(abs(crypto_change), abs(equity_change)) / max(abs(crypto_change), abs(equity_change))
                    contagion_measures["spillover_strength"] = spillover_strength

                # Volatility regime synchronization
                crypto_regime = "high" if avg_crypto_vol > 0.08 else "low"
                equity_regime = "high" if avg_equity_vol > 0.06 else "low"
                contagion_measures["regime_synchronization"] = 1.0 if crypto_regime == equity_regime else 0.0

        return contagion_measures

    def _build_contagion_network(self, current_volatilities: Dict[str, float]) -> Dict:
        """Build volatility contagion network between individual assets"""
        network = {}
        assets = list(current_volatilities.keys())

        for i, asset1 in enumerate(assets):
            for asset2 in assets[i+1:]:
                # Calculate volatility correlation between asset pairs
                vol_history1 = list(self.historical_volatilities[asset1])
                vol_history2 = list(self.historical_volatilities[asset2])

                if len(vol_history1) >= 10 and len(vol_history2) >= 10:
                    # Use recent history for correlation
                    recent_vol1 = vol_history1[-10:]
                    recent_vol2 = vol_history2[-10:]

                    if NUMPY_AVAILABLE:
                        correlation = np.corrcoef(recent_vol1, recent_vol2)[0, 1]
                        if not np.isnan(correlation):
                            network[f"{asset1}_{asset2}"] = {
                                "correlation": correlation,
                                "strength": abs(correlation),
                                "direction": "positive" if correlation > 0 else "negative"
                            }

        return network

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced VolatilityContagionEngine...")

        current_volatilities = {}

        # Calculate volatilities for all assets
        for asset_symbol in config.ASSETS_TO_TRACK.keys():
            prices = market_data.get(asset_symbol, [])
            if len(prices) > config.VOLATILITY_WINDOW:
                vol = self._calculate_realized_volatility(prices, config.VOLATILITY_WINDOW)
                current_volatilities[asset_symbol] = vol
                self.historical_volatilities[asset_symbol].append(vol)

        # Detect volatility regime
        vol_regime = self._detect_volatility_regime(current_volatilities)
        self.volatility_regimes.append(vol_regime)

        # Calculate contagion measures
        contagion_measures = self._calculate_volatility_contagion(current_volatilities)

        # Build contagion network
        contagion_network = self._build_contagion_network(current_volatilities)

        # Calculate volatility statistics
        vol_statistics = {}
        if current_volatilities:
            vol_values = list(current_volatilities.values())
            vol_statistics["mean_volatility"] = statistics.mean(vol_values)
            vol_statistics["max_volatility"] = max(vol_values)
            vol_statistics["min_volatility"] = min(vol_values)
            vol_statistics["volatility_dispersion"] = statistics.stdev(vol_values) if len(vol_values) > 1 else 0

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(current_volatilities))

        global_analysis["volatility_contagion"] = {
            "current_volatilities": current_volatilities,
            "volatility_regime": vol_regime,
            "contagion_measures": contagion_measures,
            "contagion_network": contagion_network,
            "statistics": vol_statistics,
            "regime_history": list(self.volatility_regimes)[-10:]
        }

        logger.info(f"Volatility Regime: {vol_regime}, Mean Vol: {vol_statistics.get('mean_volatility', 0):.4f}")

        return global_analysis

class NarrativeAnalysisEngine(Engine):
    """Enhanced narrative analysis engine for tracking market narratives from brain files"""

    def __init__(self):
        super().__init__("NarrativeAnalysisEngine", priority=5)
        self.active_narratives = {}
        self.narrative_history = collections.deque(maxlen=100)
        self.narrative_strength = {}
        self.narrative_templates = {
            "ai_crypto_boom": {
                "keywords": ["ai", "artificial intelligence", "machine learning", "nvidia", "tech"],
                "affected_assets": ["NVDA", "ETH", "SOL"],
                "sentiment_bias": 0.3
            },
            "institutional_adoption": {
                "keywords": ["institutional", "etf", "bitcoin etf", "corporate treasury"],
                "affected_assets": ["BTC", "MSTR", "COIN"],
                "sentiment_bias": 0.4
            },
            "defi_innovation": {
                "keywords": ["defi", "decentralized finance", "yield", "liquidity"],
                "affected_assets": ["ETH", "LINK", "UNI"],
                "sentiment_bias": 0.2
            },
            "regulatory_crackdown": {
                "keywords": ["regulation", "sec", "ban", "compliance"],
                "affected_assets": ["COIN", "HOOD", "BTC", "ETH"],
                "sentiment_bias": -0.5
            },
            "scaling_solutions": {
                "keywords": ["layer 2", "scaling", "ethereum 2.0", "polygon"],
                "affected_assets": ["ETH", "MATIC", "SOL"],
                "sentiment_bias": 0.25
            },
            "macro_uncertainty": {
                "keywords": ["inflation", "fed", "interest rates", "recession"],
                "affected_assets": ["BTC", "ETH", "TSLA", "NVDA"],
                "sentiment_bias": -0.3
            },
            "web3_gaming": {
                "keywords": ["gaming", "nft", "metaverse", "web3"],
                "affected_assets": ["ETH", "SOL", "MATIC"],
                "sentiment_bias": 0.15
            }
        }

    def _generate_narrative_events(self, market_data: Dict) -> List[Dict]:
        """Generate narrative events based on market conditions"""
        events = []

        # Simulate narrative emergence based on market conditions
        if random.random() < 0.1:  # 10% chance of narrative event
            narrative_type = random.choice(list(self.narrative_templates.keys()))
            template = self.narrative_templates[narrative_type]

            # Narrative strength depends on market conditions
            base_strength = random.uniform(0.3, 0.8)

            # Adjust strength based on affected asset performance
            affected_performance = []
            for asset in template["affected_assets"]:
                if asset in market_data and len(market_data[asset]) >= 5:
                    prices = market_data[asset]
                    recent_return = (prices[-1] - prices[-5]) / prices[-5]
                    affected_performance.append(recent_return)

            if affected_performance:
                avg_performance = sum(affected_performance) / len(affected_performance)
                # Strong positive performance amplifies positive narratives
                if template["sentiment_bias"] > 0 and avg_performance > 0.05:
                    base_strength *= 1.5
                elif template["sentiment_bias"] < 0 and avg_performance < -0.05:
                    base_strength *= 1.3

            event = {
                "type": narrative_type,
                "strength": min(1.0, base_strength),
                "sentiment_bias": template["sentiment_bias"],
                "affected_assets": template["affected_assets"],
                "timestamp": datetime.datetime.now(),
                "duration": random.randint(10, 50)  # Narrative persistence
            }

            events.append(event)
            logger.info(f"Narrative event: {narrative_type} with strength {base_strength:.3f}")

        return events

    def _update_narrative_strength(self, market_data: Dict):
        """Update strength of active narratives based on market evidence"""
        for narrative_id, narrative in list(self.active_narratives.items()):
            # Decay narrative strength over time
            narrative["strength"] *= 0.98
            narrative["duration"] -= 1

            # Reinforce narrative based on affected asset performance
            affected_assets = narrative["affected_assets"]
            reinforcement = 0.0

            for asset in affected_assets:
                if asset in market_data and len(market_data[asset]) >= 2:
                    prices = market_data[asset]
                    recent_return = (prices[-1] - prices[-2]) / prices[-2]

                    # Positive returns reinforce positive narratives
                    if narrative["sentiment_bias"] > 0 and recent_return > 0:
                        reinforcement += 0.02
                    elif narrative["sentiment_bias"] < 0 and recent_return < 0:
                        reinforcement += 0.02

            narrative["strength"] = min(1.0, narrative["strength"] + reinforcement)

            # Remove weak or expired narratives
            if narrative["strength"] < 0.1 or narrative["duration"] <= 0:
                del self.active_narratives[narrative_id]

    def _calculate_narrative_impact(self, asset: str) -> float:
        """Calculate total narrative impact on a specific asset"""
        total_impact = 0.0

        for narrative in self.active_narratives.values():
            if asset in narrative["affected_assets"]:
                impact = narrative["strength"] * narrative["sentiment_bias"]
                total_impact += impact

        return max(-1.0, min(1.0, total_impact))

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced NarrativeAnalysisEngine...")

        # Generate new narrative events
        new_events = self._generate_narrative_events(market_data)

        # Add new narratives to active list
        for event in new_events:
            narrative_id = f"{event['type']}_{int(event['timestamp'].timestamp())}"
            self.active_narratives[narrative_id] = event

        # Update existing narrative strengths
        self._update_narrative_strength(market_data)

        # Calculate narrative impacts for all assets
        narrative_impacts = {}
        for asset in config.ASSETS_TO_TRACK.keys():
            impact = self._calculate_narrative_impact(asset)
            if abs(impact) > 0.01:  # Only store significant impacts
                narrative_impacts[asset] = impact

        # Calculate narrative statistics
        narrative_stats = {
            "active_narrative_count": len(self.active_narratives),
            "strongest_narrative": None,
            "dominant_sentiment": 0.0
        }

        if self.active_narratives:
            strongest = max(self.active_narratives.values(), key=lambda x: x["strength"])
            narrative_stats["strongest_narrative"] = {
                "type": strongest["type"],
                "strength": strongest["strength"],
                "sentiment": strongest["sentiment_bias"]
            }

            # Calculate overall narrative sentiment
            total_sentiment = sum(n["strength"] * n["sentiment_bias"] for n in self.active_narratives.values())
            total_strength = sum(n["strength"] for n in self.active_narratives.values())
            narrative_stats["dominant_sentiment"] = total_sentiment / total_strength if total_strength > 0 else 0.0

        # Store narrative snapshot
        narrative_snapshot = {
            "timestamp": datetime.datetime.now(),
            "active_narratives": len(self.active_narratives),
            "dominant_sentiment": narrative_stats["dominant_sentiment"]
        }
        self.narrative_history.append(narrative_snapshot)

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(self.active_narratives))

        global_analysis["narrative_analysis"] = {
            "active_narratives": {k: {
                "type": v["type"],
                "strength": v["strength"],
                "sentiment_bias": v["sentiment_bias"],
                "affected_assets": v["affected_assets"]
            } for k, v in self.active_narratives.items()},
            "narrative_impacts": narrative_impacts,
            "statistics": narrative_stats,
            "history": list(self.narrative_history)[-10:]
        }

        logger.info(f"Active Narratives: {len(self.active_narratives)}, "
                   f"Dominant Sentiment: {narrative_stats['dominant_sentiment']:.3f}")

        return global_analysis

class LaggedEventImpactEngine(Engine):
    """Enhanced lagged event impact analysis engine from brain files"""

    def __init__(self):
        super().__init__("LaggedEventImpactEngine", priority=6)
        self.event_queue = collections.deque(maxlen=200)
        self.impact_windows = [1, 6, 24, 72, 168]  # 1h, 6h, 1d, 3d, 1w in hours
        self.event_impacts = collections.defaultdict(lambda: collections.deque(maxlen=100))

    def _register_market_event(self, market_data: Dict, global_analysis: Dict):
        """Register significant market events for lagged impact analysis"""
        events = []

        # Price-based events
        for asset, prices in market_data.items():
            if asset == "_metadata" or len(prices) < 2:
                continue

            recent_return = (prices[-1] - prices[-2]) / prices[-2]

            # Significant price movements
            if abs(recent_return) > 0.1:  # 10% move
                events.append({
                    "type": "large_price_move",
                    "asset": asset,
                    "magnitude": recent_return,
                    "timestamp": datetime.datetime.now(),
                    "expected_impacts": self.impact_windows.copy()
                })

        # Volatility events
        if "volatility_contagion" in global_analysis:
            vol_data = global_analysis["volatility_contagion"]
            if vol_data.get("volatility_regime") in ["extreme_volatility", "high_volatility"]:
                events.append({
                    "type": "volatility_spike",
                    "asset": "market_wide",
                    "magnitude": vol_data["statistics"].get("mean_volatility", 0),
                    "timestamp": datetime.datetime.now(),
                    "expected_impacts": self.impact_windows.copy()
                })

        # Narrative events
        if "narrative_analysis" in global_analysis:
            narrative_data = global_analysis["narrative_analysis"]
            if abs(narrative_data["statistics"].get("dominant_sentiment", 0)) > 0.3:
                events.append({
                    "type": "narrative_shift",
                    "asset": "market_wide",
                    "magnitude": narrative_data["statistics"]["dominant_sentiment"],
                    "timestamp": datetime.datetime.now(),
                    "expected_impacts": self.impact_windows.copy()
                })

        # Macro events
        if "macro_economic_shock" in global_analysis:
            macro_data = global_analysis["macro_economic_shock"]
            if macro_data.get("new_shock"):
                events.append({
                    "type": "macro_shock",
                    "asset": "market_wide",
                    "magnitude": macro_data["new_shock"]["magnitude"],
                    "timestamp": datetime.datetime.now(),
                    "expected_impacts": self.impact_windows.copy()
                })

        # Add events to queue
        for event in events:
            self.event_queue.append(event)
            logger.info(f"Registered lagged event: {event['type']} for {event['asset']}")

        return events

    def _analyze_lagged_impacts(self, market_data: Dict) -> Dict:
        """Analyze lagged impacts of past events"""
        current_time = datetime.datetime.now()
        lagged_impacts = {}

        # Process events in queue
        active_events = []
        for event in self.event_queue:
            time_elapsed = (current_time - event["timestamp"]).total_seconds() / 3600  # Hours

            # Check if any impact windows are still active
            remaining_windows = [w for w in event["expected_impacts"] if time_elapsed < w]

            if remaining_windows:
                event["expected_impacts"] = remaining_windows
                active_events.append(event)

                # Calculate current impact based on time decay
                for window in self.impact_windows:
                    if time_elapsed <= window:
                        # Exponential decay function
                        decay_factor = math.exp(-time_elapsed / (window * 0.3))
                        current_impact = event["magnitude"] * decay_factor

                        impact_key = f"{event['type']}_{event['asset']}_{window}h"
                        lagged_impacts[impact_key] = current_impact

                        # Store for historical analysis
                        self.event_impacts[impact_key].append({
                            "impact": current_impact,
                            "time_elapsed": time_elapsed,
                            "original_magnitude": event["magnitude"]
                        })

        # Update event queue with only active events
        self.event_queue.clear()
        self.event_queue.extend(active_events)

        return lagged_impacts

    def _calculate_impact_persistence(self) -> Dict:
        """Calculate how persistent different types of impacts are"""
        persistence_metrics = {}

        for impact_type, impact_history in self.event_impacts.items():
            if len(impact_history) >= 5:
                impacts = [h["impact"] for h in impact_history]
                times = [h["time_elapsed"] for h in impact_history]

                # Calculate half-life of impact
                if NUMPY_AVAILABLE:
                    # Fit exponential decay
                    try:
                        log_impacts = np.log(np.abs(impacts) + 1e-10)
                        slope, _ = np.polyfit(times, log_impacts, 1)
                        half_life = -np.log(2) / slope if slope < 0 else float('inf')
                        persistence_metrics[impact_type] = {
                            "half_life_hours": half_life,
                            "decay_rate": -slope,
                            "sample_size": len(impacts)
                        }
                    except:
                        pass

        return persistence_metrics

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced LaggedEventImpactEngine...")

        # Register new events
        new_events = self._register_market_event(market_data, global_analysis)

        # Analyze lagged impacts
        lagged_impacts = self._analyze_lagged_impacts(market_data)

        # Calculate persistence metrics
        persistence_metrics = self._calculate_impact_persistence()

        # Calculate summary statistics
        impact_summary = {
            "active_events": len(self.event_queue),
            "total_lagged_impacts": len(lagged_impacts),
            "strongest_lagged_impact": max(lagged_impacts.values()) if lagged_impacts else 0,
            "net_lagged_impact": sum(lagged_impacts.values()) if lagged_impacts else 0
        }

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(lagged_impacts))

        global_analysis["lagged_event_impact"] = {
            "new_events": new_events,
            "active_lagged_impacts": lagged_impacts,
            "persistence_metrics": persistence_metrics,
            "summary": impact_summary
        }

        logger.info(f"Lagged Events: {len(self.event_queue)} active, "
                   f"Net Impact: {impact_summary['net_lagged_impact']:.4f}")

        return global_analysis

class OnChainAnalyticsEngine(Engine):
    """Enhanced on-chain analytics simulation engine from brain files"""

    def __init__(self):
        super().__init__("OnChainAnalyticsEngine", priority=6)
        self.on_chain_metrics = {}
        self.whale_activity = collections.deque(maxlen=100)
        self.network_health = collections.deque(maxlen=100)
        self.defi_metrics = collections.deque(maxlen=100)

    def _simulate_whale_activity(self, market_data: Dict) -> Dict:
        """Simulate whale wallet activity"""
        whale_metrics = {}

        for asset in ["BTC", "ETH", "SOL"]:
            if asset in market_data and len(market_data[asset]) >= 2:
                price_change = (market_data[asset][-1] - market_data[asset][-2]) / market_data[asset][-2]

                # Simulate whale accumulation/distribution
                if price_change < -0.05:  # Price drop
                    accumulation_score = random.uniform(0.6, 0.9)  # Whales buy dips
                    distribution_score = random.uniform(0.1, 0.3)
                elif price_change > 0.1:  # Price pump
                    accumulation_score = random.uniform(0.1, 0.4)
                    distribution_score = random.uniform(0.6, 0.8)  # Whales take profits
                else:
                    accumulation_score = random.uniform(0.3, 0.7)
                    distribution_score = random.uniform(0.3, 0.7)

                # Simulate exchange flows
                exchange_inflow = random.uniform(0, 1000) * (1 + abs(price_change) * 10)
                exchange_outflow = random.uniform(0, 1000) * (1 + abs(price_change) * 8)
                net_flow = exchange_outflow - exchange_inflow

                whale_metrics[asset] = {
                    "accumulation_score": accumulation_score,
                    "distribution_score": distribution_score,
                    "net_exchange_flow": net_flow,
                    "large_transaction_count": random.randint(5, 50),
                    "whale_concentration": random.uniform(0.3, 0.8)
                }

        return whale_metrics

    def _simulate_network_health(self, market_data: Dict) -> Dict:
        """Simulate blockchain network health metrics"""
        network_metrics = {}

        for asset in ["BTC", "ETH", "SOL"]:
            if asset in market_data:
                base_activity = 1.0

                # Network activity correlates with price action
                if len(market_data[asset]) >= 5:
                    recent_volatility = statistics.stdev(market_data[asset][-5:]) / market_data[asset][-1]
                    base_activity *= (1 + recent_volatility * 5)

                if asset == "BTC":
                    network_metrics[asset] = {
                        "hash_rate": random.uniform(300, 500) * base_activity,  # EH/s
                        "difficulty": random.uniform(40, 60) * base_activity,   # T
                        "mempool_size": random.randint(50, 200) * int(base_activity),  # MB
                        "avg_fee": random.uniform(10, 100) * base_activity,     # USD
                        "active_addresses": random.randint(800000, 1200000) * int(base_activity)
                    }
                elif asset == "ETH":
                    network_metrics[asset] = {
                        "gas_price": random.uniform(20, 200) * base_activity,   # Gwei
                        "network_utilization": random.uniform(0.7, 0.95),
                        "staking_ratio": random.uniform(0.15, 0.25),
                        "defi_tvl": random.uniform(30, 80) * base_activity,     # Billion USD
                        "active_addresses": random.randint(400000, 800000) * int(base_activity)
                    }
                elif asset == "SOL":
                    network_metrics[asset] = {
                        "tps": random.uniform(2000, 5000) * base_activity,
                        "validator_count": random.randint(1800, 2200),
                        "staking_ratio": random.uniform(0.7, 0.8),
                        "avg_fee": random.uniform(0.0001, 0.001),  # SOL
                        "active_addresses": random.randint(100000, 300000) * int(base_activity)
                    }

        return network_metrics

    def _simulate_defi_metrics(self, market_data: Dict) -> Dict:
        """Simulate DeFi ecosystem metrics"""
        defi_metrics = {}

        # Total Value Locked simulation
        base_tvl = 50.0  # Billion USD

        # TVL correlates with ETH price
        if "ETH" in market_data and len(market_data["ETH"]) >= 2:
            eth_change = (market_data["ETH"][-1] - market_data["ETH"][-2]) / market_data["ETH"][-2]
            tvl_multiplier = 1 + eth_change * 2  # TVL is more volatile than ETH price
        else:
            tvl_multiplier = 1.0

        current_tvl = base_tvl * tvl_multiplier * random.uniform(0.9, 1.1)

        defi_metrics = {
            "total_tvl": current_tvl,
            "dex_volume_24h": random.uniform(1, 10) * (current_tvl / base_tvl),  # Billion USD
            "lending_utilization": random.uniform(0.6, 0.9),
            "yield_farming_apy": random.uniform(0.05, 0.3),
            "liquidation_volume": random.uniform(10, 500),  # Million USD
            "new_protocol_launches": random.randint(0, 5),
            "governance_proposals": random.randint(10, 50)
        }

        return defi_metrics

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced OnChainAnalyticsEngine...")

        # Simulate various on-chain metrics
        whale_activity = self._simulate_whale_activity(market_data)
        network_health = self._simulate_network_health(market_data)
        defi_metrics = self._simulate_defi_metrics(market_data)

        # Store historical data
        self.whale_activity.append(whale_activity)
        self.network_health.append(network_health)
        self.defi_metrics.append(defi_metrics)

        # Calculate on-chain sentiment
        on_chain_sentiment = 0.0
        sentiment_factors = 0

        # Whale sentiment
        for asset, metrics in whale_activity.items():
            accumulation = metrics["accumulation_score"]
            distribution = metrics["distribution_score"]
            whale_sentiment = accumulation - distribution
            on_chain_sentiment += whale_sentiment
            sentiment_factors += 1

        # Network health sentiment
        for asset, metrics in network_health.items():
            if asset == "ETH":
                # High gas prices = negative sentiment
                gas_sentiment = -0.5 if metrics["gas_price"] > 100 else 0.2
                on_chain_sentiment += gas_sentiment
                sentiment_factors += 1

        # DeFi sentiment
        tvl_change = 0
        if len(self.defi_metrics) >= 2:
            current_tvl = defi_metrics["total_tvl"]
            previous_tvl = list(self.defi_metrics)[-1]["total_tvl"]
            tvl_change = (current_tvl - previous_tvl) / previous_tvl
            defi_sentiment = max(-0.5, min(0.5, tvl_change * 5))
            on_chain_sentiment += defi_sentiment
            sentiment_factors += 1

        # Normalize sentiment
        if sentiment_factors > 0:
            on_chain_sentiment /= sentiment_factors

        # Calculate on-chain statistics
        on_chain_stats = {
            "overall_sentiment": on_chain_sentiment,
            "whale_activity_level": sum(
                metrics.get("large_transaction_count", 0)
                for metrics in whale_activity.values()
            ),
            "network_congestion": any(
                metrics.get("network_utilization", 0) > 0.9
                for metrics in network_health.values()
            ),
            "defi_growth": tvl_change
        }

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(whale_activity) + len(network_health))

        global_analysis["on_chain_analytics"] = {
            "whale_activity": whale_activity,
            "network_health": network_health,
            "defi_metrics": defi_metrics,
            "statistics": on_chain_stats
        }

        logger.info(f"On-Chain Sentiment: {on_chain_sentiment:.4f}, "
                   f"DeFi TVL: ${defi_metrics['total_tvl']:.1f}B")

        return global_analysis

class CrossMarketArbitrageEngine(Engine):
    """Enhanced cross-market arbitrage detection engine from brain files"""

    def __init__(self):
        super().__init__("CrossMarketArbitrageEngine", priority=5)
        self.arbitrage_opportunities = collections.deque(maxlen=100)
        self.price_discrepancies = {}
        self.arbitrage_history = collections.defaultdict(lambda: collections.deque(maxlen=50))

    def _detect_crypto_equity_arbitrage(self, market_data: Dict) -> List[Dict]:
        """Detect arbitrage opportunities between crypto and crypto-exposed equities"""
        opportunities = []

        # Check BTC vs BTC-exposed equities
        btc_price = market_data.get("BTC", [])
        if not btc_price:
            return opportunities

        current_btc = btc_price[-1]

        # Check against MSTR (MicroStrategy - BTC treasury company)
        mstr_price = market_data.get("MSTR", [])
        if mstr_price and len(mstr_price) >= 2:
            current_mstr = mstr_price[-1]

            # Calculate implied BTC value from MSTR
            # Simplified model: MSTR should trade at premium/discount to BTC holdings
            estimated_btc_per_mstr_share = 0.5  # Simplified assumption
            implied_btc_value = current_mstr / estimated_btc_per_mstr_share

            discrepancy = (implied_btc_value - current_btc) / current_btc

            if abs(discrepancy) > 0.05:  # 5% threshold
                opportunities.append({
                    "type": "crypto_equity_arbitrage",
                    "asset_pair": "BTC_MSTR",
                    "discrepancy": discrepancy,
                    "direction": "long_btc_short_mstr" if discrepancy < 0 else "short_btc_long_mstr",
                    "magnitude": abs(discrepancy),
                    "confidence": min(0.9, abs(discrepancy) * 10)
                })

        # Check COIN vs crypto market performance
        coin_price = market_data.get("COIN", [])
        if coin_price and len(coin_price) >= 5:
            # Calculate crypto market performance
            crypto_assets = ["BTC", "ETH", "SOL"]
            crypto_performance = []

            for asset in crypto_assets:
                if asset in market_data and len(market_data[asset]) >= 5:
                    prices = market_data[asset]
                    performance = (prices[-1] - prices[-5]) / prices[-5]
                    crypto_performance.append(performance)

            if crypto_performance:
                avg_crypto_performance = sum(crypto_performance) / len(crypto_performance)
                coin_performance = (coin_price[-1] - coin_price[-5]) / coin_price[-5]

                performance_gap = coin_performance - avg_crypto_performance * 1.5  # COIN should be more volatile

                if abs(performance_gap) > 0.1:  # 10% threshold
                    opportunities.append({
                        "type": "exchange_crypto_arbitrage",
                        "asset_pair": "COIN_CRYPTO_BASKET",
                        "discrepancy": performance_gap,
                        "direction": "long_coin" if performance_gap < 0 else "short_coin",
                        "magnitude": abs(performance_gap),
                        "confidence": min(0.8, abs(performance_gap) * 5)
                    })

        return opportunities

    def _detect_sector_arbitrage(self, market_data: Dict) -> List[Dict]:
        """Detect arbitrage within sectors"""
        opportunities = []

        # Group assets by sector
        sectors = {}
        for asset, config_data in config.ASSETS_TO_TRACK.items():
            sector = config_data.get("sector", "unknown")
            if sector not in sectors:
                sectors[sector] = []
            sectors[sector].append(asset)

        # Check for sector arbitrage
        for sector, assets in sectors.items():
            if len(assets) < 2:
                continue

            # Calculate sector performance
            sector_performances = []
            for asset in assets:
                if asset in market_data and len(market_data[asset]) >= 3:
                    prices = market_data[asset]
                    performance = (prices[-1] - prices[-3]) / prices[-3]
                    sector_performances.append((asset, performance))

            if len(sector_performances) >= 2:
                # Find outliers
                performances = [p[1] for p in sector_performances]
                mean_performance = sum(performances) / len(performances)

                for asset, performance in sector_performances:
                    deviation = performance - mean_performance
                    if abs(deviation) > 0.08:  # 8% deviation threshold
                        opportunities.append({
                            "type": "sector_arbitrage",
                            "asset_pair": f"{asset}_{sector}_SECTOR",
                            "discrepancy": deviation,
                            "direction": "short_asset" if deviation > 0 else "long_asset",
                            "magnitude": abs(deviation),
                            "confidence": min(0.7, abs(deviation) * 8)
                        })

        return opportunities

    def _calculate_arbitrage_persistence(self) -> Dict:
        """Calculate how persistent arbitrage opportunities are"""
        persistence_metrics = {}

        for arb_type, history in self.arbitrage_history.items():
            if len(history) >= 5:
                # Calculate average duration and magnitude
                durations = []
                magnitudes = []

                for opportunity in history:
                    durations.append(opportunity.get("duration", 1))
                    magnitudes.append(opportunity["magnitude"])

                persistence_metrics[arb_type] = {
                    "avg_duration": sum(durations) / len(durations),
                    "avg_magnitude": sum(magnitudes) / len(magnitudes),
                    "frequency": len(history),
                    "success_rate": sum(1 for opp in history if opp.get("profitable", False)) / len(history)
                }

        return persistence_metrics

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced CrossMarketArbitrageEngine...")

        # Detect various arbitrage opportunities
        crypto_equity_arb = self._detect_crypto_equity_arbitrage(market_data)
        sector_arb = self._detect_sector_arbitrage(market_data)

        all_opportunities = crypto_equity_arb + sector_arb

        # Store opportunities
        for opportunity in all_opportunities:
            opportunity["timestamp"] = datetime.datetime.now()
            opportunity["duration"] = 1  # Will be updated as opportunity persists
            self.arbitrage_opportunities.append(opportunity)

            # Add to type-specific history
            arb_type = opportunity["type"]
            self.arbitrage_history[arb_type].append(opportunity)

        # Calculate persistence metrics
        persistence_metrics = self._calculate_arbitrage_persistence()

        # Calculate arbitrage statistics
        arbitrage_stats = {
            "total_opportunities": len(all_opportunities),
            "avg_magnitude": sum(opp["magnitude"] for opp in all_opportunities) / len(all_opportunities) if all_opportunities else 0,
            "high_confidence_count": sum(1 for opp in all_opportunities if opp["confidence"] > 0.7),
            "most_frequent_type": max(self.arbitrage_history.keys(),
                                    key=lambda x: len(self.arbitrage_history[x])) if self.arbitrage_history else None
        }

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(all_opportunities))

        global_analysis["cross_market_arbitrage"] = {
            "current_opportunities": all_opportunities,
            "persistence_metrics": persistence_metrics,
            "statistics": arbitrage_stats,
            "opportunity_history": list(self.arbitrage_opportunities)[-20:]
        }

        logger.info(f"Arbitrage Opportunities: {len(all_opportunities)}, "
                   f"High Confidence: {arbitrage_stats['high_confidence_count']}")

        return global_analysis

class BehavioralFinanceEngine(Engine):
    """Enhanced behavioral finance modeling engine from brain files"""

    def __init__(self):
        super().__init__("BehavioralFinanceEngine", priority=6)
        self.behavioral_indicators = {}
        self.crowd_sentiment = 0.0
        self.fear_greed_index = 50.0  # 0-100 scale
        self.behavioral_biases = {
            "herding": 0.0,
            "overconfidence": 0.0,
            "loss_aversion": 0.0,
            "anchoring": 0.0,
            "confirmation_bias": 0.0,
            "fomo": 0.0,
            "fud": 0.0
        }
        self.behavioral_history = collections.deque(maxlen=100)

    def _calculate_herding_behavior(self, market_data: Dict) -> float:
        """Calculate herding behavior indicator"""
        # Herding manifests as high correlation during extreme moves
        if len(config.ASSETS_TO_TRACK) < 3:
            return 0.0

        # Calculate cross-asset correlations during recent period
        assets = [asset for asset in config.ASSETS_TO_TRACK.keys() if asset != "_metadata"]
        recent_correlations = []

        window = min(10, min(len(market_data.get(asset, [])) for asset in assets))
        if window < 5:
            return 0.0

        for i, asset1 in enumerate(assets):
            for asset2 in assets[i+1:]:
                prices1 = market_data.get(asset1, [])[-window:]
                prices2 = market_data.get(asset2, [])[-window:]

                if len(prices1) == len(prices2) and len(prices1) >= 5:
                    returns1 = [(prices1[j] / prices1[j-1] - 1) for j in range(1, len(prices1))]
                    returns2 = [(prices2[j] / prices2[j-1] - 1) for j in range(1, len(prices2))]

                    if NUMPY_AVAILABLE:
                        corr = np.corrcoef(returns1, returns2)[0, 1]
                        if not np.isnan(corr):
                            recent_correlations.append(abs(corr))

        if recent_correlations:
            avg_correlation = sum(recent_correlations) / len(recent_correlations)
            # High correlation suggests herding
            return min(1.0, avg_correlation * 1.5)

        return 0.0

    def _calculate_overconfidence(self, market_data: Dict, global_analysis: Dict) -> float:
        """Calculate overconfidence bias indicator"""
        # Overconfidence manifests as high trading volume during bull markets
        overconfidence = 0.0

        # Check if we're in a bull market
        bull_market_indicator = 0
        for asset in ["BTC", "ETH"]:
            if asset in market_data and len(market_data[asset]) >= 20:
                prices = market_data[asset]
                short_ma = sum(prices[-5:]) / 5
                long_ma = sum(prices[-20:]) / 20
                if short_ma > long_ma * 1.05:  # 5% above long-term average
                    bull_market_indicator += 1

        if bull_market_indicator >= 1:
            # In bull markets, overconfidence increases
            overconfidence = 0.6

            # Amplify if we have strong positive sentiment
            if "risk_sentiment" in global_analysis:
                roro_score = global_analysis["risk_sentiment"].get("current_roro_score", 0)
                if roro_score > 0.3:
                    overconfidence = min(1.0, overconfidence + roro_score * 0.5)

        return overconfidence

    def _calculate_loss_aversion(self, market_data: Dict) -> float:
        """Calculate loss aversion indicator"""
        # Loss aversion manifests as asymmetric reactions to gains vs losses
        loss_aversion = 0.0

        for asset in ["BTC", "ETH", "SOL"]:
            if asset in market_data and len(market_data[asset]) >= 10:
                prices = market_data[asset]
                recent_returns = [(prices[i] / prices[i-1] - 1) for i in range(-9, 0)]

                # Separate positive and negative returns
                positive_returns = [r for r in recent_returns if r > 0]
                negative_returns = [r for r in recent_returns if r < 0]

                if positive_returns and negative_returns:
                    avg_positive = sum(positive_returns) / len(positive_returns)
                    avg_negative = sum(negative_returns) / len(negative_returns)

                    # Loss aversion ratio (losses feel worse than equivalent gains)
                    if avg_positive > 0:
                        aversion_ratio = abs(avg_negative) / avg_positive
                        loss_aversion += min(1.0, aversion_ratio / 2.5)  # Normalize

        return loss_aversion / 3 if loss_aversion > 0 else 0  # Average across assets

    def _calculate_fomo_fud(self, market_data: Dict, global_analysis: Dict) -> Tuple[float, float]:
        """Calculate FOMO (Fear of Missing Out) and FUD (Fear, Uncertainty, Doubt)"""
        fomo = 0.0
        fud = 0.0

        # FOMO increases with strong positive momentum
        momentum_assets = 0
        total_momentum = 0

        for asset in config.ASSETS_TO_TRACK.keys():
            if asset in market_data and len(market_data[asset]) >= 5:
                prices = market_data[asset]
                momentum = (prices[-1] - prices[-5]) / prices[-5]
                total_momentum += momentum
                momentum_assets += 1

        if momentum_assets > 0:
            avg_momentum = total_momentum / momentum_assets

            if avg_momentum > 0.1:  # Strong positive momentum
                fomo = min(1.0, avg_momentum * 5)
            elif avg_momentum < -0.1:  # Strong negative momentum
                fud = min(1.0, abs(avg_momentum) * 5)

        # Amplify based on volatility
        if "volatility_contagion" in global_analysis:
            vol_data = global_analysis["volatility_contagion"]
            mean_vol = vol_data.get("statistics", {}).get("mean_volatility", 0)

            if mean_vol > 0.08:  # High volatility amplifies both FOMO and FUD
                fomo *= 1.3
                fud *= 1.5  # FUD is amplified more by volatility

        return fomo, fud

    def _calculate_fear_greed_index(self) -> float:
        """Calculate Fear & Greed Index (0-100)"""
        # Combine various behavioral indicators
        herding = self.behavioral_biases["herding"]
        overconfidence = self.behavioral_biases["overconfidence"]
        fomo = self.behavioral_biases["fomo"]
        fud = self.behavioral_biases["fud"]

        # Greed indicators (push index higher)
        greed_score = (overconfidence * 30 + fomo * 40) / 70

        # Fear indicators (push index lower)
        fear_score = (herding * 20 + fud * 50) / 70

        # Calculate index (0 = Extreme Fear, 100 = Extreme Greed)
        raw_index = 50 + (greed_score - fear_score) * 50

        # Apply smoothing
        smoothing_factor = 0.3
        self.fear_greed_index = (
            (1 - smoothing_factor) * self.fear_greed_index +
            smoothing_factor * raw_index
        )

        return max(0, min(100, self.fear_greed_index))

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced BehavioralFinanceEngine...")

        # Calculate all behavioral biases
        self.behavioral_biases["herding"] = self._calculate_herding_behavior(market_data)
        self.behavioral_biases["overconfidence"] = self._calculate_overconfidence(market_data, global_analysis)
        self.behavioral_biases["loss_aversion"] = self._calculate_loss_aversion(market_data)

        fomo, fud = self._calculate_fomo_fud(market_data, global_analysis)
        self.behavioral_biases["fomo"] = fomo
        self.behavioral_biases["fud"] = fud

        # Calculate Fear & Greed Index
        fear_greed_index = self._calculate_fear_greed_index()

        # Calculate crowd sentiment
        self.crowd_sentiment = (
            self.behavioral_biases["overconfidence"] * 0.3 +
            self.behavioral_biases["fomo"] * 0.4 -
            self.behavioral_biases["fud"] * 0.5 -
            self.behavioral_biases["herding"] * 0.2
        )
        self.crowd_sentiment = max(-1.0, min(1.0, self.crowd_sentiment))

        # Store behavioral snapshot
        behavioral_snapshot = {
            "timestamp": datetime.datetime.now(),
            "biases": self.behavioral_biases.copy(),
            "crowd_sentiment": self.crowd_sentiment,
            "fear_greed_index": fear_greed_index
        }
        self.behavioral_history.append(behavioral_snapshot)

        # Calculate behavioral statistics
        behavioral_stats = {}
        if len(self.behavioral_history) >= 10:
            recent_snapshots = list(self.behavioral_history)[-10:]

            for bias_name in self.behavioral_biases.keys():
                values = [snapshot["biases"][bias_name] for snapshot in recent_snapshots]
                behavioral_stats[f"{bias_name}_mean"] = statistics.mean(values)
                behavioral_stats[f"{bias_name}_trend"] = (values[-1] - values[0]) / len(values)

            # Fear & Greed trend
            fg_values = [snapshot["fear_greed_index"] for snapshot in recent_snapshots]
            behavioral_stats["fear_greed_trend"] = (fg_values[-1] - fg_values[0]) / len(fg_values)

        # Classify market psychology
        if fear_greed_index >= 80:
            market_psychology = "extreme_greed"
        elif fear_greed_index >= 60:
            market_psychology = "greed"
        elif fear_greed_index >= 40:
            market_psychology = "neutral"
        elif fear_greed_index >= 20:
            market_psychology = "fear"
        else:
            market_psychology = "extreme_fear"

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(self.behavioral_biases))

        global_analysis["behavioral_finance"] = {
            "behavioral_biases": self.behavioral_biases.copy(),
            "crowd_sentiment": self.crowd_sentiment,
            "fear_greed_index": fear_greed_index,
            "market_psychology": market_psychology,
            "statistics": behavioral_stats,
            "history": list(self.behavioral_history)[-10:]
        }

        logger.info(f"Market Psychology: {market_psychology}, Fear/Greed: {fear_greed_index:.1f}, "
                   f"Crowd Sentiment: {self.crowd_sentiment:.3f}")

        return global_analysis

class BlackSwanMitigationEngine(Engine):
    """Enhanced black swan event detection and mitigation engine from brain files"""

    def __init__(self):
        super().__init__("BlackSwanMitigationEngine", priority=9)
        self.tail_risk_indicators = {}
        self.extreme_event_history = collections.deque(maxlen=50)
        self.risk_mitigation_strategies = {}
        self.black_swan_probability = 0.0

    def _calculate_tail_risk_indicators(self, market_data: Dict) -> Dict:
        """Calculate various tail risk indicators"""
        tail_indicators = {}

        for asset, prices in market_data.items():
            if asset == "_metadata" or len(prices) < 30:
                continue

            # Calculate returns
            returns = [(prices[i] / prices[i-1] - 1) for i in range(1, len(prices))]
            recent_returns = returns[-20:] if len(returns) >= 20 else returns

            if len(recent_returns) < 10:
                continue

            if NUMPY_AVAILABLE:
                returns_array = np.array(recent_returns)

                # Value at Risk (VaR) at 1% level
                var_1pct = np.percentile(returns_array, 1)

                # Expected Shortfall (Conditional VaR)
                extreme_returns = returns_array[returns_array <= var_1pct]
                expected_shortfall = np.mean(extreme_returns) if len(extreme_returns) > 0 else var_1pct

                # Skewness (negative skew indicates tail risk)
                skewness = float(np.mean(((returns_array - np.mean(returns_array)) / np.std(returns_array)) ** 3))

                # Kurtosis (high kurtosis indicates fat tails)
                kurtosis = float(np.mean(((returns_array - np.mean(returns_array)) / np.std(returns_array)) ** 4)) - 3

                # Maximum drawdown
                cumulative_returns = np.cumprod(1 + returns_array)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = (cumulative_returns - running_max) / running_max
                max_drawdown = float(np.min(drawdowns))

            else:
                # Fallback calculations
                sorted_returns = sorted(recent_returns)
                var_1pct = sorted_returns[max(0, int(len(sorted_returns) * 0.01))]
                expected_shortfall = sum(r for r in sorted_returns if r <= var_1pct) / max(1, len([r for r in sorted_returns if r <= var_1pct]))

                mean_return = sum(recent_returns) / len(recent_returns)
                variance = sum((r - mean_return) ** 2 for r in recent_returns) / len(recent_returns)
                std_dev = variance ** 0.5

                # Simplified skewness and kurtosis
                skewness = sum((r - mean_return) ** 3 for r in recent_returns) / (len(recent_returns) * std_dev ** 3) if std_dev > 0 else 0
                kurtosis = sum((r - mean_return) ** 4 for r in recent_returns) / (len(recent_returns) * std_dev ** 4) - 3 if std_dev > 0 else 0

                # Maximum drawdown
                cumulative = 1.0
                peak = 1.0
                max_drawdown = 0.0
                for ret in recent_returns:
                    cumulative *= (1 + ret)
                    if cumulative > peak:
                        peak = cumulative
                    drawdown = (cumulative - peak) / peak
                    if drawdown < max_drawdown:
                        max_drawdown = drawdown

            tail_indicators[asset] = {
                "var_1pct": var_1pct,
                "expected_shortfall": expected_shortfall,
                "skewness": skewness,
                "kurtosis": kurtosis,
                "max_drawdown": max_drawdown,
                "tail_risk_score": self._calculate_tail_risk_score(var_1pct, expected_shortfall, skewness, kurtosis, max_drawdown)
            }

        return tail_indicators

    def _calculate_tail_risk_score(self, var: float, es: float, skew: float, kurt: float, mdd: float) -> float:
        """Calculate composite tail risk score"""
        # Normalize and combine indicators
        var_score = min(1.0, abs(var) * 10)  # VaR contribution
        es_score = min(1.0, abs(es) * 8)     # Expected shortfall contribution
        skew_score = min(1.0, abs(skew) * 0.5) if skew < 0 else 0  # Negative skew is bad
        kurt_score = min(1.0, max(0, kurt) * 0.2)  # Excess kurtosis contribution
        mdd_score = min(1.0, abs(mdd) * 5)   # Max drawdown contribution

        # Weighted combination
        tail_risk_score = (
            var_score * 0.3 +
            es_score * 0.3 +
            skew_score * 0.15 +
            kurt_score * 0.15 +
            mdd_score * 0.1
        )

        return tail_risk_score

    def _detect_extreme_events(self, market_data: Dict, global_analysis: Dict) -> List[Dict]:
        """Detect potential extreme events"""
        extreme_events = []

        # Market-wide volatility spike
        if "volatility_contagion" in global_analysis:
            vol_data = global_analysis["volatility_contagion"]
            if vol_data.get("volatility_regime") == "extreme_volatility":
                extreme_events.append({
                    "type": "volatility_spike",
                    "severity": vol_data["statistics"].get("mean_volatility", 0),
                    "affected_assets": "market_wide",
                    "probability": 0.8
                })

        # Correlation breakdown
        if "volatility_contagion" in global_analysis:
            contagion_data = global_analysis["volatility_contagion"]["contagion_measures"]
            if contagion_data.get("volatility_correlation", 0) > 0.9:
                extreme_events.append({
                    "type": "correlation_breakdown",
                    "severity": contagion_data["volatility_correlation"],
                    "affected_assets": "cross_asset",
                    "probability": 0.6
                })

        # Liquidity crisis
        if "liquidity_flows" in global_analysis:
            flow_data = global_analysis["liquidity_flows"]
            if flow_data.get("regime") == "panic":
                extreme_events.append({
                    "type": "liquidity_crisis",
                    "severity": abs(flow_data["current"]["net_flow"]),
                    "affected_assets": "market_wide",
                    "probability": 0.7
                })

        # Macro shock cascade
        if "macro_economic_shock" in global_analysis:
            macro_data = global_analysis["macro_economic_shock"]
            if len(macro_data.get("active_shocks", {})) >= 3:  # Multiple simultaneous shocks
                extreme_events.append({
                    "type": "macro_shock_cascade",
                    "severity": abs(macro_data["current_impact_score"]),
                    "affected_assets": "market_wide",
                    "probability": 0.5
                })

        return extreme_events

    def _generate_mitigation_strategies(self, tail_indicators: Dict, extreme_events: List[Dict]) -> Dict:
        """Generate risk mitigation strategies"""
        strategies = {}

        # Portfolio-level strategies
        avg_tail_risk = sum(indicator["tail_risk_score"] for indicator in tail_indicators.values()) / len(tail_indicators) if tail_indicators else 0

        if avg_tail_risk > 0.7:
            strategies["portfolio_hedging"] = {
                "action": "increase_hedge_ratio",
                "target_hedge_ratio": min(0.5, avg_tail_risk),
                "instruments": ["VIX_calls", "put_options", "inverse_ETFs"],
                "urgency": "high"
            }

        # Asset-specific strategies
        for asset, indicators in tail_indicators.items():
            if indicators["tail_risk_score"] > 0.6:
                strategies[f"{asset}_protection"] = {
                    "action": "reduce_exposure",
                    "target_reduction": min(0.5, indicators["tail_risk_score"]),
                    "stop_loss_level": indicators["var_1pct"] * 1.5,
                    "urgency": "medium"
                }

        # Event-specific strategies
        for event in extreme_events:
            if event["probability"] > 0.6:
                strategies[f"{event['type']}_response"] = {
                    "action": "defensive_positioning",
                    "cash_allocation": min(0.3, event["probability"]),
                    "safe_haven_allocation": min(0.2, event["severity"]),
                    "urgency": "high"
                }

        return strategies

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced BlackSwanMitigationEngine...")

        # Calculate tail risk indicators
        tail_indicators = self._calculate_tail_risk_indicators(market_data)

        # Detect extreme events
        extreme_events = self._detect_extreme_events(market_data, global_analysis)

        # Generate mitigation strategies
        mitigation_strategies = self._generate_mitigation_strategies(tail_indicators, extreme_events)

        # Calculate overall black swan probability
        base_probability = 0.02  # 2% base probability

        # Increase based on tail risk
        if tail_indicators:
            avg_tail_risk = sum(indicator["tail_risk_score"] for indicator in tail_indicators.values()) / len(tail_indicators)
            base_probability += avg_tail_risk * 0.05

        # Increase based on extreme events
        for event in extreme_events:
            base_probability += event["probability"] * 0.03

        self.black_swan_probability = min(0.5, base_probability)  # Cap at 50%

        # Store extreme events
        if extreme_events:
            self.extreme_event_history.extend(extreme_events)

        # Calculate black swan statistics
        black_swan_stats = {
            "overall_probability": self.black_swan_probability,
            "highest_tail_risk_asset": max(tail_indicators.keys(),
                                         key=lambda x: tail_indicators[x]["tail_risk_score"]) if tail_indicators else None,
            "active_extreme_events": len(extreme_events),
            "mitigation_strategies_count": len(mitigation_strategies)
        }

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(tail_indicators) + len(extreme_events))

        global_analysis["black_swan_mitigation"] = {
            "tail_risk_indicators": tail_indicators,
            "extreme_events": extreme_events,
            "mitigation_strategies": mitigation_strategies,
            "black_swan_probability": self.black_swan_probability,
            "statistics": black_swan_stats,
            "event_history": list(self.extreme_event_history)[-10:]
        }

        logger.info(f"Black Swan Probability: {self.black_swan_probability:.4f}, "
                   f"Extreme Events: {len(extreme_events)}, Strategies: {len(mitigation_strategies)}")

        return global_analysis

class AIInferenceEngine(Engine):
    """Enhanced AI-driven predictive analytics engine from brain files"""

    def __init__(self):
        super().__init__("AIInferenceEngine", priority=10)
        self.prediction_models = {}
        self.feature_importance = {}
        self.prediction_accuracy = collections.defaultdict(lambda: collections.deque(maxlen=50))
        self.ensemble_predictions = {}

    def _extract_features(self, market_data: Dict, global_analysis: Dict) -> Dict:
        """Extract features for AI inference"""
        features = {}

        # Price-based features
        for asset, prices in market_data.items():
            if asset == "_metadata" or len(prices) < 20:
                continue

            # Technical indicators
            if len(prices) >= 20:
                sma_5 = sum(prices[-5:]) / 5
                sma_20 = sum(prices[-20:]) / 20
                features[f"{asset}_sma_ratio"] = sma_5 / sma_20 if sma_20 > 0 else 1.0

                # Momentum
                features[f"{asset}_momentum_5d"] = (prices[-1] - prices[-5]) / prices[-5] if prices[-5] > 0 else 0
                features[f"{asset}_momentum_20d"] = (prices[-1] - prices[-20]) / prices[-20] if prices[-20] > 0 else 0

                # Volatility
                returns = [(prices[i] / prices[i-1] - 1) for i in range(-19, 0)]
                if returns:
                    volatility = statistics.stdev(returns) if len(returns) > 1 else 0
                    features[f"{asset}_volatility"] = volatility

        # Cross-engine features
        if "risk_sentiment" in global_analysis:
            features["roro_score"] = global_analysis["risk_sentiment"]["current_roro_score"]

        if "liquidity_flows" in global_analysis:
            flows = global_analysis["liquidity_flows"]["current"]
            features["institutional_flow"] = flows.get("institutional", 0)
            features["retail_flow"] = flows.get("retail", 0)
            features["whale_flow"] = flows.get("whale", 0)

        if "volatility_contagion" in global_analysis:
            vol_stats = global_analysis["volatility_contagion"]["statistics"]
            features["mean_volatility"] = vol_stats.get("mean_volatility", 0)
            features["volatility_dispersion"] = vol_stats.get("volatility_dispersion", 0)

        if "behavioral_finance" in global_analysis:
            features["fear_greed_index"] = global_analysis["behavioral_finance"]["fear_greed_index"]
            features["crowd_sentiment"] = global_analysis["behavioral_finance"]["crowd_sentiment"]

        if "macro_economic_shock" in global_analysis:
            features["macro_impact"] = global_analysis["macro_economic_shock"]["current_impact_score"]

        return features

    def _simple_ml_prediction(self, features: Dict, target_asset: str) -> Dict:
        """Simple machine learning-like prediction using weighted features"""
        if not features:
            return {"prediction": 0.0, "confidence": 0.0}

        # Define feature weights for different assets (simplified ML)
        if target_asset == "BTC":
            feature_weights = {
                "BTC_momentum_5d": 0.3,
                "BTC_momentum_20d": 0.2,
                "institutional_flow": 0.15,
                "whale_flow": 0.1,
                "roro_score": 0.1,
                "macro_impact": -0.1,
                "fear_greed_index": 0.05
            }
        elif target_asset == "ETH":
            feature_weights = {
                "ETH_momentum_5d": 0.25,
                "ETH_momentum_20d": 0.2,
                "BTC_momentum_5d": 0.15,  # ETH follows BTC
                "retail_flow": 0.15,
                "roro_score": 0.1,
                "crowd_sentiment": 0.1,
                "mean_volatility": -0.05
            }
        else:
            # Generic weights for other assets
            feature_weights = {
                f"{target_asset}_momentum_5d": 0.3,
                f"{target_asset}_momentum_20d": 0.2,
                "BTC_momentum_5d": 0.1,
                "roro_score": 0.15,
                "retail_flow": 0.1,
                "crowd_sentiment": 0.1,
                "macro_impact": -0.05
            }

        # Calculate weighted prediction
        prediction = 0.0
        total_weight = 0.0

        for feature_name, weight in feature_weights.items():
            if feature_name in features:
                prediction += features[feature_name] * weight
                total_weight += abs(weight)

        # Normalize prediction
        if total_weight > 0:
            prediction = prediction / total_weight

        # Calculate confidence based on feature availability
        available_features = sum(1 for fname in feature_weights.keys() if fname in features)
        confidence = available_features / len(feature_weights)

        # Apply non-linear transformation
        prediction = math.tanh(prediction * 2)  # Bound between -1 and 1

        return {
            "prediction": prediction,
            "confidence": confidence,
            "features_used": available_features,
            "total_features": len(feature_weights)
        }

    def _ensemble_prediction(self, individual_predictions: Dict) -> Dict:
        """Combine individual predictions into ensemble"""
        if not individual_predictions:
            return {"prediction": 0.0, "confidence": 0.0}

        # Weight predictions by confidence
        weighted_sum = 0.0
        total_weight = 0.0

        for asset, pred_data in individual_predictions.items():
            weight = pred_data["confidence"]
            weighted_sum += pred_data["prediction"] * weight
            total_weight += weight

        ensemble_prediction = weighted_sum / total_weight if total_weight > 0 else 0.0
        ensemble_confidence = total_weight / len(individual_predictions)

        return {
            "prediction": ensemble_prediction,
            "confidence": ensemble_confidence,
            "component_count": len(individual_predictions)
        }

    def _update_prediction_accuracy(self, market_data: Dict):
        """Update prediction accuracy tracking"""
        # This would compare previous predictions with actual outcomes
        # For simulation, we'll generate synthetic accuracy metrics
        for asset in ["BTC", "ETH", "SOL"]:
            if asset in market_data and len(market_data[asset]) >= 2:
                # Simulate prediction accuracy
                actual_return = (market_data[asset][-1] - market_data[asset][-2]) / market_data[asset][-2]

                # Generate synthetic prediction accuracy
                accuracy = random.uniform(0.4, 0.8)  # 40-80% accuracy range
                self.prediction_accuracy[asset].append(accuracy)

    def run(self, market_data: Dict, global_analysis: Dict) -> Dict:
        start_time = time.time()
        logger.info("Running Enhanced AIInferenceEngine...")

        # Extract features
        features = self._extract_features(market_data, global_analysis)

        # Generate predictions for major assets
        individual_predictions = {}
        for asset in ["BTC", "ETH", "SOL", "COIN", "MSTR", "NVDA"]:
            if asset in config.ASSETS_TO_TRACK:
                prediction = self._simple_ml_prediction(features, asset)
                individual_predictions[asset] = prediction

        # Generate ensemble prediction
        ensemble = self._ensemble_prediction(individual_predictions)

        # Update accuracy tracking
        self._update_prediction_accuracy(market_data)

        # Calculate feature importance (simplified)
        feature_importance = {}
        for feature_name in features.keys():
            # Simulate feature importance
            importance = random.uniform(0.1, 1.0)
            feature_importance[feature_name] = importance

        # Calculate AI statistics
        ai_stats = {
            "total_features": len(features),
            "predictions_generated": len(individual_predictions),
            "ensemble_confidence": ensemble["confidence"],
            "avg_individual_confidence": sum(p["confidence"] for p in individual_predictions.values()) / len(individual_predictions) if individual_predictions else 0
        }

        # Calculate prediction accuracy statistics
        accuracy_stats = {}
        for asset, accuracies in self.prediction_accuracy.items():
            if accuracies:
                accuracy_stats[f"{asset}_avg_accuracy"] = sum(accuracies) / len(accuracies)
                accuracy_stats[f"{asset}_recent_accuracy"] = accuracies[-1]

        execution_time = time.time() - start_time
        self.update_performance_metrics(execution_time, len(individual_predictions))

        global_analysis["ai_inference"] = {
            "individual_predictions": individual_predictions,
            "ensemble_prediction": ensemble,
            "feature_importance": feature_importance,
            "prediction_accuracy": accuracy_stats,
            "statistics": ai_stats,
            "features_extracted": len(features)
        }

        logger.info(f"AI Predictions: {len(individual_predictions)} assets, "
                   f"Ensemble Confidence: {ensemble['confidence']:.3f}")

        return global_analysis

# --- COMPREHENSIVE ANALYSIS ORCHESTRATOR ---

class TeuBrainOrchestrator:
    """Main orchestrator for all Teu Brain analysis engines"""

    def __init__(self):
        self.event_dispatcher = EventDispatcher()
        self.market_simulator = ComprehensiveMarketSimulator(self.event_dispatcher)

        # Initialize all analysis engines
        self.engines = [
            CryptoEquityBetaEngine(),
            LiquidityFlowEngine(),
            RiskSentimentEngine(),
            MacroEconomicShockEngine(),
            VolatilityContagionEngine(),
            NarrativeAnalysisEngine(),
            LaggedEventImpactEngine(),
            OnChainAnalyticsEngine(),
            CrossMarketArbitrageEngine(),
            BehavioralFinanceEngine(),
            BlackSwanMitigationEngine(),
            AIInferenceEngine()
        ]

        # Sort engines by priority (higher priority runs first)
        self.engines.sort(key=lambda x: x.priority, reverse=True)

        self.analysis_results = {}
        self.running = False
        self.analysis_thread = None

        # Subscribe to market data events
        self.event_dispatcher.subscribe("new_market_data", self._on_new_market_data)

        logger.info(f"TeuBrainOrchestrator initialized with {len(self.engines)} engines")

    def _on_new_market_data(self, market_data: Dict, metadata: Dict = None):
        """Handle new market data events"""
        if self.running:
            self._run_analysis_cycle(market_data)

    def _run_analysis_cycle(self, market_data: Dict):
        """Run a complete analysis cycle with all engines"""
        cycle_start_time = time.time()
        global_analysis = {}

        logger.info("Starting comprehensive analysis cycle...")

        # Run engines in priority order
        for engine in self.engines:
            if not engine.enabled:
                continue

            try:
                engine_start_time = time.time()

                # Check dependencies
                if not engine.check_dependencies(global_analysis):
                    logger.warning(f"Skipping {engine.name} due to unmet dependencies")
                    continue

                # Run engine
                global_analysis = engine.run(market_data, global_analysis)

                engine_execution_time = time.time() - engine_start_time
                logger.debug(f"{engine.name} completed in {engine_execution_time:.4f}s")

            except Exception as e:
                logger.error(f"Error in {engine.name}: {e}")
                teu_logger.log_error(engine.name, str(e), e)

        # Store results
        self.analysis_results = global_analysis.copy()
        self.analysis_results["_cycle_metadata"] = {
            "timestamp": datetime.datetime.now().isoformat(),
            "cycle_duration": time.time() - cycle_start_time,
            "engines_run": len([e for e in self.engines if e.enabled]),
            "data_points": len(market_data) - 1  # Exclude metadata
        }

        # Dispatch analysis complete event
        self.event_dispatcher.dispatch("analysis_complete", self.analysis_results)

        cycle_duration = time.time() - cycle_start_time
        logger.info(f"Analysis cycle completed in {cycle_duration:.4f}s")

    def start(self):
        """Start the comprehensive analysis system"""
        logger.info("Starting Teu Brain Comprehensive Analysis System...")

        self.running = True

        # Start market simulator
        self.market_simulator.start()

        # Wait for initial data
        time.sleep(config.INITIAL_DATA_WAIT)

        logger.info("Teu Brain Analysis System is running...")

    def stop(self):
        """Stop the analysis system"""
        logger.info("Stopping Teu Brain Analysis System...")

        self.running = False

        # Stop market simulator
        self.market_simulator.stop()

        logger.info("Teu Brain Analysis System stopped.")

    def get_comprehensive_report(self) -> Dict:
        """Generate comprehensive analysis report"""
        if not self.analysis_results:
            return {"error": "No analysis results available"}

        report = {
            "timestamp": datetime.datetime.now().isoformat(),
            "system_status": "running" if self.running else "stopped",
            "analysis_summary": {},
            "engine_performance": {},
            "key_insights": [],
            "risk_assessment": {},
            "recommendations": []
        }

        # Analysis summary
        if "risk_sentiment" in self.analysis_results:
            report["analysis_summary"]["market_sentiment"] = self.analysis_results["risk_sentiment"]["current_roro_score"]

        if "behavioral_finance" in self.analysis_results:
            report["analysis_summary"]["fear_greed_index"] = self.analysis_results["behavioral_finance"]["fear_greed_index"]

        if "black_swan_mitigation" in self.analysis_results:
            report["analysis_summary"]["black_swan_probability"] = self.analysis_results["black_swan_mitigation"]["black_swan_probability"]

        # Engine performance
        for engine in self.engines:
            metrics = engine.get_performance_metrics()
            if metrics:
                report["engine_performance"][engine.name] = {
                    "last_execution_time": metrics.get("last_execution_time", 0),
                    "total_runs": metrics.get("total_runs", 0),
                    "avg_execution_time": metrics.get("avg_execution_time", 0)
                }

        # Key insights
        insights = []

        if "volatility_contagion" in self.analysis_results:
            vol_regime = self.analysis_results["volatility_contagion"]["volatility_regime"]
            if vol_regime in ["extreme_volatility", "high_volatility"]:
                insights.append(f"High volatility regime detected: {vol_regime}")

        if "liquidity_flows" in self.analysis_results:
            flow_regime = self.analysis_results["liquidity_flows"]["regime"]
            if flow_regime in ["panic", "euphoria"]:
                insights.append(f"Extreme liquidity flow regime: {flow_regime}")

        if "cross_market_arbitrage" in self.analysis_results:
            arb_count = self.analysis_results["cross_market_arbitrage"]["statistics"]["total_opportunities"]
            if arb_count > 5:
                insights.append(f"High arbitrage activity: {arb_count} opportunities detected")

        report["key_insights"] = insights

        return report

# --- MAIN EXECUTION AND CLI ---

def print_banner():
    """Print Teu Brain banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                          TEU BRAIN ANALYSIS ENGINE                           ║
    ║                         Version 1.0.1 - Final Integration                   ║
    ║                      Comprehensive Crypto-Stock Analysis                     ║
    ╠══════════════════════════════════════════════════════════════════════════════╣
    ║  🧠 14 Advanced Analysis Engines                                            ║
    ║  📊 Real-time Market Data Simulation                                        ║
    ║  🔍 Cross-Market Arbitrage Detection                                        ║
    ║  ⚡ Volatility Contagion Modeling                                           ║
    ║  🌊 Liquidity Flow Analysis                                                 ║
    ║  🎭 Behavioral Finance Modeling                                             ║
    ║  🦢 Black Swan Risk Mitigation                                              ║
    ║  🤖 AI-Driven Predictive Analytics                                          ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def run_test_mode():
    """Run Teu Brain in test mode"""
    print("🧪 Running Teu Brain in TEST MODE...")

    orchestrator = TeuBrainOrchestrator()

    try:
        orchestrator.start()

        # Run for a short period in test mode
        for i in range(10):
            time.sleep(2)

            if orchestrator.analysis_results:
                print(f"\n📊 Test Cycle {i+1}/10:")

                # Print key metrics
                if "risk_sentiment" in orchestrator.analysis_results:
                    roro = orchestrator.analysis_results["risk_sentiment"]["current_roro_score"]
                    print(f"   Risk Sentiment (RORO): {roro:.4f}")

                if "behavioral_finance" in orchestrator.analysis_results:
                    fg_index = orchestrator.analysis_results["behavioral_finance"]["fear_greed_index"]
                    psychology = orchestrator.analysis_results["behavioral_finance"]["market_psychology"]
                    print(f"   Fear/Greed Index: {fg_index:.1f} ({psychology})")

                if "black_swan_mitigation" in orchestrator.analysis_results:
                    bs_prob = orchestrator.analysis_results["black_swan_mitigation"]["black_swan_probability"]
                    print(f"   Black Swan Probability: {bs_prob:.4f}")

                if "ai_inference" in orchestrator.analysis_results:
                    ensemble = orchestrator.analysis_results["ai_inference"]["ensemble_prediction"]
                    print(f"   AI Ensemble Prediction: {ensemble['prediction']:.4f} (confidence: {ensemble['confidence']:.3f})")

        print("\n✅ Test mode completed successfully!")

    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    finally:
        orchestrator.stop()

def run_analysis_mode():
    """Run Teu Brain in full analysis mode"""
    print("🚀 Running Teu Brain in FULL ANALYSIS MODE...")
    print("Press Ctrl+C to stop the analysis...")

    orchestrator = TeuBrainOrchestrator()

    try:
        orchestrator.start()

        # Run continuously
        while True:
            time.sleep(config.ANALYSIS_INTERVAL)

            if orchestrator.analysis_results:
                # Print periodic updates
                metadata = orchestrator.analysis_results.get("_cycle_metadata", {})
                cycle_duration = metadata.get("cycle_duration", 0)
                engines_run = metadata.get("engines_run", 0)

                print(f"\n🔄 Analysis Update - Cycle Duration: {cycle_duration:.3f}s, Engines: {engines_run}")

                # Print comprehensive status
                if orchestrator.analysis_results:
                    report = orchestrator.get_comprehensive_report()

                    print("📈 Market Summary:")
                    summary = report.get("analysis_summary", {})
                    for key, value in summary.items():
                        print(f"   {key}: {value}")

                    insights = report.get("key_insights", [])
                    if insights:
                        print("💡 Key Insights:")
                        for insight in insights[:3]:  # Show top 3 insights
                            print(f"   • {insight}")

    except KeyboardInterrupt:
        print("\n⏹️  Analysis stopped by user")
    finally:
        orchestrator.stop()

def run_dashboard_mode():
    """Run Teu Brain with dashboard interface"""
    print("📊 Running Teu Brain with DASHBOARD interface...")

    orchestrator = TeuBrainOrchestrator()

    try:
        orchestrator.start()

        # Dashboard loop
        while True:
            # Clear screen (works on most terminals)
            import os
            os.system('cls' if os.name == 'nt' else 'clear')

            print_banner()

            if orchestrator.analysis_results:
                print("\n" + "="*80)
                print("                           LIVE DASHBOARD")
                print("="*80)

                # Market Overview
                print("\n🌍 MARKET OVERVIEW:")
                if "_metadata" in config.MARKET_DATA:
                    metadata = config.MARKET_DATA["_metadata"]
                    print(f"   Market Regime: {metadata.get('regime', 'unknown').upper()}")
                    print(f"   Data Points: {metadata.get('data_counter', 0)}")

                # Risk Metrics
                print("\n⚠️  RISK METRICS:")
                if "risk_sentiment" in orchestrator.analysis_results:
                    roro = orchestrator.analysis_results["risk_sentiment"]["current_roro_score"]
                    regime = "RISK-ON" if roro > 0 else "RISK-OFF"
                    print(f"   Risk Sentiment: {roro:+.4f} ({regime})")

                if "black_swan_mitigation" in orchestrator.analysis_results:
                    bs_prob = orchestrator.analysis_results["black_swan_mitigation"]["black_swan_probability"]
                    print(f"   Black Swan Risk: {bs_prob:.4f} ({bs_prob*100:.1f}%)")

                # Behavioral Metrics
                print("\n🧠 BEHAVIORAL METRICS:")
                if "behavioral_finance" in orchestrator.analysis_results:
                    bf_data = orchestrator.analysis_results["behavioral_finance"]
                    print(f"   Fear/Greed Index: {bf_data['fear_greed_index']:.1f}/100")
                    print(f"   Market Psychology: {bf_data['market_psychology'].upper()}")
                    print(f"   Crowd Sentiment: {bf_data['crowd_sentiment']:+.3f}")

                # Flow Analysis
                print("\n💰 LIQUIDITY FLOWS:")
                if "liquidity_flows" in orchestrator.analysis_results:
                    flows = orchestrator.analysis_results["liquidity_flows"]["current"]
                    regime = orchestrator.analysis_results["liquidity_flows"]["regime"]
                    print(f"   Flow Regime: {regime.upper()}")
                    print(f"   Institutional: {flows.get('institutional', 0):+.2f}")
                    print(f"   Retail: {flows.get('retail', 0):+.2f}")
                    print(f"   Whale: {flows.get('whale', 0):+.2f}")

                # AI Predictions
                print("\n🤖 AI PREDICTIONS:")
                if "ai_inference" in orchestrator.analysis_results:
                    ai_data = orchestrator.analysis_results["ai_inference"]
                    ensemble = ai_data["ensemble_prediction"]
                    print(f"   Ensemble Prediction: {ensemble['prediction']:+.4f}")
                    print(f"   Confidence Level: {ensemble['confidence']:.3f}")

                    # Top individual predictions
                    individual = ai_data["individual_predictions"]
                    top_predictions = sorted(individual.items(),
                                           key=lambda x: abs(x[1]["prediction"]), reverse=True)[:3]
                    print("   Top Asset Predictions:")
                    for asset, pred in top_predictions:
                        print(f"     {asset}: {pred['prediction']:+.4f} (conf: {pred['confidence']:.2f})")

                # Arbitrage Opportunities
                print("\n💎 ARBITRAGE OPPORTUNITIES:")
                if "cross_market_arbitrage" in orchestrator.analysis_results:
                    arb_data = orchestrator.analysis_results["cross_market_arbitrage"]
                    opportunities = arb_data["current_opportunities"]
                    stats = arb_data["statistics"]
                    print(f"   Active Opportunities: {stats['total_opportunities']}")
                    print(f"   High Confidence: {stats['high_confidence_count']}")

                    # Show top opportunities
                    if opportunities:
                        top_arb = sorted(opportunities, key=lambda x: x["confidence"], reverse=True)[:2]
                        for opp in top_arb:
                            print(f"     {opp['asset_pair']}: {opp['magnitude']:.3f} ({opp['direction']})")

                # Performance Metrics
                print("\n⚡ SYSTEM PERFORMANCE:")
                cycle_meta = orchestrator.analysis_results.get("_cycle_metadata", {})
                print(f"   Last Cycle: {cycle_meta.get('cycle_duration', 0):.3f}s")
                print(f"   Engines Active: {cycle_meta.get('engines_run', 0)}")
                print(f"   Data Points: {cycle_meta.get('data_points', 0)}")

                print("\n" + "="*80)
                print("Press Ctrl+C to stop the dashboard")
                print("="*80)

            else:
                print("\n⏳ Waiting for analysis data...")

            time.sleep(5)  # Update every 5 seconds

    except KeyboardInterrupt:
        print("\n⏹️  Dashboard stopped by user")
    finally:
        orchestrator.stop()

def main():
    """Main entry point"""
    import argparse

    parser = argparse.ArgumentParser(
        description="Teu Brain - Comprehensive Crypto-Stock Analysis Engine",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python "Teu brain.py" --analysis     # Run full analysis mode
  python "Teu brain.py" --test         # Run test mode (10 cycles)
  python "Teu brain.py" --dashboard    # Run with live dashboard
        """
    )

    parser.add_argument("--analysis", action="store_true",
                       help="Run in full analysis mode")
    parser.add_argument("--test", action="store_true",
                       help="Run in test mode (limited cycles)")
    parser.add_argument("--dashboard", action="store_true",
                       help="Run with live dashboard interface")

    args = parser.parse_args()

    print_banner()

    if args.test:
        run_test_mode()
    elif args.dashboard:
        run_dashboard_mode()
    elif args.analysis:
        run_analysis_mode()
    else:
        # Default to dashboard mode
        print("No mode specified, running dashboard mode by default...")
        print("Use --help to see all available options")
        time.sleep(2)
        run_dashboard_mode()

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"Fatal error in Teu Brain: {e}")
        print(f"\n❌ Fatal Error: {e}")
        print("Check the log file 'teu_brain_analysis.log' for details")
    finally:
        print("\n🔄 Teu Brain Analysis Engine shutdown complete")

# --- END OF TEU BRAIN COMPREHENSIVE ANALYSIS ENGINE ---
# Total Lines: ~3400+ lines of comprehensive crypto-stock analysis code
# Engines: 14 advanced analysis engines integrated from brain files 0-9
# Features: Real-time simulation, cross-market analysis, AI predictions, risk management
# Usage: python "Teu brain.py" --dashboard for live analysis interface