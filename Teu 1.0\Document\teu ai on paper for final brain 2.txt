# [cite_start]Teu.py - Institutional-Grade Financial Analysis Engine (Continued) [cite: 42]
# [cite_start]Version: 3.0.0 [cite: 42]
# [cite_start]Author: Gemini [cite: 42]
#
# DESCRIPTION:
# This is the v3.0 evolution of the analysis engine, rebuilt with an event-driven
# architecture and expanded to over 5000 lines with multiple new, sophisticated
# [cite_start]analysis engines. [cite: 42] The core focus is on modeling the complex, interconnected
# relationships between different market factors as described in the user's
# [cite_start]research, providing an unparalleled depth of analysis. [cite: 43]
#
# NEW IN V3.0 - "THE THINKING ENGINE":
# - ARCHITECTURE:
#   - Event-Driven Design: Implemented a robust EventDispatcher for decoupled,
#     [cite_start]scalable communication between all system components. [cite: 44]
#   [cite_start]- Abstract Base Classes: Enforces a clean, consistent structure for all modules. [cite: 45]
# - NEW ANALYSIS ENGINES:
#   - RiskSentimentEngine: Generates a master Risk-On/Risk-Off (RORO) score
#     [cite_start]that influences the entire system's analysis. [cite: 46]
#   - NarrativeAnalysisEngine: Identifies and tracks dominant market narratives
#     [cite_start](e.g., "AI & Crypto", "DeFi Regulation") using NLP on news and social media. [cite: 47]
#   - CryptoEquityBetaEngine: Calculates the beta of crypto-related stocks
#     [cite_start](e.g., COIN, MSTR) to their underlying crypto assets (BTC, ETH). [cite: 48]
#   - LaggedEventImpactEngine: Analyzes the market impact of macro events over
#     [cite_start]multiple time horizons (1h, 24h, 72h) to model delayed reactions. [cite: 49]
#   - LiquidityFlowEngine: Simulates order book data to analyze market depth
#     [cite_start]and liquidity conditions. [cite: 50]
# - DYNAMIC SIMULATION & DEEPER ANALYSIS:
#   - The MarketSimulator is now driven by the RORO score, generating data that
#     [cite_start]realistically reflects the current market mood. [cite: 51]
#   [cite_start]- Correlation analysis now tracks changes over time (30d vs 90d). [cite: 52]
#   [cite_start]- Predictive model upgraded to an ensemble approach, using inputs from all engines. [cite: 53]
# - ADVANCED TOOLING:
#   [cite_start]- Enhanced CLI for targeted analysis and operational modes. [cite: 54]
#   - Massively expanded and detailed prompt for the Gemini API to synthesize
#     [cite_start]all new analytical layers into a cohesive, expert-level report. [cite: 55]
#
# DISCLAIMER:
# [cite_start]This script is for educational and illustrative purposes ONLY. [cite: 56] The analysis and reports
# [cite_start]generated by this script DO NOT CONSTITUTE FINANCIAL ADVICE. [cite: 57] Trading and investing in
# [cite_start]financial markets involve substantial risk. [cite: 58] Always conduct your own research and consult
# [cite_start]with a qualified financial advisor before making any investment decisions. [cite: 58]

[cite_start]import os [cite: 59]
[cite_start]import sys [cite: 59]
[cite_start]import json [cite: 59]
[cite_start]import time [cite: 59]
[cite_start]import random [cite: 59]
[cite_start]import logging [cite: 59]
[cite_start]import threading [cite: 59]
[cite_start]import unittest [cite: 59]
[cite_start]import numpy as np [cite: 59]
[cite_start]import pandas as pd [cite: 59]
[cite_start]import requests [cite: 59]
[cite_start]from collections import deque, defaultdict [cite: 59]
[cite_start]from abc import ABC, abstractmethod [cite: 59]
from datetime import datetime, timedelta

# --- CONFIGURATION ---
class Config:
    [cite_start]GEMINI_API_KEY = "YOUR_GEMINI_API_KEY" [cite: 59]
    [cite_start]GEMINI_API_URL = f"[https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=](https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=){GEMINI_API_KEY}" [cite: 59]
    [cite_start]ASSETS_TO_TRACK = ['BTC', 'ETH', 'AAPL', 'GOOG', 'MSTR', 'COIN'] [cite: 59]
    [cite_start]STATE_FILE = "engine_state_v3.json" [cite: 59]
    [cite_start]LOG_FILE = "engine_v3.log" [cite: 59]
    # Intervals in seconds
    [cite_start]DATA_PROVIDER_INTERVAL = 2 [cite: 59]
    [cite_start]ANALYSIS_INTERVAL = 180 [cite: 59]
    [cite_start]INITIAL_DATA_WAIT = 45 [cite: 59]
    # Lookback Periods & Parameters
    [cite_start]SMA_SHORT, SMA_LONG = 20, 100 [cite: 59]
    [cite_start]EMA_SHORT, EMA_LONG = 12, 26 [cite: 60]
    [cite_start]RSI_PERIOD, BOLLINGER_PERIOD, ATR_PERIOD = 14, 20, 14 [cite: 60]
    [cite_start]STOCH_K, STOCH_D = 14, 3 [cite: 60]
    [cite_start]CORRELATION_WINDOWS = [30, 90] [cite: 60]
    # Data Storage Limits
    [cite_start]MAX_PRICE_POINTS, MAX_NEWS, MAX_OPINIONS, MAX_ONCHAIN, MAX_MACRO = 5000, 500, 1000, 1000, 100 [cite: 60]

# --- LOGGING SETUP ---
[cite_start]logger = logging.getLogger(__name__) [cite: 60]
[cite_start]logger.setLevel(logging.DEBUG) [cite: 60]
# Console handler
[cite_start]c_handler = logging.StreamHandler() [cite: 60]
[cite_start]c_handler.setLevel(logging.INFO) [cite: 60]
# File handler
[cite_start]f_handler = logging.FileHandler(Config.LOG_FILE, mode='w') [cite: 60]
[cite_start]f_handler.setLevel(logging.DEBUG) [cite: 60]
[cite_start]formatter = logging.Formatter('%(asctime)s - [%(threadName)s] - %(levelname)s - %(module)s.%(funcName)s: %(message)s') [cite: 60]
[cite_start]c_handler.setFormatter(formatter) [cite: 60]
[cite_start]f_handler.setFormatter(formatter) [cite: 60]
[cite_start]logger.addHandler(c_handler) [cite: 60]
[cite_start]logger.addHandler(f_handler) [cite: 60]

# --- UTILITY FUNCTIONS & EVENT DISPATCHER ---
def safe_float(value, default=0.0):
    try: return float(value)
    [cite_start]except (ValueError, TypeError, AttributeError): return default [cite: 61]

def get_current_timestamp():
    [cite_start]return datetime.utcnow().isoformat() + 'Z' [cite: 61]

class EventDispatcher:
    [cite_start]"""A simple pub/sub event dispatcher to decouple system components.""" [cite: 61]
    def __init__(self):
        [cite_start]self._listeners = defaultdict(list) [cite: 61]
    def subscribe(self, event_type, listener):
        [cite_start]self._listeners[event_type].append(listener) [cite: 61]
        [cite_start]logger.debug(f"Listener {listener.__class__.__name__} subscribed to event '{event_type}'") [cite: 61]
    def dispatch(self, event_type, *args, **kwargs):
        [cite_start]logger.debug(f"Dispatching event '{event_type}'") [cite: 61]
        for listener in self._listeners[event_type]:
            [cite_start]try: [cite: 62]
                listener.handle_event(event_type, *args, **kwargs)
            except Exception as e:
                [cite_start]logger.error(f"Error in listener {listener.__class__.__name__} for event '{event_type}': {e}", exc_info=True) [cite: 62]

# --- DATA MODELS (with expanded detail and serialization) ---
class Serializable(ABC):
    [cite_start]def to_dict(self): return self.__dict__ [cite: 62]
    @classmethod
    [cite_start]def from_dict(cls, data): return cls(**data) [cite: 62]

class DataPoint(Serializable):
    [cite_start]def __init__(self, timestamp, open_price, high_price, low_price, close_price, volume): [cite: 63]
        [cite_start]self.timestamp = timestamp [cite: 63]
        [cite_start]self.open, self.high, self.low, self.close, self.volume = map(safe_float, [open_price, high_price, low_price, close_price, volume]) [cite: 63]
    [cite_start]def __repr__(self): return f"DataPoint(T='{self.timestamp}', C={self.close})" [cite: 63]

class NewsArticle(Serializable):
    [cite_start]def __init__(self, timestamp, source, headline, summary, topics=None, impact_score=0.0): [cite: 63]
        [cite_start]self.timestamp, self.source, self.headline, self.summary = timestamp, source, headline, summary [cite: 64]
        [cite_start]self.topics = topics or [] [cite: 64]
        [cite_start]self.sentiment_score = self._analyze_sentiment() [cite: 64]
        [cite_start]self.impact_score = impact_score [cite: 64]
    def _analyze_sentiment(self):
        [cite_start]text = (self.headline + " " + self.summary).lower() [cite: 64]
        [cite_start]pos = ['up', 'bullish', 'rally', 'gains', 'optimistic', 'record', 'high', 'approval', 'growth', 'breakthrough'] [cite: 64]
        [cite_start]neg = ['down', 'bearish', 'crash', 'losses', 'plunges', 'pessimistic', 'fears', 'risk', 'regulation', 'hack'] [cite: 64]
        [cite_start]return np.clip((sum(1 for w in pos if w in text) - sum(1 for w in neg if w in text)) / 5.0, -1.0, 1.0) [cite: 64]
    [cite_start]def __repr__(self): return f"NewsArticle(Src='{self.source}', Headline='{self.headline[:30]}...')" [cite: 65]

class UserOpinion(Serializable):
    def __init__(self, timestamp, platform, text, author_influence=1.0):
        [cite_start]self.timestamp, self.platform, self.text = timestamp, platform, text [cite: 65]
        [cite_start]self.author_influence = author_influence [cite: 66]
        [cite_start]self.sentiment_score = self._analyze_sentiment() * self.author_influence [cite: 66]
    def _analyze_sentiment(self):
        [cite_start]text = self.text.lower() [cite: 66]
        [cite_start]pos = ['buy', 'hodl', 'to the moon', 'diamond hands', 'long', 'bull', 'undervalued', 'send it'] [cite: 66]
        [cite_start]neg = ['sell', 'dump', 'scam', 'rekt', 'short', 'bear', 'fud', 'overvalued', 'rugpull'] [cite: 66]
        [cite_start]return np.clip((sum(1.5 for w in pos if w in text) - sum(1.5 for w in neg if w in text)) / 3.0, -1.0, 1.0) [cite: 66]
    [cite_start]def __repr__(self): return f"UserOpinion(Platform='{self.platform}', Text='{self.text[:30]}...')" [cite: 66]

class OnChainEvent(Serializable):
    def __init__(self, timestamp, event_type, value_usd, details, tx_hash=""):
        [cite_start]self.timestamp, self.event_type, self.value_usd, self.details, self.tx_hash = timestamp, event_type, safe_float(value_usd), details, tx_hash [cite: 66]
    [cite_start]def __repr__(self): return f"OnChainEvent(Type='{self.event_type}', Value=${self.value_usd:,.0f})" [cite: 66]

class MacroEconomicEvent(Serializable):
    [cite_start]def __init__(self, timestamp, event_type, details, expected, actual, impact_level='medium'): [cite: 67]
        [cite_start]self.timestamp, self.event_type, self.details, self.expected, self.actual, self.impact_level = timestamp, event_type, details, expected, actual, impact_level [cite: 67]
        self.surprise_factor = self._calculate_surprise()
    def _calculate_surprise(self):
        try:
            return (safe_float(self.actual) - safe_float(self.expected)) / (safe_float(self.expected) if safe_float(self.expected) != 0 else 1)
        except (ZeroDivisionError, TypeError):
            return 0.0
    def __repr__(self):
        return f"MacroEvent(Type='{self.event_type}', Details='{self.details}')"

class Asset:
    """The central data store for a single financial asset."""
    def __init__(self, symbol, asset_type, is_crypto_equity=False):
        [cite_start]self.symbol, self.asset_type, self.is_crypto_equity = symbol, asset_type, is_crypto_equity [cite: 24]
        [cite_start]self.price_history = deque(maxlen=Config.MAX_PRICE_POINTS) [cite: 24]
        [cite_start]self.news_history = deque(maxlen=Config.MAX_NEWS) [cite: 24]
        [cite_start]self.opinion_history = deque(maxlen=Config.MAX_OPINIONS) [cite: 24]
        [cite_start]self.onchain_history = deque(maxlen=Config.MAX_ONCHAIN) if asset_type == 'crypto' else None [cite: 24]
        [cite_start]self.analysis_results = defaultdict(dict) [cite: 24]

    def add_data(self, data_type, data_obj):
        history_map = {
            'price': self.price_history,
            'news': self.news_history,
            'opinion': self.opinion_history,
            'onchain': self.onchain_history
        [cite_start]} [cite: 25]
        if data_type in history_map and history_map[data_type] is not None:
            history_map[data_type].append(data_obj)

    [cite_start]def get_price_dataframe(self): [cite: 25]
        if not self.price_history: return pd.DataFrame()
        df = pd.DataFrame([p.to_dict() for p in self.price_history])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        return df.sort_index()

    def get_news_dataframe(self):
        if not self.news_history: return pd.DataFrame()
        df = pd.DataFrame([n.to_dict() for n in self.news_history])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        return df.sort_index()

    def get_opinion_dataframe(self):
        if not self.opinion_history: return pd.DataFrame()
        df = pd.DataFrame([o.to_dict() for o in self.opinion_history])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        return df.sort_index()

    def get_onchain_dataframe(self):
        if not self.onchain_history: return pd.DataFrame()
        df = pd.DataFrame([o.to_dict() for o in self.onchain_history])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        return df.sort_index()

# --- ABSTRACT BASE CLASSES FOR MODULARITY ---
class BaseEngine(ABC):
    def __init__(self, event_dispatcher, assets):
        self.event_dispatcher = event_dispatcher
        self.assets = assets

    @abstractmethod
    def run_analysis(self, current_timestamp):
        """Executes the analysis logic for the engine."""
        pass

    def handle_event(self, event_type, *args, **kwargs):
        """Default event handler, can be overridden by subclasses."""
        pass

class BaseDataProvider(ABC):
    def __init__(self, event_dispatcher):
        self.event_dispatcher = event_dispatcher
        self._stop_event = threading.Event()
        self._thread = None

    def start(self):
        if self._thread is None or not self._thread.is_alive():
            self._stop_event.clear()
            self._thread = threading.Thread(target=self._run, name=f"{self.__class__.__name__}Thread")
            self._thread.daemon = True
            self._thread.start()
            logger.info(f"{self.__class__.__name__} started.")

    def stop(self):
        self._stop_event.set()
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=5)
            if self._thread.is_alive():
                logger.warning(f"Failed to stop {self.__class__.__name__} thread gracefully.")
        logger.info(f"{self.__class__.__name__} stopped.")

    @abstractmethod
    def _fetch_and_dispatch_data(self):
        """Fetches data and dispatches events."""
        pass

    def _run(self):
        while not self._stop_event.is_set():
            try:
                self._fetch_and_dispatch_data()
                self._stop_event.wait(Config.DATA_PROVIDER_INTERVAL)
            except Exception as e:
                logger.error(f"Error in {self.__class__.__name__} data collection: {e}", exc_info=True)
                self._stop_event.wait(Config.DATA_PROVIDER_INTERVAL * 2) # Wait longer on error

# --- DATA PROVIDERS ---
class MarketDataProvider(BaseDataProvider):
    def __init__(self, event_dispatcher, assets_to_track):
        super().__init__(event_dispatcher)
        self.assets_to_track = assets_to_track
        self._asset_prices = {symbol: deque(maxlen=10) for symbol in assets_to_track} # Simple mock for price history

    def _fetch_and_dispatch_data(self):
        logger.debug("Fetching market data...")
        for symbol in self.assets_to_track:
            # Mock data generation for demonstration
            # In a real system, this would call external APIs (e.g., Binance, Yahoo Finance)
            current_time = get_current_timestamp()
            open_p = random.uniform(50, 50000)
            high_p = open_p * random.uniform(1.005, 1.02)
            low_p = open_p * random.uniform(0.98, 0.995)
            close_p = random.uniform(low_p, high_p)
            volume = random.uniform(1000, 1000000)

            dp = DataPoint(current_time, open_p, high_p, low_p, close_p, volume)
            self.event_dispatcher.dispatch('new_price_data', symbol=symbol, data_point=dp)
            self._asset_prices[symbol].append(close_p) # Keep a small history for mock dependencies

class NewsDataProvider(BaseDataProvider):
    def __init__(self, event_dispatcher, assets_to_track):
        super().__init__(event_dispatcher)
        self.assets_to_track = assets_to_track
        self._mock_headlines = [
            "Global Markets React to Fed's Latest Stance",
            "Bitcoin Reaches New All-Time High Amidst Institutional Adoption",
            "Tech Stocks Face Headwinds as Inflation Concerns Mount",
            "Major Blockchain Breakthrough Announced",
            "Regulatory Scrutiny Intensifies for DeFi Projects",
            "Geopolitical Tensions Drive Investors to Safe Havens",
            "Earnings Season Kicks Off: Mixed Signals for Growth Stocks"
        ]
        self._mock_sources = ["Reuters", "Bloomberg", "CoinDesk", "Wall Street Journal", "Decrypt", "Financial Times"]

    def _fetch_and_dispatch_data(self):
        logger.debug("Fetching news data...")
        if random.random() < 0.7: # Simulate less frequent news
            return

        current_time = get_current_timestamp()
        headline = random.choice(self._mock_headlines)
        source = random.choice(self._mock_sources)
        summary = f"Summary of the news: {headline}. This is a mock summary to illustrate content."
        topics = []
        if "Bitcoin" in headline or "Crypto" in headline or "Blockchain" in headline:
            topics.append("Cryptocurrency")
        if "Stock" in headline or "Market" in headline or "Tech" in headline:
            topics.append("Traditional Finance")
        if "Fed" in headline or "Inflation" in headline or "Geopolitical" in headline:
            topics.append("Macroeconomics")

        na = NewsArticle(current_time, source, headline, summary, topics)
        # Dispatch news relevant to tracked assets, or general market news
        target_asset = random.choice(self.assets_to_track + ["GLOBAL_MARKET_NEWS"])
        self.event_dispatcher.dispatch('new_news_data', symbol=target_asset, news_article=na)

class MacroEconomicDataProvider(BaseDataProvider):
    def __init__(self, event_dispatcher):
        super().__init__(event_dispatcher)
        self._mock_events = [
            ("CPI_REPORT", "Consumer Price Index data released", 0.03, 0.032, 'high'),
            ("FED_RATE_DECISION", "FOMC announces interest rate decision", 0.0025, 0.005, 'critical'),
            ("UNEMPLOYMENT_RATE", "Monthly unemployment figures published", 0.04, 0.038, 'medium'),
            ("GDP_REPORT", "Gross Domestic Product growth report", 0.01, 0.008, 'high'),
            ("TRADE_BALANCE", "International trade balance figures", -50.0, -55.0, 'low'),
            ("ISM_MANUFACTURING_PMI", "Manufacturing PMI index", 52.0, 51.5, 'medium')
        ]

    def _fetch_and_dispatch_data(self):
        logger.debug("Fetching macroeconomic data...")
        if random.random() < 0.85: # Simulate less frequent macro events
            return

        current_time = get_current_timestamp()
        event_type, details, expected, actual, impact_level = random.choice(self._mock_events)
        # Simulate some variability around actual
        actual_val = actual * random.uniform(0.95, 1.05) if isinstance(actual, (int, float)) else actual
        me = MacroEconomicEvent(current_time, event_type, details, expected, actual_val, impact_level)
        self.event_dispatcher.dispatch('new_macro_event', macro_event=me)

class OnChainDataProvider(BaseDataProvider):
    def __init__(self, event_dispatcher, crypto_assets):
        super().__init__(event_dispatcher)
        self.crypto_assets = crypto_assets # Only fetch on-chain data for crypto assets
        self._mock_event_types = ["LARGE_TRANSFER", "EXCHANGE_FLOW", "MINER_ACTIVITY", "DEFI_LIQUIDATION"]

    def _fetch_and_dispatch_data(self):
        logger.debug("Fetching on-chain data...")
        if random.random() < 0.75: # Simulate less frequent on-chain events
            return

        for symbol in self.crypto_assets:
            if random.random() < 0.5: # Simulate not every crypto asset has an event every cycle
                continue

            current_time = get_current_timestamp()
            event_type = random.choice(self._mock_event_types)
            value_usd = random.uniform(100000, 10000000)
            details = f"Mock details for {event_type} on {symbol}"
            tx_hash = ''.join(random.choices('0123456789abcdef', k=64)) # Mock transaction hash

            oe = OnChainEvent(current_time, event_type, value_usd, details, tx_hash)
            self.event_dispatcher.dispatch('new_onchain_event', symbol=symbol, onchain_event=oe)

# --- ANALYSIS ENGINES ---
class TechnicalAnalysisEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_price_data', self.handle_event)

    def handle_event(self, event_type, symbol, data_point):
        asset = self.assets.get(symbol)
        if asset:
            asset.add_data('price', data_point)
            self.run_analysis(symbol)

    def run_analysis(self, symbol):
        asset = self.assets.get(symbol)
        if not asset or len(asset.price_history) < max(Config.SMA_LONG, Config.EMA_LONG, Config.RSI_PERIOD, Config.BOLLINGER_PERIOD, Config.ATR_PERIOD, Config.STOCH_K):
            logger.debug(f"Not enough price data for TA for {symbol}. Needed: {max(Config.SMA_LONG, Config.EMA_LONG, Config.RSI_PERIOD, Config.BOLLINGER_PERIOD, Config.ATR_PERIOD, Config.STOCH_K)}, Have: {len(asset.price_history)}")
            return

        df = asset.get_price_dataframe()
        if df.empty: return

        # Calculate SMAs
        df['SMA_SHORT'] = df['close'].rolling(window=Config.SMA_SHORT).mean()
        df['SMA_LONG'] = df['close'].rolling(window=Config.SMA_LONG).mean()

        # Calculate EMAs
        df['EMA_SHORT'] = df['close'].ewm(span=Config.EMA_SHORT, adjust=False).mean()
        df['EMA_LONG'] = df['close'].ewm(span=Config.EMA_LONG, adjust=False).mean()

        # Calculate RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.ewm(span=Config.RSI_PERIOD, adjust=False).mean()
        avg_loss = loss.ewm(span=Config.RSI_PERIOD, adjust=False).mean()
        rs = avg_gain / avg_loss
        df['RSI'] = 100 - (100 / (1 + rs))

        # Calculate Bollinger Bands
        df['MA_BB'] = df['close'].rolling(window=Config.BOLLINGER_PERIOD).mean()
        df['STD_BB'] = df['close'].rolling(window=Config.BOLLINGER_PERIOD).std()
        df['Upper_BB'] = df['MA_BB'] + (df['STD_BB'] * 2)
        df['Lower_BB'] = df['MA_BB'] - (df['STD_BB'] * 2)

        # Calculate Stochastic Oscillator
        low_min = df['low'].rolling(window=Config.STOCH_K).min()
        high_max = df['high'].rolling(window=Config.STOCH_K).max()
        df['%K'] = (df['close'] - low_min) / (high_max - low_min) * 100
        df['%D'] = df['%K'].rolling(window=Config.STOCH_D).mean()

        # Store latest TA results
        asset.analysis_results['technical'] = {
            'timestamp': df.index[-1].isoformat() + 'Z',
            'SMA_SHORT': df['SMA_SHORT'].iloc[-1],
            'SMA_LONG': df['SMA_LONG'].iloc[-1],
            'EMA_SHORT': df['EMA_SHORT'].iloc[-1],
            'EMA_LONG': df['EMA_LONG'].iloc[-1],
            'RSI': df['RSI'].iloc[-1],
            'Upper_BB': df['Upper_BB'].iloc[-1],
            'Lower_BB': df['Lower_BB'].iloc[-1],
            '%K': df['%K'].iloc[-1],
            '%D': df['%D'].iloc[-1]
        }
        logger.info(f"Technical analysis updated for {symbol}.")
        self.event_dispatcher.dispatch('technical_analysis_updated', symbol=symbol, data=asset.analysis_results['technical'])

    def run_analysis_cycle(self, current_timestamp):
        # This engine is primarily event-driven by 'new_price_data',
        # but this method can be called to force update all assets.
        for symbol in self.assets:
            self.run_analysis(symbol)

class RiskSentimentEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        # Subscribe to relevant data updates for sentiment analysis
        self.event_dispatcher.subscribe('new_news_data', self.handle_event)
        self.event_dispatcher.subscribe('new_opinion_data', self.handle_event)
        self.event_dispatcher.subscribe('new_macro_event', self.handle_event)
        self._global_roro_score = 0.0 # -1 (Risk-Off) to 1 (Risk-On)

    def handle_event(self, event_type, *args, **kwargs):
        # Data points have their own sentiment scores. Aggregate them.
        if event_type == 'new_news_data':
            news_article = kwargs.get('news_article')
            if news_article:
                self._update_sentiment(news_article.sentiment_score, weight=news_article.impact_score)
        elif event_type == 'new_opinion_data':
            user_opinion = kwargs.get('user_opinion')
            if user_opinion:
                self._update_sentiment(user_opinion.sentiment_score, weight=user_opinion.author_influence)
        elif event_type == 'new_macro_event':
            macro_event = kwargs.get('macro_event')
            if macro_event:
                # Macro events impact sentiment based on surprise and impact_level
                sentiment_impact = macro_event.surprise_factor * (1.0 if macro_event.impact_level == 'critical' else (0.5 if macro_event.impact_level == 'high' else 0.2))
                self._update_sentiment(sentiment_impact)

        self.run_analysis(get_current_timestamp()) # Re-evaluate RORO on every relevant event

    def _update_sentiment(self, score, weight=1.0):
        # Simple aggregation, can be enhanced with time-decaying average or more complex models
        self._global_roro_score = (self._global_roro_score * 0.9) + (score * weight * 0.1) # Exponential decay
        self._global_roro_score = np.clip(self._global_roro_score, -1.0, 1.0) # Keep within bounds
        logger.debug(f"Updated global RORO score: {self._global_roro_score:.4f}")

    def run_analysis(self, current_timestamp):
        # This method recalculates the overall RORO score based on aggregated sentiment
        # and dispatches it. The handle_event method drives the updates.
        sentiment_status = "Risk-On" if self._global_roro_score > 0.1 else ("Risk-Off" if self._global_roro_score < -0.1 else "Neutral")
        roro_data = {
            'timestamp': current_timestamp,
            'score': self._global_roro_score,
            'status': sentiment_status
        }
        self.event_dispatcher.dispatch('risk_sentiment_updated', roro_data=roro_data)
        logger.info(f"Risk sentiment updated: {sentiment_status} ({self._global_roro_score:.4f})")

class NarrativeAnalysisEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_news_data', self.handle_event)
        self.event_dispatcher.subscribe('new_opinion_data', self.handle_event)
        self._active_narratives = defaultdict(float) # narrative -> strength
        self._narrative_decay_rate = 0.95 # Decay strength over time

    def handle_event(self, event_type, symbol, **kwargs):
        if event_type == 'new_news_data':
            news_article = kwargs.get('news_article')
            if news_article and news_article.topics:
                for topic in news_article.topics:
                    self._update_narrative_strength(topic, 0.1, news_article.sentiment_score) # Small boost from news
        elif event_type == 'new_opinion_data':
            user_opinion = kwargs.get('user_opinion')
            if user_opinion:
                # Mock topic extraction for opinions
                text = user_opinion.text.lower()
                if "ai" in text or "artificial intelligence" in text:
                    self._update_narrative_strength("AI & Crypto", 0.05, user_opinion.sentiment_score)
                if "defi" in text or "regulation" in text:
                    self._update_narrative_strength("DeFi Regulation", 0.05, user_opinion.sentiment_score)
                if "inflation" in text or "fed" in text:
                    self._update_narrative_strength("Macro Trends", 0.05, user_opinion.sentiment_score)

        self.run_analysis(get_current_timestamp())

    def _update_narrative_strength(self, narrative, base_strength, sentiment_score):
        # Combine base strength with sentiment for more nuanced impact
        self._active_narratives[narrative] += base_strength * (1 + sentiment_score)
        self._active_narratives[narrative] = np.clip(self._active_narratives[narrative], 0.0, 1.0) # Strength 0 to 1

    def run_analysis(self, current_timestamp):
        # Decay old narratives and identify dominant ones
        for narrative in list(self._active_narratives.keys()):
            self._active_narratives[narrative] *= self._narrative_decay_rate
            if self._active_narratives[narrative] < 0.01: # Remove very weak narratives
                del self._active_narratives[narrative]

        # Sort and get top narratives
        sorted_narratives = sorted(self._active_narratives.items(), key=lambda item: item[1], reverse=True)
        dominant_narratives = {n: s for n, s in sorted_narratives[:3]} # Top 3

        narrative_data = {
            'timestamp': current_timestamp,
            'dominant_narratives': dominant_narratives,
            'all_active_narratives': dict(self._active_narratives)
        }
        self.event_dispatcher.dispatch('narrative_analysis_updated', narrative_data=narrative_data)
        logger.info(f"Narrative analysis updated: Dominant Narratives: {dominant_narratives}")

class CryptoEquityBetaEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('technical_analysis_updated', self.handle_event)
        self._crypto_assets = {s: a for s, a in assets.items() if a.asset_type == 'crypto'}
        self._equity_assets = {s: a for s, a in assets.items() if a.asset_type == 'equity' and a.is_crypto_equity}

    def handle_event(self, event_type, symbol, data):
        # This engine needs price data from both crypto and equity to run,
        # so it's better to trigger it on a schedule or after new price data for *all* relevant assets.
        # For simplicity in this mock, we'll just run it when any relevant TA is updated.
        self.run_analysis(get_current_timestamp())

    def run_analysis(self, current_timestamp):
        if not self._crypto_assets or not self._equity_assets:
            return

        beta_results = {}
        # Iterate over crypto-related equities and calculate beta against main crypto assets
        for eq_symbol, eq_asset in self._equity_assets.items():
            eq_df = eq_asset.get_price_dataframe()
            if len(eq_df) < Config.CORRELATION_WINDOWS[1]: # Need enough data for correlation
                continue

            eq_returns = eq_df['close'].pct_change().dropna()

            for crypto_symbol, crypto_asset in self._crypto_assets.items():
                crypto_df = crypto_asset.get_price_dataframe()
                if len(crypto_df) < Config.CORRELATION_WINDOWS[1]:
                    continue
                crypto_returns = crypto_df['close'].pct_change().dropna()

                # Align dataframes by index (timestamps)
                combined_returns = pd.concat([eq_returns.rename('equity_returns'), crypto_returns.rename('crypto_returns')], axis=1).dropna()

                if len(combined_returns) < Config.CORRELATION_WINDOWS[1]:
                    continue

                # Calculate beta: Cov(Equity_Returns, Crypto_Returns) / Var(Crypto_Returns)
                # Using the longer correlation window for more stable beta
                covariance = combined_returns['equity_returns'].cov(combined_returns['crypto_returns'], ddof=0)
                crypto_variance = combined_returns['crypto_returns'].var(ddof=0)

                beta = covariance / crypto_variance if crypto_variance != 0 else 0.0
                beta_results[f"{eq_symbol}_vs_{crypto_symbol}"] = beta
                logger.debug(f"Calculated Beta: {eq_symbol} vs {crypto_symbol} = {beta:.4f}")

        if beta_results:
            self.event_dispatcher.dispatch('crypto_equity_beta_updated', beta_data=beta_results)
            logger.info(f"Crypto-equity beta analysis updated: {beta_results}")

class LaggedEventImpactEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_macro_event', self.handle_event)
        self.event_dispatcher.subscribe('new_news_data', self.handle_event)
        self._event_queue = deque(maxlen=Config.MAX_MACRO * 2) # Store recent events to analyze impact

    def handle_event(self, event_type, **kwargs):
        current_time = datetime.utcnow()
        if event_type == 'new_macro_event':
            macro_event = kwargs.get('macro_event')
            self._event_queue.append({'event': macro_event, 'ingestion_time': current_time, 'type': 'macro'})
        elif event_type == 'new_news_data':
            news_article = kwargs.get('news_article')
            if news_article and news_article.impact_score > 0.5: # Only track high impact news
                self._event_queue.append({'event': news_article, 'ingestion_time': current_time, 'type': 'news'})
        self.run_analysis(get_current_timestamp()) # Run analysis whenever a new event is received

    def run_analysis(self, current_timestamp):
        impact_analysis_results = defaultdict(dict)
        current_dt = datetime.fromisoformat(current_timestamp.replace('Z', '+00:00'))

        for event_entry in list(self._event_queue): # Iterate over a copy as we might modify queue
            event_obj = event_entry['event']
            event_timestamp = datetime.fromisoformat(event_obj.timestamp.replace('Z', '+00:00'))
            ingestion_time = event_entry['ingestion_time']

            # Only analyze events that are old enough to have impact windows
            if (current_dt - ingestion_time) < timedelta(minutes=5): # Give some buffer
                continue

            for symbol, asset in self.assets.items():
                df = asset.get_price_dataframe()
                if df.empty or len(df) < 2:
                    continue

                # Calculate price change over different lagged windows
                for window_hours in [1, 24, 72]:
                    end_time = event_timestamp + timedelta(hours=window_hours)
                    if end_time > current_dt: # Don't analyze future or incomplete windows
                        continue

                    # Find price at event time and end of window
                    price_at_event_series = df['close'].asof(event_timestamp)
                    price_at_end_window_series = df['close'].asof(end_time)

                    price_at_event = price_at_event_series if not pd.isna(price_at_event_series) else None
                    price_at_end_window = price_at_end_window_series if not pd.isna(price_at_end_window_series) else None

                    if price_at_event is not None and price_at_end_window is not None and price_at_event != 0:
                        price_change = (price_at_end_window - price_at_event) / price_at_event
                        if event_obj.details not in impact_analysis_results[symbol]:
                            impact_analysis_results[symbol][event_obj.details] = {}
                        impact_analysis_results[symbol][event_obj.details][f'{window_hours}h_impact'] = price_change

            # Remove event if all its impact windows have been fully analyzed (or it's very old)
            if (current_dt - ingestion_time) > timedelta(hours=72 + 24): # A bit more than longest window
                try:
                    self._event_queue.remove(event_entry)
                except ValueError:
                    pass # Already removed by another thread/call

        if impact_analysis_results:
            self.event_dispatcher.dispatch('lagged_event_impact_updated', impact_data=impact_analysis_results)
            logger.info(f"Lagged event impact analysis updated: {impact_analysis_results}")

class LiquidityFlowEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_price_data', self.handle_event) # Mock order book via price data
        self._mock_order_book = defaultdict(lambda: {'bids': [], 'asks': []})

    def handle_event(self, event_type, symbol, data_point):
        # Simulate simple order book depth based on price and volume
        # This is a very simplistic mock; real liquidity engines parse actual order book data
        mid_price = (data_point.high + data_point.low) / 2
        volume = data_point.volume

        # Mock bids and asks around mid_price
        self._mock_order_book[symbol]['bids'] = [
            {'price': mid_price * (1 - 0.001 * i), 'size': volume * random.uniform(0.01, 0.05)}
            for i in range(5)
        ]
        self._mock_order_book[symbol]['asks'] = [
            {'price': mid_price * (1 + 0.001 * i), 'size': volume * random.uniform(0.01, 0.05)}
            for i in range(5)
        ]
        self.run_analysis(get_current_timestamp()) # Run analysis on every price update

    def run_analysis(self, current_timestamp):
        liquidity_metrics = {}
        for symbol, book in self._mock_order_book.items():
            if not book['bids'] or not book['asks']:
                continue

            # Calculate bid-ask spread
            best_bid = max(b['price'] for b in book['bids'])
            best_ask = min(a['price'] for a in book['asks'])
            spread = best_ask - best_bid
            relative_spread = (spread / best_bid) * 100 if best_bid != 0 else 0

            # Calculate cumulative liquidity at certain depth (e.g., within 0.1% of mid-price)
            mid_price = (best_bid + best_ask) / 2
            bid_liquidity_depth = sum(b['size'] for b in book['bids'] if b['price'] >= mid_price * 0.999)
            ask_liquidity_depth = sum(a['size'] for a in book['asks'] if a['price'] <= mid_price * 1.001)

            liquidity_metrics[symbol] = {
                'timestamp': current_timestamp,
                'bid_ask_spread': spread,
                'relative_spread_pct': relative_spread,
                'bid_liquidity_depth': bid_liquidity_depth,
                'ask_liquidity_depth': ask_liquidity_depth,
                'total_liquidity_depth': bid_liquidity_depth + ask_liquidity_depth
            }
        if liquidity_metrics:
            self.event_dispatcher.dispatch('liquidity_flow_updated', liquidity_data=liquidity_metrics)
            logger.info(f"Liquidity flow analysis updated: {liquidity_metrics}")

# --- GLOBAL CORRELATION ENGINE (Expanded) ---
class CorrelationEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_price_data', self.handle_event) # Trigger on new price data
        self._asset_prices_history = defaultdict(lambda: deque(maxlen=Config.MAX_PRICE_POINTS))

    def handle_event(self, event_type, symbol, data_point):
        # This engine needs data from all assets to run its full analysis,
        # so simply updating history here. The main run_analysis will be called periodically.
        self._asset_prices_history[symbol].append(data_point.close)

    def run_analysis(self, current_timestamp):
        all_dfs = []
        for symbol, asset in self.assets.items():
            df = asset.get_price_dataframe()
            if not df.empty:
                all_dfs.append(df['close'].rename(symbol))

        if not all_dfs:
            return

        combined_prices = pd.concat(all_dfs, axis=1)
        combined_returns = combined_prices.pct_change().dropna()

        correlation_results = {}
        for window in Config.CORRELATION_WINDOWS:
            if len(combined_returns) >= window:
                window_returns = combined_returns.iloc[-window:]
                correlations = window_returns.corr().to_dict()
                correlation_results[f'{window}d_correlations'] = correlations
                logger.debug(f"Calculated {window}d correlations.")

        if correlation_results:
            self.event_dispatcher.dispatch('global_correlation_updated', correlations=correlation_results)
            logger.info(f"Global correlation analysis updated for windows: {Config.CORRELATION_WINDOWS}")

# --- REPORT GENERATION ---
class ReportGenerator:
    def __init__(self, event_dispatcher):
        self.event_dispatcher = event_dispatcher
        self.analysis_cache = {} # Cache for the latest analysis results from various engines

        self.event_dispatcher.subscribe('technical_analysis_updated', self._update_cache)
        self.event_dispatcher.subscribe('risk_sentiment_updated', self._update_cache)
        self.event_dispatcher.subscribe('narrative_analysis_updated', self._update_cache)
        self.event_dispatcher.subscribe('crypto_equity_beta_updated', self._update_cache)
        self.event_dispatcher.subscribe('lagged_event_impact_updated', self._update_cache)
        self.event_dispatcher.subscribe('liquidity_flow_updated', self._update_cache)
        self.event_dispatcher.subscribe('global_correlation_updated', self._update_cache)
        self.event_dispatcher.subscribe('predictive_model_updated', self._update_cache)


    def _update_cache(self, event_type, **kwargs):
        # Extract relevant data based on event type and store in cache
        if event_type == 'technical_analysis_updated':
            symbol = kwargs.get('symbol')
            data = kwargs.get('data')
            if symbol and data:
                self.analysis_cache.setdefault('technical_analysis', {})[symbol] = data
        elif event_type == 'risk_sentiment_updated':
            self.analysis_cache['risk_sentiment'] = kwargs.get('roro_data')
        elif event_type == 'narrative_analysis_updated':
            self.analysis_cache['narratives'] = kwargs.get('narrative_data')
        elif event_type == 'crypto_equity_beta_updated':
            self.analysis_cache['crypto_equity_beta'] = kwargs.get('beta_data')
        elif event_type == 'lagged_event_impact_updated':
            self.analysis_cache['lagged_impact'] = kwargs.get('impact_data')
        elif event_type == 'liquidity_flow_updated':
            self.analysis_cache['liquidity_flow'] = kwargs.get('liquidity_data')
        elif event_type == 'global_correlation_updated':
            self.analysis_cache['correlations'] = kwargs.get('correlations')
        elif event_type == 'predictive_model_updated':
            self.analysis_cache['predictions'] = kwargs.get('prediction_data')

        logger.debug(f"ReportGenerator cache updated by event: {event_type}")

    def generate_financial_report(self, assets_list, global_analysis):
        # This is where the Gemini API integration would be for synthesizing a cohesive report.
        # For now, it will compile and format the available cached data.

        report_sections = []
        report_sections.append("# Institutional-Grade Financial Analysis Report")
        report_sections.append(f"Report Generated: {get_current_timestamp()}\n")

        # Executive Summary (placeholder for Gemini API synthesis)
        report_sections.append("## Executive Summary")
        report_sections.append("*(This section would be dynamically generated by the Gemini API, synthesizing insights from all analysis engines into a coherent, expert-level overview of market conditions, key risks, opportunities, and forward-looking perspectives. It would leverage the detailed prompt mentioned in the Config.)*\n")

        # Market Sentiment
        report_sections.append("## Market Sentiment Analysis")
        roro_data = self.analysis_cache.get('risk_sentiment')
        if roro_data:
            report_sections.append(f"- **Global Risk-On/Risk-Off (RORO) Score**: {roro_data['score']:.4f} ({roro_data['status']})")
            report_sections.append(f"  *Interpretation*: Reflects the prevailing market mood, influencing asset allocation decisions. [cite_start]A higher score indicates increased investor willingness to take on risk, favoring growth assets like technology stocks and cryptocurrencies. [cite: 129] [cite_start]Conversely, a lower score suggests risk aversion, leading investors to safe-haven assets. [cite: 131]")
        else:
            report_sections.append("- No recent Risk-On/Risk-Off (RORO) sentiment data available.")

        narratives = self.analysis_cache.get('narratives')
        if narratives and narratives.get('dominant_narratives'):
            report_sections.append("\n- **Dominant Market Narratives**:")
            for nar, strength in narratives['dominant_narratives'].items():
                report_sections.append(f"  - '{nar}': Strength {strength:.2f}")
            [cite_start]report_sections.append(f"  *Interpretation*: These narratives are currently shaping market discourse and investor behavior, often influencing capital flows into or out of related asset classes. [cite: 47]")
        else:
            report_sections.append("- No dominant market narratives identified.")
        report_sections.append("\n")

        # Asset-Specific Technical Analysis
        report_sections.append("## Asset-Specific Technical Analysis (Latest Data)")
        tech_analysis_data = self.analysis_cache.get('technical_analysis')
        if tech_analysis_data:
            for symbol, data in tech_analysis_data.items():
                report_sections.append(f"### {symbol}")
                report_sections.append(f"- Timestamp: {data.get('timestamp', 'N/A')}")
                report_sections.append(f"- RSI: {data.get('RSI', 'N/A'):.2f} (Overbought > 70, Oversold < 30)")
                report_sections.append(f"- SMA_SHORT ({Config.SMA_SHORT}): {data.get('SMA_SHORT', 'N/A'):,.2f}")
                report_sections.append(f"- SMA_LONG ({Config.SMA_LONG}): {data.get('SMA_LONG', 'N/A'):,.2f}")
                report_sections.append(f"- Bollinger Bands: Upper {data.get('Upper_BB', 'N/A'):,.2f}, Lower {data.get('Lower_BB', 'N/A'):,.2f}")
                # Add more TA indicators as needed
                report_sections.append("")
        else:
            report_sections.append("- No recent technical analysis data available.")
        report_sections.append("\n")

        # Cross-Market Linkages
        report_sections.append("## Cross-Market Linkages & Correlations")
        global_correlations = self.analysis_cache.get('correlations')
        if global_correlations:
            for window, corr_matrix in global_correlations.items():
                report_sections.append(f"### {window} Correlation Matrix (Selected Pairs)")
                # Print a few key correlations, not the whole matrix for brevity
                report_sections.append(f"- BTC vs S&P 500 (implied, using AAPL/GOOG as proxies): {corr_matrix.get('BTC', {}).get('AAPL', 'N/A'):.4f}, {corr_matrix.get('BTC', {}).get('GOOG', 'N/A'):.4f}")
                report_sections.append(f"- BTC vs MSTR (Crypto Equity): {corr_matrix.get('BTC', {}).get('MSTR', 'N/A'):.4f}")
                report_sections.append(f"- ETH vs COIN (Crypto Equity): {corr_matrix.get('ETH', {}).get('COIN', 'N/A'):.4f}")
                [cite_start]report_sections.append(f"  *Interpretation*: Positive correlation indicates assets tend to move in the same direction, reflecting a deeper integration between cryptocurrency and traditional financial markets, especially during periods of market stress. [cite: 133, 301]")
            report_sections.append("")
        else:
            report_sections.append("- No global correlation data available.")

        beta_data = self.analysis_cache.get('crypto_equity_beta')
        if beta_data:
            report_sections.append("### Crypto-Equity Beta (Sensitivity to Crypto Assets)")
            for pair, beta_val in beta_data.items():
                report_sections.append(f"- {pair}: {beta_val:.4f}")
            [cite_start]report_sections.append(f"  *Interpretation*: Beta measures the sensitivity of crypto-related stocks (like MSTR, COIN) to their underlying crypto assets. A beta > 1 suggests the stock is more volatile than the crypto asset it tracks. [cite: 48]")
            report_sections.append("")
        else:
            report_sections.append("- No crypto-equity beta data available.")

        # Macroeconomic Impact Analysis
        report_sections.append("## Macroeconomic Impact Analysis")
        lagged_impact = self.analysis_cache.get('lagged_impact')
        if lagged_impact:
            report_sections.append("Latest Lagged Event Impacts on Asset Prices:")
            for symbol, events in lagged_impact.items():
                report_sections.append(f"### {symbol}")
                for event_details, impacts in events.items():
                    report_sections.append(f"- Event: '{event_details}'")
                    for window, change in impacts.items():
                        report_sections.append(f"  - {window}: {change:.2%}")
                report_sections.append("")
            [cite_start]report_sections.append(f"  *Interpretation*: This analysis quantifies how major macroeconomic events (e.g., CPI reports, Fed decisions) impact asset prices over various time horizons, highlighting the interconnectedness driven by monetary policy and economic shifts. [cite: 171, 174, 49]")
        else:
            report_sections.append("- No recent lagged event impact data available.")
        report_sections.append("\n")

        # Liquidity & On-Chain Flows (for crypto assets)
        report_sections.append("## Liquidity & On-Chain Flow Analysis")
        liquidity_data = self.analysis_cache.get('liquidity_flow')
        if liquidity_data:
            report_sections.append("Latest Liquidity Metrics:")
            for symbol, metrics in liquidity_data.items():
                report_sections.append(f"### {symbol}")
                report_sections.append(f"- Bid-Ask Spread: {metrics.get('bid_ask_spread', 'N/A'):,.4f}")
                report_sections.append(f"- Relative Spread: {metrics.get('relative_spread_pct', 'N/A'):.4f}%")
                report_sections.append(f"- Total Liquidity Depth: {metrics.get('total_liquidity_depth', 'N/A'):,.2f}")
                [cite_start]report_sections.append(f"  *Interpretation*: Measures market depth and ease of trading. Lower spreads and higher liquidity depth generally indicate healthier, more efficient markets. [cite: 50]")
                report_sections.append("")
        else:
            report_sections.append("- No recent liquidity flow data available.")

        report_sections.append("On-Chain Events (last 24h, mock placeholder):")
        # In a real system, OnChainDataProvider would feed specific, categorized on-chain events
        [cite_start]report_sections.append("- No detailed on-chain event summary available in cache yet, but engine tracks Large Transfers, Exchange Flows, Miner Activity, and DeFi Liquidations. [cite: 66]")
        report_sections.append("\n")

        # Predictive Insights (placeholder for Ensemble Model)
        report_sections.append("## Predictive Insights")
        predictions = self.analysis_cache.get('predictions')
        if predictions:
            report_sections.append(f"Latest Market Predictions: {predictions}")
            [cite_start]report_sections.append(f"  *Interpretation*: This section would provide forward-looking market predictions, generated by the ensemble predictive model, considering inputs from all specialized analysis engines. [cite: 53]")
        else:
            report_sections.append("- No predictive model output available yet.")
        report_sections.append("\n")

        # Append the doc file summary as requested.
        report_sections.append("## Relevant Insights from 'Phân tích các mối liên hệ giữa thị trường tiền điện tử và thị trường chứng khoán.docx'")
        [cite_start]report_sections.append("The document 'Phân tích các mối liên hệ giữa thị trường tiền điện tử và thị trường chứng khoán.docx' provides an in-depth analysis of the evolving relationship between the cryptocurrency market and traditional stock markets. [cite: 272]")
        [cite_start]report_sections.append("Key takeaways include: [cite: 275]")
        [cite_start]report_sections.append("- Historically, cryptocurrencies like Bitcoin were seen as uncorrelated assets or 'digital gold,' offering portfolio diversification benefits. [cite: 273]")
        [cite_start]report_sections.append("- Recent years have shown a deeper convergence, challenging these traditional views. [cite: 274]")
        [cite_start]report_sections.append("- Both crypto and stock markets are increasingly sensitive to common underlying factors, particularly risk-on/risk-off sentiment. [cite: 276]")
        [cite_start]report_sections.append("- Central bank monetary policies (interest rates, inflation concerns) significantly impact both asset classes, often synchronously. [cite: 278]")
        [cite_start]report_sections.append("- Institutional capital flows, especially through spot Bitcoin ETFs, have integrated crypto into traditional finance. [cite: 280]")
        [cite_start]report_sections.append("- Publicly listed companies tied to crypto (e.g., Coinbase, MicroStrategy) create direct channels for crypto market performance to reflect in traditional stock prices. [cite: 281]")
        [cite_start]report_sections.append("- Blockchain technology is reshaping traditional financial infrastructure, fostering deeper convergence. [cite: 282]")
        [cite_start]report_sections.append("- The relationship has evolved from uncorrelated to increasingly positively correlated, especially during market stress. [cite: 283]")
        [cite_start]report_sections.append("- This shift necessitates re-evaluation of diversification and risk management strategies for investors and calls for comprehensive regulatory frameworks. [cite: 284]")
        report_sections.append("\n")

        return "\n".join(report_sections)

# --- MAIN FINANCIAL ANALYSIS ENGINE ORCHESTRATOR ---
class FinancialAnalysisEngine:
    def __init__(self, assets_to_track):
        self.event_dispatcher = EventDispatcher()
        self.assets = self._initialize_assets(assets_to_track)
        self._lock = threading.Lock() # For thread-safe access to shared resources

        # Data Providers
        self.market_data_provider = MarketDataProvider(self.event_dispatcher, assets_to_track)
        self.news_data_provider = NewsDataProvider(self.event_dispatcher, assets_to_track)
        self.macro_data_provider = MacroEconomicDataProvider(self.event_dispatcher)
        # Assuming all assets_to_track are potentially crypto for simplicity with on-chain data
        self.onchain_data_provider = OnChainDataProvider(self.event_dispatcher, [s for s, a in self.assets.items() if a.asset_type == 'crypto'])

        # Analysis Engines
        self.technical_analysis_engine = TechnicalAnalysisEngine(self.event_dispatcher, self.assets)
        self.risk_sentiment_engine = RiskSentimentEngine(self.event_dispatcher, self.assets)
        self.narrative_analysis_engine = NarrativeAnalysisEngine(self.event_dispatcher, self.assets)
        self.crypto_equity_beta_engine = CryptoEquityBetaEngine(self.event_dispatcher, self.assets)
        self.lagged_event_impact_engine = LaggedEventImpactEngine(self.event_dispatcher, self.assets)
        self.liquidity_flow_engine = LiquidityFlowEngine(self.event_dispatcher, self.assets)
        self.correlation_engine = CorrelationEngine(self.event_dispatcher, self.assets)

        # Report Generator
        self.report_generator = ReportGenerator(self.event_dispatcher)

        # Global analysis storage (for direct access by report generator if needed, though cache is preferred)
        self.global_analysis = defaultdict(dict)
        self.event_dispatcher.subscribe('risk_sentiment_updated', lambda event_type, roro_data: self._update_global_analysis('risk_sentiment', roro_data))
        self.event_dispatcher.subscribe('narrative_analysis_updated', lambda event_type, narrative_data: self._update_global_analysis('narratives', narrative_data))
        self.event_dispatcher.subscribe('crypto_equity_beta_updated', lambda event_type, beta_data: self._update_global_analysis('crypto_equity_beta', beta_data))
        self.event_dispatcher.subscribe('lagged_event_impact_updated', lambda event_type, impact_data: self._update_global_analysis('lagged_impact', impact_data))
        self.event_dispatcher.subscribe('liquidity_flow_updated', lambda event_type, liquidity_data: self._update_global_analysis('liquidity_flow', liquidity_data))
        self.event_dispatcher.subscribe('global_correlation_updated', lambda event_type, correlations: self._update_global_analysis('correlations', correlations))
        # No direct subscription for 'technical_analysis_updated' to global_analysis, as it's asset-specific and handled by ReportGenerator cache

        self._load_state()

    def _initialize_assets(self, assets_to_track):
        assets = {}
        for symbol in assets_to_track:
            asset_type = 'crypto' if symbol in ['BTC', 'ETH'] else 'equity'
            is_crypto_equity = symbol in ['MSTR', 'COIN'] # Mark these specifically
            assets[symbol] = Asset(symbol, asset_type, is_crypto_equity)
        return assets

    def _update_global_analysis(self, key, data):
        with self._lock:
            self.global_analysis[key] = data

    def _save_state(self):
        state = {
            'assets': {s: {
                'symbol': a.symbol,
                'asset_type': a.asset_type,
                'is_crypto_equity': a.is_crypto_equity,
                'price_history': [dp.to_dict() for dp in a.price_history],
                'news_history': [na.to_dict() for na in a.news_history],
                'opinion_history': [uo.to_dict() for uo in a.opinion_history],
                'onchain_history': [oe.to_dict() for oe in a.onchain_history] if a.onchain_history else []
            } for s, a in self.assets.items()},
            'global_analysis': dict(self.global_analysis) # Convert defaultdict to dict for JSON serialization
        }
        with open(Config.STATE_FILE, 'w') as f:
            json.dump(state, f, indent=4)
        logger.info(f"Engine state saved to {Config.STATE_FILE}")

    def _load_state(self):
        if os.path.exists(Config.STATE_FILE):
            with open(Config.STATE_FILE, 'r') as f:
                state = json.load(f)
            # Reconstruct assets
            for symbol, asset_data in state.get('assets', {}).items():
                asset = Asset(asset_data['symbol'], asset_data['asset_type'], asset_data.get('is_crypto_equity', False))
                asset.price_history.extend([DataPoint.from_dict(dp) for dp in asset_data['price_history']])
                asset.news_history.extend([NewsArticle.from_dict(na) for na in asset_data['news_history']])
                asset.opinion_history.extend([UserOpinion.from_dict(uo) for uo in asset_data['opinion_history']])
                if asset_data.get('onchain_history'):
                    if asset.onchain_history is None: asset.onchain_history = deque(maxlen=Config.MAX_ONCHAIN)
                    asset.onchain_history.extend([OnChainEvent.from_dict(oe) for oe in asset_data['onchain_history']])
                self.assets[symbol] = asset
            self.global_analysis.update(state.get('global_analysis', {}))
            logger.info(f"Engine state loaded from {Config.STATE_FILE}")
        else:
            logger.info("No saved state found. Starting fresh.")

    def start(self):
        logger.info("Financial Analysis Engine starting...")
        self.market_data_provider.start()
        self.news_data_provider.start()
        self.macro_data_provider.start()
        self.onchain_data_provider.start()

        self.simulator = MarketSimulator(self.event_dispatcher, self.assets) # Placeholder for actual simulator start logic
        self.simulator.start()

        try:
            logger.info(f"Waiting for initial data collection ({Config.INITIAL_DATA_WAIT} seconds)...")
            time.sleep(Config.INITIAL_DATA_WAIT)

            while True:
                self.run_analysis_cycle()

                # Generate and display report
                with self._lock:
                    report = self.report_generator.generate_financial_report(
                        list(self.assets.values()),
                        self.global_analysis
                    )
                print("\n" + "="*80)
                print(report)
                print("="*80 + "\n")

                logger.info(f"Next analysis cycle in {Config.ANALYSIS_INTERVAL} seconds.")
                time.sleep(Config.ANALYSIS_INTERVAL)

        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received.")
        finally:
            self.stop()

    def run_analysis_cycle(self):
        current_timestamp = get_current_timestamp()
        logger.info(f"Running analysis cycle at {current_timestamp}")

        # Trigger analysis engines
        self.technical_analysis_engine.run_analysis_cycle(current_timestamp)
        self.risk_sentiment_engine.run_analysis(current_timestamp)
        self.narrative_analysis_engine.run_analysis(current_timestamp)
        self.crypto_equity_beta_engine.run_analysis(current_timestamp)
        self.lagged_event_impact_engine.run_analysis(current_timestamp)
        self.liquidity_flow_engine.run_analysis(current_timestamp)
        self.correlation_engine.run_analysis(current_timestamp)
        # Predictive model would be triggered here as well, leveraging all engine outputs
        # self.predictive_model_engine.run_prediction(current_timestamp) # Assuming a predictive model engine exists

    def stop(self):
        logger.info("Engine shutdown sequence initiated.")
        self.market_data_provider.stop()
        self.news_data_provider.stop()
        self.macro_data_provider.stop()
        self.onchain_data_provider.stop()
        self.simulator.stop() # Stop the simulator as well
        self._save_state()
        logger.info("Financial Analysis Engine has stopped.")

# --- MARKET SIMULATOR (Placeholder/Mock for complex data generation) ---
class MarketSimulator(BaseDataProvider):
    """
    A simulated market data generator that can be influenced by system-wide
    sentiment (e.g., RORO score). This is a mock for a much more complex
    simulation environment in a real v3.0 engine.
    """
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher)
        self.assets = assets
        self.current_prices = {symbol: random.uniform(100, 10000) for symbol in assets}
        self.event_dispatcher.subscribe('risk_sentiment_updated', self.handle_event)
        self.roro_score = 0.0 # Initial neutral RORO

    def handle_event(self, event_type, roro_data):
        self.roro_score = roro_data['score']
        logger.debug(f"Simulator received RORO update: {self.roro_score}")

    def _fetch_and_dispatch_data(self):
        logger.debug("Simulating market data...")
        current_time = get_current_timestamp()

        # Simulate price movement influenced by RORO score
        # Positive RORO means prices tend to go up, negative means down
        for symbol in self.assets:
            base_movement = random.uniform(-0.005, 0.005) # Baseline random movement
            roro_influence = self.roro_score * 0.002 # RORO can shift the movement
            
            # Add a small, asset-specific noise to the influence
            asset_specific_noise = random.uniform(-0.0005, 0.0005)
            
            daily_change_factor = 1 + base_movement + roro_influence + asset_specific_noise
            
            # Ensure price doesn't go negative
            self.current_prices[symbol] = max(1.0, self.current_prices[symbol] * daily_change_factor)

            open_p = self.current_prices[symbol] * random.uniform(0.999, 1.001)
            close_p = self.current_prices[symbol]
            high_p = max(open_p, close_p) * random.uniform(1.0001, 1.002)
            low_p = min(open_p, close_p) * random.uniform(0.998, 0.9999)
            volume = random.uniform(10000, 5000000)

            dp = DataPoint(current_time, open_p, high_p, low_p, close_p, volume)
            self.event_dispatcher.dispatch('new_price_data', symbol=symbol, data_point=dp)


# --- CLI & Entry Point ---
if __name__ == "__main__":
    if '--test' in sys.argv:
        # Unit test suite would be expanded here.
        # For a full v3.0, extensive tests for each engine and data model would be present.
        print("Test suite not fully implemented in this version.")

        # Example of a minimal test
        class TestDataModels(unittest.TestCase):
            def test_data_point_serialization(self):
                dp = DataPoint("2025-06-17T10:00:00Z", 100.0, 105.0, 98.0, 102.0, 1000)
                dp_dict = dp.to_dict()
                self.assertIn('timestamp', dp_dict)
                self.assertEqual(dp_dict['close'], 102.0)
                reconstructed_dp = DataPoint.from_dict(dp_dict)
                self.assertEqual(reconstructed_dp.close, 102.0)

            def test_news_article_sentiment(self):
                na_pos = NewsArticle("2025-06-17T11:00:00Z", "Test", "Market Rallies on Breakthrough News", "Positive outlook")
                self.assertGreater(na_pos.sentiment_score, 0)
                na_neg = NewsArticle("2025-06-17T12:00:00Z", "Test", "Stocks Plunge Amidst Regulation Fears", "Negative news")
                self.assertLess(na_neg.sentiment_score, 0)

        # To run tests, uncomment:
        # unittest.main(argv=['first-arg-is-ignored'], exit=False)

    else:
        engine = FinancialAnalysisEngine(Config.ASSETS_TO_TRACK)
        engine.start()

```
[cite_start]The provided file "teu ai on paper for final brain.txt" has been significantly expanded to illustrate a more complete "Institutional-Grade Financial Analysis Engine" (Teu.py v3.0)[cite: 42].

Here's a breakdown of the expansion, aligning with the original file's described features and extending its functionality:

### Key Additions and Enhancements in Teu.py:

* [cite_start]**Completed Data Models**: The `MacroEconomicEvent` class was completed, and a `Serializable` Abstract Base Class was introduced for consistent data model serialization and deserialization[cite: 62, 67]. [cite_start]The `Asset` class was further fleshed out with methods to retrieve data as Pandas DataFrames, which are essential for analytical operations[cite: 25].
* [cite_start]**Abstract Base Classes for Modularity**: `BaseEngine` and `BaseDataProvider` Abstract Base Classes were defined to enforce a consistent structure and promote modularity for various components of the analysis engine[cite: 45].
* **Expanded Data Providers**:
    * `MarketDataProvider`: Simulates fetching real-time market price data for tracked assets.
    * [cite_start]`NewsDataProvider`: Mocks fetching news articles, including sentiment analysis and topic extraction[cite: 47].
    * `MacroEconomicDataProvider`: Simulates the release of major economic reports (e.g., CPI, Fed decisions) and their initial impact.
    * [cite_start]`OnChainDataProvider`: Mocks fetching on-chain events for crypto assets (e.g., large transfers, exchange flows)[cite: 66].
* **Detailed Analysis Engines**:
    * [cite_start]**TechnicalAnalysisEngine**: Implements calculations for common technical indicators like Simple Moving Averages (SMA), Exponential Moving Averages (EMA), Relative Strength Index (RSI), Bollinger Bands, and Stochastic Oscillator[cite: 59, 60]. This engine processes `new_price_data` events.
    * [cite_start]**RiskSentimentEngine**: Aggregates sentiment from news and user opinions, and incorporates macroeconomic event impact to generate a master Risk-On/Risk-Off (RORO) score[cite: 46]. This score is designed to influence the entire system's analysis, including the Market Simulator.
    * [cite_start]**NarrativeAnalysisEngine**: Identifies and tracks dominant market narratives (e.g., "AI & Crypto", "DeFi Regulation") based on news and user opinions, modeling their strength over time[cite: 47].
    * [cite_start]**CryptoEquityBetaEngine**: Calculates the beta of crypto-related stocks (e.g., MSTR, COIN) against major crypto assets (BTC, ETH), indicating their price sensitivity[cite: 48].
    * [cite_start]**LaggedEventImpactEngine**: Analyzes the market impact of macroeconomic and high-impact news events over various time horizons (1h, 24h, 72h)[cite: 49].
    * [cite_start]**LiquidityFlowEngine**: Simulates order book data and calculates liquidity metrics like bid-ask spread and liquidity depth, providing insights into market efficiency[cite: 50].
    * [cite_start]**CorrelationEngine**: Calculates inter-asset correlations over different lookback periods (e.g., 30-day, 90-day), reflecting the "complex, interconnected relationships between different market factors"[cite: 43, 52].
* **Enhanced Report Generation**: The `ReportGenerator` class now subscribes to all analysis engine updates and compiles the latest insights into a structured financial report. [cite_start]This report includes sections for Market Sentiment, Asset-Specific Technical Analysis, Cross-Market Linkages, Macroeconomic Impact, Liquidity & On-Chain Flows, and Predictive Insights (placeholder)[cite: 55].
* [cite_start]**Market Simulator Integration**: The `MarketSimulator` (a simplified mock) is now integrated to generate price data, with its behavior dynamically influenced by the `RiskSentimentEngine`'s RORO score[cite: 51].
* **Engine Orchestration and State Management**: The `FinancialAnalysisEngine` orchestrates the data providers and analysis engines, running periodic analysis cycles. It also includes methods to save and load the engine's state, allowing for persistence across runs.

### Insights from the Document File Integrated into the Teu.py Expansion:

The "Phân tích các mối liên hệ giữa thị trường tiền điện tử và thị trường chứng khoán.docx" provides crucial context for the Teu.py expansion, particularly for the analysis engines and report generation. The insights from this document are directly reflected in the `teu.py`'s capabilities:

* **Market Sentiment (Risk-on/Risk-off, Fear & Greed Index)**: The `RiskSentimentEngine` and `NarrativeAnalysisEngine` are directly inspired by the document's emphasis on market psychology. [cite_start]The report highlights how RORO sentiment drives both crypto and traditional stock markets and mentions the convergence of Fear & Greed Indexes[cite: 111, 112, 129, 131, 150, 152, 153, 154, 155].
* **Monetary Policy and Macroeconomics (Interest Rates, Inflation, USD Strength)**: The `LaggedEventImpactEngine` and the overall `FinancialAnalysisEngine` are designed to process macroeconomic events and their impact. [cite_start]The report explicitly states that both asset classes are sensitive to central bank policies, interest rates, inflation expectations, and USD strength[cite: 113, 114, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 178, 180, 184, 185, 186, 187].
* [cite_start]**Institutional Capital Flows & Crypto-Related Public Companies**: The document's mention of institutional adoption (e.g., Bitcoin ETFs) [cite: 115, 280] [cite_start]and publicly listed crypto-related companies (e.g., Coinbase, MicroStrategy) [cite: 116, 281] [cite_start]directly informed the inclusion of `MSTR` and `COIN` in `ASSETS_TO_TRACK` and the development of the `CryptoEquityBetaEngine`[cite: 48].
* [cite_start]**Evolving Correlations**: The `CorrelationEngine` specifically addresses the document's core finding about the shifting correlation between cryptocurrencies and traditional stocks from uncorrelated to increasingly positively correlated, particularly during market stress[cite: 283, 289, 290, 299, 300, 301, 319].
* [cite_start]**Blockchain Technology's Role**: The document points out that blockchain technology is not just for crypto but also reshaping traditional finance[cite: 282], aligning with the engine's focus on interconnectedness.

The expanded `teu.py` now represents a more robust and conceptually aligned financial analysis engine, drawing heavily from the research insights provided in the "doc file."