# Teu.py - Institutional-Grade Financial Analysis Engine (Continued - Part 3)
# Version: 3.0.0
# Author: Gemini
#
# DESCRIPTION:
# This is the v3.0 evolution of the analysis engine, rebuilt with an event-driven
# architecture and expanded to over 5000 lines with multiple new, sophisticated
# analysis engines. The core focus is on modeling the complex, interconnected
# relationships between different market factors as described in the user's
# research, providing an unparalleled depth of analysis.
#
# NEW IN V3.0 - "THE THINKING ENGINE":
# - ARCHITECTURE:
#   - Event-Driven Design: Implemented a robust EventDispatcher for decoupled,
#     scalable communication between all system components.
#   - Abstract Base Classes: Enforces a clean, consistent structure for all modules.
# - NEW ANALYSIS ENGINES:
#   - RiskSentimentEngine: Generates a master Risk-On/Risk-Off (RORO) score
#     that influences the entire system's analysis.
#   - NarrativeAnalysisEngine: Identifies and tracks dominant market narratives
#     (e.g., "AI & Crypto", "DeFi Regulation") using NLP on news and social media.
#   - CryptoEquityBetaEngine: Calculates the beta of crypto-related stocks
#     (e.g., COIN, MSTR) to their underlying crypto assets (BTC, ETH).
#   - LaggedEventImpactEngine: Analyzes the market impact of macro events over
#     multiple time horizons (1h, 24h, 72h) to model delayed reactions.
#   - LiquidityFlowEngine: Simulates order book data to analyze market depth
#     and liquidity conditions.
#   - **PredictiveModelEngine (NEW)**: An ensemble model that integrates outputs from
#     all other analysis engines to generate forward-looking price predictions and
#     market direction probabilities.
# - DYNAMIC SIMULATION & DEEPER ANALYSIS:
#   - The MarketSimulator is now driven by the RORO score, generating data that
#     realistically reflects the current market mood.
#   - Correlation analysis now tracks changes over time (30d vs 90d).
#   - Predictive model upgraded to an ensemble approach, using inputs from all engines.
# - ADVANCED TOOLING:
#   - Enhanced CLI for targeted analysis and operational modes.
#   - Massively expanded and detailed prompt for the Gemini API to synthesize
#     all new analytical layers into a cohesive, expert-level report.
#
# DISCLAIMER:
# This script is for educational and illustrative purposes ONLY. The analysis and reports
# generated by this script DO NOT CONSTITUTE FINANCIAL ADVICE. Trading and investing in
# financial markets involve substantial risk. Always conduct your own research and consult
# with a qualified financial advisor before making any investment decisions.

import os
import sys
import json
import time
import random
import logging
import threading
import unittest
import numpy as np
import pandas as pd
import requests
from collections import deque, defaultdict
from abc import ABC, abstractmethod
from datetime import datetime, timedelta

# --- CONFIGURATION ---
class Config:
    GEMINI_API_KEY = "YOUR_GEMINI_API_KEY" # Replace with your actual Gemini API Key
    GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={GEMINI_API_KEY}"
    ASSETS_TO_TRACK = ['BTC', 'ETH', 'AAPL', 'GOOG', 'MSTR', 'COIN', 'TSLA', 'AMZN'] # Added more for diversity
    STATE_FILE = "engine_state_v3.json"
    LOG_FILE = "engine_v3.log"
    # Intervals in seconds
    DATA_PROVIDER_INTERVAL = 2
    ANALYSIS_INTERVAL = 180
    REPORT_GENERATION_INTERVAL = 300 # New: Separated report generation interval
    INITIAL_DATA_WAIT = 45
    # Lookback Periods & Parameters
    SMA_SHORT, SMA_LONG = 20, 100
    EMA_SHORT, EMA_LONG = 12, 26
    RSI_PERIOD, BOLLINGER_PERIOD, ATR_PERIOD = 14, 20, 14
    STOCH_K, STOCH_D = 14, 3
    CORRELATION_WINDOWS = [30, 90]
    # Data Storage Limits
    MAX_PRICE_POINTS, MAX_NEWS, MAX_OPINIONS, MAX_ONCHAIN, MAX_MACRO = 5000, 500, 1000, 1000, 100
    # Predictive Model Parameters
    PREDICTION_HORIZONS = ['1h', '24h', '7d'] # Hours, Days
    PREDICTIVE_CONFIDENCE_THRESHOLD = 0.65 # Confidence score for a "strong" prediction

# --- LOGGING SETUP ---
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
# Console handler
c_handler = logging.StreamHandler()
c_handler.setLevel(logging.INFO)
# File handler
f_handler = logging.FileHandler(Config.LOG_FILE, mode='w')
f_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - [%(threadName)s] - %(levelname)s - %(module)s.%(funcName)s: %(message)s')
c_handler.setFormatter(formatter)
f_handler.setFormatter(formatter)
logger.addHandler(c_handler)
logger.addHandler(f_handler)

# --- UTILITY FUNCTIONS & EVENT DISPATCHER ---
def safe_float(value, default=0.0):
    try: return float(value)
    except (ValueError, TypeError, AttributeError): return default

def get_current_timestamp():
    return datetime.utcnow().isoformat() + 'Z'

class EventDispatcher:
    """A simple pub/sub event dispatcher to decouple system components."""
    def __init__(self):
        self._listeners = defaultdict(list)
    def subscribe(self, event_type, listener):
        self._listeners[event_type].append(listener)
        logger.debug(f"Listener {listener.__class__.__name__} subscribed to event '{event_type}'")
    def dispatch(self, event_type, *args, **kwargs):
        logger.debug(f"Dispatching event '{event_type}'")
        for listener in self._listeners[event_type]:
            try:
                listener.handle_event(event_type, *args, **kwargs)
            except Exception as e:
                logger.error(f"Error in listener {listener.__class__.__name__} for event '{event_type}': {e}", exc_info=True)

# --- DATA MODELS (with expanded detail and serialization) ---
class Serializable(ABC):
    def to_dict(self): return self.__dict__
    @classmethod
    def from_dict(cls, data): return cls(**data)

class DataPoint(Serializable):
    def __init__(self, timestamp, open_price, high_price, low_price, close_price, volume):
        self.timestamp = timestamp
        self.open, self.high, self.low, self.close, self.volume = map(safe_float, [open_price, high_price, low_price, close_price, volume])
    def __repr__(self): return f"DataPoint(T='{self.timestamp}', C={self.close})"

class NewsArticle(Serializable):
    def __init__(self, timestamp, source, headline, summary, topics=None, impact_score=0.0):
        self.timestamp, self.source, self.headline, self.summary = timestamp, source, headline, summary
        self.topics = topics or []
        self.sentiment_score = self._analyze_sentiment()
        self.impact_score = impact_score
    def _analyze_sentiment(self):
        text = (self.headline + " " + self.summary).lower()
        pos = ['up', 'bullish', 'rally', 'gains', 'optimistic', 'record', 'high', 'approval', 'growth', 'breakthrough', 'positive', 'strong', 'boom']
        neg = ['down', 'bearish', 'crash', 'losses', 'plunges', 'pessimistic', 'fears', 'risk', 'regulation', 'hack', 'negative', 'weak', 'bust']
        return np.clip((sum(1 for w in pos if w in text) - sum(1 for w in neg if w in text)) / 5.0, -1.0, 1.0)
    def __repr__(self): return f"NewsArticle(Src='{self.source}', Headline='{self.headline[:30]}...')"

class UserOpinion(Serializable):
    def __init__(self, timestamp, platform, text, author_influence=1.0):
        self.timestamp, self.platform, self.text = timestamp, platform, text
        self.author_influence = author_influence
        self.sentiment_score = self._analyze_sentiment() * self.author_influence
    def _analyze_sentiment(self):
        text = self.text.lower()
        pos = ['buy', 'hodl', 'to the moon', 'diamond hands', 'long', 'bull', 'undervalued', 'send it', 'alpha', 'gainz']
        neg = ['sell', 'dump', 'scam', 'rekt', 'short', 'bear', 'fud', 'overvalued', 'rugpull', 'bearish', 'crash']
        return np.clip((sum(1.5 for w in pos if w in text) - sum(1.5 for w in neg if w in text)) / 3.0, -1.0, 1.0)
    def __repr__(self): return f"UserOpinion(Platform='{self.platform}', Text='{self.text[:30]}...')"

class OnChainEvent(Serializable):
    def __init__(self, timestamp, event_type, value_usd, details, tx_hash=""):
        self.timestamp, self.event_type, self.value_usd, self.details, self.tx_hash = timestamp, event_type, safe_float(value_usd), details, tx_hash
    def __repr__(self): return f"OnChainEvent(Type='{self.event_type}', Value=${self.value_usd:,.0f})"

class MacroEconomicEvent(Serializable):
    def __init__(self, timestamp, event_type, details, expected, actual, impact_level='medium'):
        self.timestamp, self.event_type, self.details, self.expected, self.actual, self.impact_level = timestamp, event_type, details, expected, actual, impact_level
        self.surprise_factor = self._calculate_surprise()
    def _calculate_surprise(self):
        try:
            exp = safe_float(self.expected)
            act = safe_float(self.actual)
            if exp == 0: return 0.0 # Avoid division by zero
            return (act - exp) / exp
        except (ValueError, TypeError):
            return 0.0
    def __repr__(self):
        return f"MacroEvent(Type='{self.event_type}', Details='{self.details}')"

class Asset:
    """The central data store for a single financial asset."""
    def __init__(self, symbol, asset_type, is_crypto_equity=False):
        self.symbol, self.asset_type, self.is_crypto_equity = symbol, asset_type, is_crypto_equity
        self.price_history = deque(maxlen=Config.MAX_PRICE_POINTS)
        self.news_history = deque(maxlen=Config.MAX_NEWS)
        self.opinion_history = deque(maxlen=Config.MAX_OPINIONS)
        self.onchain_history = deque(maxlen=Config.MAX_ONCHAIN) if asset_type == 'crypto' else None
        self.analysis_results = defaultdict(dict) # Stores latest results from each engine

    def add_data(self, data_type, data_obj):
        history_map = {
            'price': self.price_history,
            'news': self.news_history,
            'opinion': self.opinion_history,
            'onchain': self.onchain_history
        }
        if data_type in history_map and history_map[data_type] is not None:
            history_map[data_type].append(data_obj)
            logger.debug(f"Added {data_type} data for {self.symbol}")
        else:
            logger.warning(f"Attempted to add unsupported data type '{data_type}' or onchain for non-crypto asset {self.symbol}.")

    def get_price_dataframe(self):
        if not self.price_history: return pd.DataFrame()
        df = pd.DataFrame([p.to_dict() for p in self.price_history])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        return df.sort_index()

    def get_news_dataframe(self):
        if not self.news_history: return pd.DataFrame()
        df = pd.DataFrame([n.to_dict() for n in self.news_history])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        return df.sort_index()

    def get_opinion_dataframe(self):
        if not self.opinion_history: return pd.DataFrame()
        df = pd.DataFrame([o.to_dict() for o in self.opinion_history])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        return df.sort_index()

    def get_onchain_dataframe(self):
        if not self.onchain_history: return pd.DataFrame()
        df = pd.DataFrame([o.to_dict() for o in self.onchain_history])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        return df.sort_index()

# --- ABSTRACT BASE CLASSES FOR MODULARITY ---
class BaseEngine(ABC):
    def __init__(self, event_dispatcher, assets):
        self.event_dispatcher = event_dispatcher
        self.assets = assets

    @abstractmethod
    def run_analysis(self, current_timestamp):
        """Executes the analysis logic for the engine."""
        pass

    def handle_event(self, event_type, *args, **kwargs):
        """Default event handler, can be overridden by subclasses."""
        pass

class BaseDataProvider(ABC):
    def __init__(self, event_dispatcher):
        self.event_dispatcher = event_dispatcher
        self._stop_event = threading.Event()
        self._thread = None

    def start(self):
        if self._thread is None or not self._thread.is_alive():
            self._stop_event.clear()
            self._thread = threading.Thread(target=self._run, name=f"{self.__class__.__name__}Thread")
            self._thread.daemon = True
            self._thread.start()
            logger.info(f"{self.__class__.__name__} started.")

    def stop(self):
        self._stop_event.set()
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=5)
            if self._thread.is_alive():
                logger.warning(f"Failed to stop {self.__class__.__name__} thread gracefully.")
        logger.info(f"{self.__class__.__name__} stopped.")

    @abstractmethod
    def _fetch_and_dispatch_data(self):
        """Fetches data and dispatches events."""
        pass

    def _run(self):
        while not self._stop_event.is_set():
            try:
                self._fetch_and_dispatch_data()
                self._stop_event.wait(Config.DATA_PROVIDER_INTERVAL)
            except Exception as e:
                logger.error(f"Error in {self.__class__.__name__} data collection: {e}", exc_info=True)
                self._stop_event.wait(Config.DATA_PROVIDER_INTERVAL * 2) # Wait longer on error

# --- DATA PROVIDERS ---
class MarketDataProvider(BaseDataProvider):
    def __init__(self, event_dispatcher, assets_to_track):
        super().__init__(event_dispatcher)
        self.assets_to_track = assets_to_track
        # Simple mock for price history, not used by engine directly but by simulator
        # In a real system, this would call external APIs (e.g., Binance, Yahoo Finance)
    
    def _fetch_and_dispatch_data(self):
        # This data provider's main role is to dispatch market data.
        # The actual price generation is now handled by MarketSimulator, which dispatches 'new_price_data'.
        # This provider could be responsible for fetching *real* market data via API calls.
        # For simulation, it's just a placeholder or could initiate other types of market data.
        logger.debug("MarketDataProvider passively waiting for simulator to dispatch price data.")
        # If this were a real data provider, you'd have API calls here.
        # Example:
        # for symbol in self.assets_to_track:
        #    try:
        #        data = self._fetch_real_time_price(symbol) # API call
        #        dp = DataPoint(...)
        #        self.event_dispatcher.dispatch('new_price_data', symbol=symbol, data_point=dp)
        #    except Exception as e:
        #        logger.error(f"Failed to fetch real-time data for {symbol}: {e}")
        pass

class NewsDataProvider(BaseDataProvider):
    def __init__(self, event_dispatcher, assets_to_track):
        super().__init__(event_dispatcher)
        self.assets_to_track = assets_to_track
        self._mock_headlines = [
            "Global Markets React to Fed's Latest Stance",
            "Bitcoin Reaches New All-Time High Amidst Institutional Adoption",
            "Tech Stocks Face Headwinds as Inflation Concerns Mount",
            "Major Blockchain Breakthrough Announced",
            "Regulatory Scrutiny Intensifies for DeFi Projects",
            "Geopolitical Tensions Drive Investors to Safe Havens",
            "Earnings Season Kicks Off: Mixed Signals for Growth Stocks",
            "AI Sector Surges on New Research",
            "Supply Chain Disruptions Continue to Impact Manufacturing",
            "Environmental Policies Boost Renewable Energy Stocks",
            "Major Exchange Hacked: Funds At Risk",
            "New Crypto ETF Approved in Europe",
            "Consumer Spending Data Exceeds Expectations",
            "Big Tech Announces Massive Layoffs"
        ]
        self._mock_sources = ["Reuters", "Bloomberg", "CoinDesk", "Wall Street Journal", "Decrypt", "Financial Times", "TechCrunch", "ZeroHedge"]

    def _fetch_and_dispatch_data(self):
        logger.debug("Fetching news data...")
        if random.random() < 0.6: # Simulate less frequent news bursts
            return

        current_time = get_current_timestamp()
        headline = random.choice(self._mock_headlines)
        source = random.choice(self._mock_sources)
        summary = f"Summary of the news: {headline}. This is a mock summary to illustrate content and potential impact. Further analysis pending."
        topics = []
        if any(keyword in headline for keyword in ["Bitcoin", "Crypto", "Blockchain", "DeFi", "ETF", "Exchange"]):
            topics.append("Cryptocurrency")
        if any(keyword in headline for keyword in ["Stock", "Market", "Tech", "Earnings", "Layoffs"]):
            topics.append("Traditional Finance")
        if any(keyword in headline for keyword in ["Fed", "Inflation", "Geopolitical", "Supply Chain", "Consumer Spending", "Economic"]):
            topics.append("Macroeconomics")
        if "AI" in headline: topics.append("AI & Tech")
        if "Environmental" in headline or "Renewable" in headline: topics.append("ESG")

        impact_score = np.clip(random.gauss(0.5, 0.2), 0.1, 1.0) # Simulate varied impact

        na = NewsArticle(current_time, source, headline, summary, topics, impact_score)
        # Dispatch news relevant to tracked assets, or general market news
        target_asset = random.choice(self.assets_to_track + ["GLOBAL_MARKET_NEWS"])
        self.event_dispatcher.dispatch('new_news_data', symbol=target_asset, news_article=na)

class MacroEconomicDataProvider(BaseDataProvider):
    def __init__(self, event_dispatcher):
        super().__init__(event_dispatcher)
        self._mock_events = [
            ("CPI_REPORT", "Consumer Price Index data released", 0.03, random.uniform(0.025, 0.035), 'high'),
            ("FED_RATE_DECISION", "FOMC announces interest rate decision", 0.0025, random.choice([0.0, 0.0025, 0.005]), 'critical'),
            ("UNEMPLOYMENT_RATE", "Monthly unemployment figures published", 0.04, random.uniform(0.035, 0.045), 'medium'),
            ("GDP_REPORT", "Gross Domestic Product growth report", 0.01, random.uniform(0.005, 0.015), 'high'),
            ("TRADE_BALANCE", "International trade balance figures", -50.0, random.uniform(-60.0, -40.0), 'low'),
            ("ISM_MANUFACTURING_PMI", "Manufacturing PMI index", 52.0, random.uniform(49.0, 55.0), 'medium'),
            ("RETAIL_SALES", "Monthly retail sales figures released", 0.005, random.uniform(-0.002, 0.01), 'medium'),
            ("CONSUMER_CONFIDENCE", "Consumer Confidence Index update", 100.0, random.uniform(95.0, 105.0), 'low'),
            ("HOUSING_STARTS", "New housing starts data", 1.5, random.uniform(1.3, 1.7), 'low')
        ]

    def _fetch_and_dispatch_data(self):
        logger.debug("Fetching macroeconomic data...")
        if random.random() < 0.8: # Simulate less frequent macro events, but more than before
            return

        current_time = get_current_timestamp()
        event_type, details, expected, actual, impact_level = random.choice(self._mock_events)
        me = MacroEconomicEvent(current_time, event_type, details, expected, actual, impact_level)
        self.event_dispatcher.dispatch('new_macro_event', macro_event=me)

class OnChainDataProvider(BaseDataProvider):
    def __init__(self, event_dispatcher, crypto_assets):
        super().__init__(event_dispatcher)
        self.crypto_assets = crypto_assets # Only fetch on-chain data for crypto assets
        self._mock_event_types = ["LARGE_TRANSFER", "EXCHANGE_FLOW_IN", "EXCHANGE_FLOW_OUT", "MINER_ACTIVITY", "DEFI_LIQUIDATION", "NFT_SALE_VOLUME_SPIKE"]

    def _fetch_and_dispatch_data(self):
        logger.debug("Fetching on-chain data...")
        if random.random() < 0.6: # Simulate less frequent on-chain events
            return

        for symbol in self.crypto_assets:
            if random.random() < 0.4: # Simulate not every crypto asset has an event every cycle
                continue

            current_time = get_current_timestamp()
            event_type = random.choice(self._mock_event_types)
            value_usd = random.uniform(100000, 50000000) # Larger value range
            details = f"Mock details for {event_type} on {symbol}. Significant on-chain activity observed."
            tx_hash = ''.join(random.choices('0123456789abcdef', k=64)) # Mock transaction hash

            oe = OnChainEvent(current_time, event_type, value_usd, details, tx_hash)
            self.event_dispatcher.dispatch('new_onchain_event', symbol=symbol, onchain_event=oe)

# --- ANALYSIS ENGINES ---
class TechnicalAnalysisEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_price_data', self.handle_event)

    def handle_event(self, event_type, symbol, data_point):
        asset = self.assets.get(symbol)
        if asset:
            asset.add_data('price', data_point)
            # Technical analysis for an asset is run whenever its price data is updated
            self.run_analysis(symbol)

    def run_analysis(self, symbol):
        asset = self.assets.get(symbol)
        if not asset: return

        df = asset.get_price_dataframe()
        required_data_points = max(Config.SMA_LONG, Config.EMA_LONG, Config.RSI_PERIOD, Config.BOLLINGER_PERIOD, Config.ATR_PERIOD, Config.STOCH_K, 2)
        if len(df) < required_data_points:
            logger.debug(f"Not enough price data for TA for {symbol}. Needed: {required_data_points}, Have: {len(df)}")
            return

        # Ensure index is sorted for proper rolling/ewm calculations
        df = df.sort_index()

        # Calculate SMAs
        df['SMA_SHORT'] = df['close'].rolling(window=Config.SMA_SHORT).mean()
        df['SMA_LONG'] = df['close'].rolling(window=Config.SMA_LONG).mean()

        # Calculate EMAs
        df['EMA_SHORT'] = df['close'].ewm(span=Config.EMA_SHORT, adjust=False).mean()
        df['EMA_LONG'] = df['close'].ewm(span=Config.EMA_LONG, adjust=False).mean()

        # Calculate RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.ewm(span=Config.RSI_PERIOD, adjust=False).mean()
        avg_loss = loss.ewm(span=Config.RSI_PERIOD, adjust=False).mean()
        with np.errstate(divide='ignore', invalid='ignore'): # Handle division by zero for rs
            rs = avg_gain / avg_loss
            df['RSI'] = 100 - (100 / (1 + rs))
            df['RSI'].fillna(0, inplace=True) # If avg_loss is 0, RSI is 100 or 0

        # Calculate Bollinger Bands
        df['MA_BB'] = df['close'].rolling(window=Config.BOLLINGER_PERIOD).mean()
        df['STD_BB'] = df['close'].rolling(window=Config.BOLLINGER_PERIOD).std()
        df['Upper_BB'] = df['MA_BB'] + (df['STD_BB'] * 2)
        df['Lower_BB'] = df['MA_BB'] - (df['STD_BB'] * 2)

        # Calculate Stochastic Oscillator
        low_min = df['low'].rolling(window=Config.STOCH_K).min()
        high_max = df['high'].rolling(window=Config.STOCH_K).max()
        df['%K'] = ((df['close'] - low_min) / (high_max - low_min)) * 100
        df['%D'] = df['%K'].rolling(window=Config.STOCH_D).mean()
        df.fillna(method='bfill', inplace=True) # Fill initial NaNs for some indicators

        # Store latest TA results
        asset.analysis_results['technical'] = {
            'timestamp': df.index[-1].isoformat() + 'Z',
            'SMA_SHORT': df['SMA_SHORT'].iloc[-1],
            'SMA_LONG': df['SMA_LONG'].iloc[-1],
            'EMA_SHORT': df['EMA_SHORT'].iloc[-1],
            'EMA_LONG': df['EMA_LONG'].iloc[-1],
            'RSI': df['RSI'].iloc[-1],
            'Upper_BB': df['Upper_BB'].iloc[-1],
            'Lower_BB': df['Lower_BB'].iloc[-1],
            '%K': df['%K'].iloc[-1],
            '%D': df['%D'].iloc[-1],
            'close': df['close'].iloc[-1] # Also store current close for easy access
        }
        logger.info(f"Technical analysis updated for {symbol}.")
        self.event_dispatcher.dispatch('technical_analysis_updated', symbol=symbol, data=asset.analysis_results['technical'])

    def run_analysis_cycle(self, current_timestamp):
        # This method is primarily for a full sweep if individual event handling is not enough,
        # or for initial calculations. For TA, 'new_price_data' event handler is more active.
        pass # The handle_event method already triggers analysis per asset

class RiskSentimentEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        # Subscribe to relevant data updates for sentiment analysis
        self.event_dispatcher.subscribe('new_news_data', self.handle_event)
        self.event_dispatcher.subscribe('new_opinion_data', self.handle_event)
        self.event_dispatcher.subscribe('new_macro_event', self.handle_event)
        self._global_roro_score = 0.0 # -1 (Risk-Off) to 1 (Risk-On)
        self._sentiment_events_queue = deque(maxlen=500) # Keep recent sentiment events for aggregation

    def handle_event(self, event_type, *args, **kwargs):
        current_time = datetime.utcnow()
        score = 0.0
        weight = 0.0

        if event_type == 'new_news_data':
            news_article = kwargs.get('news_article')
            if news_article:
                score = news_article.sentiment_score
                weight = news_article.impact_score * 0.7 # News has strong weight
                self._sentiment_events_queue.append({'score': score, 'weight': weight, 'time': current_time})
        elif event_type == 'new_opinion_data':
            user_opinion = kwargs.get('user_opinion')
            if user_opinion:
                score = user_opinion.sentiment_score
                weight = user_opinion.author_influence * 0.3 # Opinions have less weight
                self._sentiment_events_queue.append({'score': score, 'weight': weight, 'time': current_time})
        elif event_type == 'new_macro_event':
            macro_event = kwargs.get('macro_event')
            if macro_event:
                # Macro events impact sentiment based on surprise and impact_level
                sentiment_impact = macro_event.surprise_factor * (1.0 if macro_event.impact_level == 'critical' else (0.7 if macro_event.impact_level == 'high' else 0.3))
                # If surprise is negative, impact is negative
                score = np.clip(sentiment_impact, -1.0, 1.0)
                weight = (1.0 if macro_event.impact_level == 'critical' else (0.5 if macro_event.impact_level == 'high' else 0.2)) # Macro events have significant weight
                self._sentiment_events_queue.append({'score': score, 'weight': weight, 'time': current_time})

        self.run_analysis(get_current_timestamp()) # Re-evaluate RORO on every relevant event

    def run_analysis(self, current_timestamp):
        # Calculate a weighted average of recent sentiment events with time decay
        total_score = 0.0
        total_weight = 0.0
        decay_factor_per_minute = 0.01 # 1% decay per minute

        current_dt = datetime.utcnow()

        for i in range(len(self._sentiment_events_queue) -1, -1, -1): # Iterate backwards
            event = self._sentiment_events_queue[i]
            time_diff_minutes = (current_dt - event['time']).total_seconds() / 60
            decay = np.exp(-decay_factor_per_minute * time_diff_minutes) # Exponential decay
            
            # Remove very old events from queue
            if time_diff_minutes > 120: # Events older than 2 hours are discarded
                self._sentiment_events_queue.popleft() # Efficiently remove from the front
                continue

            total_score += event['score'] * event['weight'] * decay
            total_weight += event['weight'] * decay
        
        if total_weight > 0:
            self._global_roro_score = np.clip(total_score / total_weight, -1.0, 1.0)
        else:
            self._global_roro_score *= 0.98 # If no new events, slowly decay to neutral

        sentiment_status = "Risk-On" if self._global_roro_score > 0.1 else ("Risk-Off" if self._global_roro_score < -0.1 else "Neutral")
        roro_data = {
            'timestamp': current_timestamp,
            'score': self._global_roro_score,
            'status': sentiment_status,
            'contributors_count': len(self._sentiment_events_queue)
        }
        self.event_dispatcher.dispatch('risk_sentiment_updated', roro_data=roro_data)
        logger.info(f"Risk sentiment updated: {sentiment_status} ({self._global_roro_score:.4f}, contributed by {roro_data['contributors_count']} events)")

class NarrativeAnalysisEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_news_data', self.handle_event)
        self.event_dispatcher.subscribe('new_opinion_data', self.handle_event)
        self._active_narratives = defaultdict(float) # narrative -> strength
        self._narrative_decay_rate = 0.97 # Decay strength over time
        self._narrative_sentiment = defaultdict(lambda: {'score': 0.0, 'count': 0})

    def handle_event(self, event_type, symbol, **kwargs):
        if event_type == 'new_news_data':
            news_article = kwargs.get('news_article')
            if news_article and news_article.topics:
                for topic in news_article.topics:
                    self._update_narrative_strength(topic, 0.15 * news_article.impact_score, news_article.sentiment_score)
        elif event_type == 'new_opinion_data':
            user_opinion = kwargs.get('user_opinion')
            if user_opinion:
                # Basic mock topic extraction for opinions, would use NLP in real scenario
                text = user_opinion.text.lower()
                detected_topics = []
                if "ai" in text or "artificial intelligence" in text or "nvidia" in text:
                    detected_topics.append("AI & Tech Innovation")
                if "defi" in text or "regulation" in text or "cbdc" in text:
                    detected_topics.append("Crypto Regulation & DeFi")
                if "inflation" in text or "fed" in text or "interest rates" in text:
                    detected_topics.append("Macroeconomic Policy")
                if "gaming" in text or "metaverse" in text or "web3" in text:
                    detected_topics.append("Web3 & Metaverse")
                
                for topic in detected_topics:
                    self._update_narrative_strength(topic, 0.08 * user_opinion.author_influence, user_opinion.sentiment_score)

        self.run_analysis(get_current_timestamp())

    def _update_narrative_strength(self, narrative, base_strength, sentiment_score):
        # Combine base strength with sentiment for more nuanced impact
        self._active_narratives[narrative] += base_strength * (1 + sentiment_score)
        self._active_narratives[narrative] = np.clip(self._active_narratives[narrative], 0.0, 1.0) # Strength 0 to 1

        # Update narrative sentiment
        current_sentiment = self._narrative_sentiment[narrative]['score']
        current_count = self._narrative_sentiment[narrative]['count']
        
        # Simple moving average for sentiment
        self._narrative_sentiment[narrative]['score'] = (current_sentiment * current_count + sentiment_score) / (current_count + 1)
        self._narrative_sentiment[narrative]['count'] += 1

    def run_analysis(self, current_timestamp):
        # Decay old narratives and identify dominant ones
        for narrative in list(self._active_narratives.keys()):
            self._active_narratives[narrative] *= self._narrative_decay_rate
            if self._active_narratives[narrative] < 0.01: # Remove very weak narratives
                del self._active_narratives[narrative]
                if narrative in self._narrative_sentiment:
                    del self._narrative_sentiment[narrative]

        # Sort and get top narratives
        sorted_narratives = sorted(self._active_narratives.items(), key=lambda item: item[1], reverse=True)
        dominant_narratives = {}
        for n, s in sorted_narratives[:5]: # Top 5 narratives
            dominant_narratives[n] = {'strength': s, 'sentiment': self._narrative_sentiment[n]['score']}

        narrative_data = {
            'timestamp': current_timestamp,
            'dominant_narratives': dominant_narratives,
            'all_active_narratives': {n: {'strength': s, 'sentiment': self._narrative_sentiment[n]['score']} for n, s in self._active_narratives.items()}
        }
        self.event_dispatcher.dispatch('narrative_analysis_updated', narrative_data=narrative_data)
        logger.info(f"Narrative analysis updated: Dominant Narratives: {dominant_narratives}")

class CryptoEquityBetaEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_price_data', self.handle_event) # Trigger on new price data for any asset
        self._crypto_assets = {s: a for s, a in assets.items() if a.asset_type == 'crypto'}
        self._equity_assets = {s: a for s, a in assets.items() if a.asset_type == 'equity' and a.is_crypto_equity}

    def handle_event(self, event_type, symbol, data_point):
        # This engine needs price data from both crypto and equity to run,
        # so it's better to trigger it on a schedule after sufficient data accumulation.
        # For now, simply log that data was received. The run_analysis will be called periodically by Orchestrator.
        logger.debug(f"Received new price data for {symbol}, queuing beta recalculation.")

    def run_analysis(self, current_timestamp):
        if not self._crypto_assets or not self._equity_assets:
            logger.debug("Not enough crypto or crypto-equity assets to calculate beta.")
            return

        beta_results = {}
        # Iterate over crypto-related equities and calculate beta against main crypto assets
        for eq_symbol, eq_asset in self._equity_assets.items():
            eq_df = eq_asset.get_price_dataframe()
            if len(eq_df) < max(Config.CORRELATION_WINDOWS):
                logger.debug(f"Not enough equity data for {eq_symbol} for beta calculation.")
                continue

            eq_returns = eq_df['close'].pct_change().dropna()

            for crypto_symbol, crypto_asset in self._crypto_assets.items():
                crypto_df = crypto_asset.get_price_dataframe()
                if len(crypto_df) < max(Config.CORRELATION_WINDOWS):
                    logger.debug(f"Not enough crypto data for {crypto_symbol} for beta calculation.")
                    continue
                crypto_returns = crypto_df['close'].pct_change().dropna()

                # Align dataframes by index (timestamps)
                # Use outer join to keep all available dates, then drop NaNs
                combined_returns = pd.concat([eq_returns.rename('equity_returns'), crypto_returns.rename('crypto_returns')], axis=1).dropna()

                if len(combined_returns) < min(Config.CORRELATION_WINDOWS): # Need at least for the shortest window
                    logger.debug(f"Insufficient aligned return data for {eq_symbol} vs {crypto_symbol} for beta.")
                    continue

                for window in Config.CORRELATION_WINDOWS:
                    if len(combined_returns) < window:
                        continue # Skip if not enough data for this window

                    window_returns = combined_returns.iloc[-window:]

                    # Calculate beta: Cov(Equity_Returns, Crypto_Returns) / Var(Crypto_Returns)
                    covariance = window_returns['equity_returns'].cov(window_returns['crypto_returns'], ddof=0)
                    crypto_variance = window_returns['crypto_returns'].var(ddof=0)

                    beta = covariance / crypto_variance if crypto_variance != 0 else 0.0
                    beta_results[f"{eq_symbol}_vs_{crypto_symbol}_{window}d"] = beta
                    logger.debug(f"Calculated Beta: {eq_symbol} vs {crypto_symbol} ({window}d) = {beta:.4f}")

        if beta_results:
            self.event_dispatcher.dispatch('crypto_equity_beta_updated', beta_data=beta_results)
            logger.info(f"Crypto-equity beta analysis updated: {beta_results}")

class LaggedEventImpactEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_macro_event', self.handle_event)
        self.event_dispatcher.subscribe('new_news_data', self.handle_event)
        self._event_queue = deque(maxlen=Config.MAX_MACRO * 3) # Store recent events to analyze impact over longer periods

    def handle_event(self, event_type, **kwargs):
        current_time = datetime.utcnow()
        event_to_add = None

        if event_type == 'new_macro_event':
            macro_event = kwargs.get('macro_event')
            event_to_add = {'event': macro_event, 'ingestion_time': current_time, 'type': 'macro'}
        elif event_type == 'new_news_data':
            news_article = kwargs.get('news_article')
            if news_article and news_article.impact_score >= 0.6: # Only track medium to high impact news
                event_to_add = {'event': news_article, 'ingestion_time': current_time, 'type': 'news'}
        
        if event_to_add:
            self._event_queue.append(event_to_add)
            logger.debug(f"Event added to lagged impact queue: {event_to_add['event'].details if event_to_add['type'] == 'macro' else event_to_add['event'].headline}")
        
        # self.run_analysis(get_current_timestamp()) # Run analysis on a schedule, not every event, to avoid overload

    def run_analysis(self, current_timestamp):
        impact_analysis_results = defaultdict(dict)
        current_dt = datetime.fromisoformat(current_timestamp.replace('Z', '+00:00'))

        # Clean up old events from queue
        while self._event_queue and (current_dt - self._event_queue[0]['ingestion_time']) > timedelta(hours=72 + 24):
            self._event_queue.popleft() # Remove events older than longest analysis window + buffer

        for event_entry in self._event_queue:
            event_obj = event_entry['event']
            event_timestamp = datetime.fromisoformat(event_obj.timestamp.replace('Z', '+00:00'))

            for symbol, asset in self.assets.items():
                df = asset.get_price_dataframe()
                if df.empty or len(df) < 2:
                    continue

                event_key = event_obj.details if event_entry['type'] == 'macro' else event_obj.headline

                # Calculate price change over different lagged windows
                for window_hours in [1, 24, 72]:
                    end_time = event_timestamp + timedelta(hours=window_hours)
                    if end_time > current_dt: # Don't analyze future or incomplete windows
                        continue

                    # Find price at event time and end of window
                    price_at_event_series = df['close'].asof(event_timestamp)
                    price_at_end_window_series = df['close'].asof(end_time)

                    price_at_event = price_at_event_series if not pd.isna(price_at_event_series) else None
                    price_at_end_window = price_at_end_window_series if not pd.isna(price_at_end_window_series) else None

                    if price_at_event is not None and price_at_end_window is not None and price_at_event != 0:
                        price_change = (price_at_end_window - price_at_event) / price_at_event
                        
                        if event_key not in impact_analysis_results[symbol]:
                            impact_analysis_results[symbol][event_key] = {}
                        impact_analysis_results[symbol][event_key][f'{window_hours}h_impact'] = price_change

        if impact_analysis_results:
            self.event_dispatcher.dispatch('lagged_event_impact_updated', impact_data=impact_analysis_results)
            logger.info(f"Lagged event impact analysis updated for {len(impact_analysis_results)} assets.")

class LiquidityFlowEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_price_data', self.handle_event) # Mock order book via price data
        self.event_dispatcher.subscribe('new_onchain_event', self.handle_event) # On-chain flows might affect liquidity
        self._mock_order_book = defaultdict(lambda: {'bids': [], 'asks': [], 'last_update': datetime.min})

    def handle_event(self, event_type, symbol, **kwargs):
        if event_type == 'new_price_data':
            data_point = kwargs.get('data_point')
            # Simulate simple order book depth based on price and volume
            mid_price = (data_point.high + data_point.low) / 2
            volume = data_point.volume

            # Mock bids and asks around mid_price
            self._mock_order_book[symbol]['bids'] = [
                {'price': mid_price * (1 - 0.0005 * i), 'size': volume * random.uniform(0.005, 0.03)}
                for i in range(5)
            ]
            self._mock_order_book[symbol]['asks'] = [
                {'price': mid_price * (1 + 0.0005 * i), 'size': volume * random.uniform(0.005, 0.03)}
                for i in range(5)
            ]
            self._mock_order_book[symbol]['last_update'] = datetime.utcnow()
            logger.debug(f"Mock order book updated for {symbol} based on price data.")

        elif event_type == 'new_onchain_event':
            onchain_event = kwargs.get('onchain_event')
            if onchain_event.event_type in ["EXCHANGE_FLOW_IN", "EXCHANGE_FLOW_OUT"]:
                # Simulate a temporary impact on liquidity from large exchange flows
                impact_factor = onchain_event.value_usd / 1e7 # Scale impact based on value
                if onchain_event.event_type == "EXCHANGE_FLOW_IN":
                    # Inflow might temporarily increase bid liquidity
                    for bid in self._mock_order_book[symbol]['bids']:
                        bid['size'] *= (1 + impact_factor * 0.1)
                else: # EXCHANGE_FLOW_OUT
                    # Outflow might temporarily decrease bid liquidity
                    for bid in self._mock_order_book[symbol]['bids']:
                        bid['size'] *= (1 - impact_factor * 0.05)
                logger.info(f"On-chain event {onchain_event.event_type} influenced mock liquidity for {symbol}.")

        # self.run_analysis(get_current_timestamp()) # Run analysis on a schedule

    def run_analysis(self, current_timestamp):
        liquidity_metrics = {}
        for symbol, book in self._mock_order_book.items():
            if not book['bids'] or not book['asks'] or (datetime.utcnow() - book['last_update']) > timedelta(minutes=5):
                logger.debug(f"Skipping liquidity analysis for {symbol}: insufficient data or outdated.")
                continue

            # Calculate bid-ask spread
            best_bid = max(b['price'] for b in book['bids'])
            best_ask = min(a['price'] for a in book['asks'])
            
            if best_bid <= 0 or best_ask <= 0: # Avoid division by zero or invalid prices
                logger.warning(f"Invalid prices for {symbol} in liquidity calculation. Skipping.")
                continue

            spread = best_ask - best_bid
            relative_spread = (spread / best_bid) * 100

            # Calculate cumulative liquidity at certain depth (e.g., within 0.2% of mid-price)
            mid_price = (best_bid + best_ask) / 2
            depth_percentage = 0.002 # 0.2% depth

            bid_liquidity_depth = sum(b['size'] for b in book['bids'] if b['price'] >= mid_price * (1 - depth_percentage))
            ask_liquidity_depth = sum(a['size'] for a in book['asks'] if a['price'] <= mid_price * (1 + depth_percentage))

            liquidity_metrics[symbol] = {
                'timestamp': current_timestamp,
                'bid_ask_spread': spread,
                'relative_spread_pct': relative_spread,
                'bid_liquidity_depth_usd': bid_liquidity_depth * best_bid, # Convert to USD value
                'ask_liquidity_depth_usd': ask_liquidity_depth * best_ask,
                'total_liquidity_depth_usd': (bid_liquidity_depth * best_bid) + (ask_liquidity_depth * best_ask)
            }
        if liquidity_metrics:
            self.event_dispatcher.dispatch('liquidity_flow_updated', liquidity_data=liquidity_metrics)
            logger.info(f"Liquidity flow analysis updated for {len(liquidity_metrics)} assets.")

class CorrelationEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        self.event_dispatcher.subscribe('new_price_data', self.handle_event) # Trigger on new price data for any asset
        # Asset price history is already managed by the Asset objects themselves.

    def handle_event(self, event_type, symbol, data_point):
        # This engine runs periodically on all available data, so no specific action here
        logger.debug(f"Received new price data for {symbol}, triggering future correlation recalculation.")

    def run_analysis(self, current_timestamp):
        all_dfs = []
        for symbol, asset in self.assets.items():
            df = asset.get_price_dataframe()
            if not df.empty:
                # Get the 'close' prices and rename the column to the asset symbol
                all_dfs.append(df['close'].rename(symbol))

        if not all_dfs or len(all_dfs) < 2:
            logger.debug("Not enough assets or data to perform correlation analysis.")
            return

        # Concatenate all asset price series into a single DataFrame
        combined_prices = pd.concat(all_dfs, axis=1)
        # Calculate daily percentage returns
        combined_returns = combined_prices.pct_change().dropna()

        correlation_results = {}
        for window in Config.CORRELATION_WINDOWS:
            if len(combined_returns) >= window:
                # Take the latest 'window' days of returns
                window_returns = combined_returns.iloc[-window:]
                # Calculate the correlation matrix
                correlations = window_returns.corr().to_dict()
                correlation_results[f'{window}d_correlations'] = correlations
                logger.debug(f"Calculated {window}d correlations.")
            else:
                logger.debug(f"Not enough data for {window}d correlation calculation. Have {len(combined_returns)}, Need {window}.")

        if correlation_results:
            self.event_dispatcher.dispatch('global_correlation_updated', correlations=correlation_results)
            logger.info(f"Global correlation analysis updated for windows: {Config.CORRELATION_WINDOWS}")

class PredictiveModelEngine(BaseEngine):
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher, assets)
        # Subscribe to all relevant analysis engine updates to gather inputs
        self.event_dispatcher.subscribe('technical_analysis_updated', self.handle_event)
        self.event_dispatcher.subscribe('risk_sentiment_updated', self.handle_event)
        self.event_dispatcher.subscribe('narrative_analysis_updated', self.handle_event)
        self.event_dispatcher.subscribe('crypto_equity_beta_updated', self.handle_event)
        self.event_dispatcher.subscribe('lagged_event_impact_updated', self.handle_event)
        self.event_dispatcher.subscribe('liquidity_flow_updated', self.handle_event)
        self.event_dispatcher.subscribe('global_correlation_updated', self.handle_event)

        # Store the latest aggregated inputs from all engines
        self._latest_inputs = {}
        self._prediction_history = deque(maxlen=100)

    def handle_event(self, event_type, **kwargs):
        # Update the latest inputs based on incoming events
        if event_type == 'technical_analysis_updated':
            symbol = kwargs.get('symbol')
            data = kwargs.get('data')
            self._latest_inputs.setdefault('technical_analysis', {})[symbol] = data
        elif event_type == 'risk_sentiment_updated':
            self._latest_inputs['risk_sentiment'] = kwargs.get('roro_data')
        elif event_type == 'narrative_analysis_updated':
            self._latest_inputs['narratives'] = kwargs.get('narrative_data')
        elif event_type == 'crypto_equity_beta_updated':
            self._latest_inputs['crypto_equity_beta'] = kwargs.get('beta_data')
        elif event_type == 'lagged_event_impact_updated':
            self._latest_inputs['lagged_impact'] = kwargs.get('impact_data')
        elif event_type == 'liquidity_flow_updated':
            self._latest_inputs['liquidity_flow'] = kwargs.get('liquidity_data')
        elif event_type == 'global_correlation_updated':
            self._latest_inputs['correlations'] = kwargs.get('correlations')
        
        logger.debug(f"PredictiveModelEngine received update: {event_type}. Will consider for next prediction.")
        # Predictions are run on a scheduled cycle, not every event, to ensure all inputs are somewhat fresh

    def run_analysis(self, current_timestamp):
        # This function acts as the "ensemble" logic. It gathers all available
        # insights from `_latest_inputs` and synthesizes a prediction.
        
        if not self._latest_inputs:
            logger.info("Not enough inputs for predictive model yet.")
            return

        predictions = {}
        # Iterate through each asset to make individual predictions
        for symbol, asset in self.assets.items():
            current_close = self._latest_inputs.get('technical_analysis', {}).get(symbol, {}).get('close')
            if current_close is None:
                logger.debug(f"No recent close price for {symbol}, skipping prediction for this asset.")
                continue

            # --- Feature Engineering from latest inputs ---
            features = {
                'roro_score': self._latest_inputs.get('risk_sentiment', {}).get('score', 0.0),
                'dominant_narrative_strength': 0.0,
                'dominant_narrative_sentiment': 0.0,
                'sma_short': self._latest_inputs.get('technical_analysis', {}).get(symbol, {}).get('SMA_SHORT', 0),
                'sma_long': self._latest_inputs.get('technical_analysis', {}).get(symbol, {}).get('SMA_LONG', 0),
                'rsi': self._latest_inputs.get('technical_analysis', {}).get(symbol, {}).get('RSI', 50),
                'relative_spread_pct': self._latest_inputs.get('liquidity_flow', {}).get(symbol, {}).get('relative_spread_pct', 0),
                'lagged_1h_impact': self._latest_inputs.get('lagged_impact', {}).get(symbol, {}).get('latest_event_1h_impact', 0), # Simplified for demo
                'lagged_24h_impact': self._latest_inputs.get('lagged_impact', {}).get(symbol, {}).get('latest_event_24h_impact', 0),
                'equity_crypto_beta': self._latest_inputs.get('crypto_equity_beta', {}).get(f"{symbol}_vs_BTC_90d", 0.0) if asset.is_crypto_equity and 'BTC' in self.assets else 0.0,
                'global_correlation_with_spy': self._latest_inputs.get('correlations', {}).get('90d_correlations', {}).get(symbol, {}).get('AAPL', 0.0) # Using AAPL as proxy for general market
            }

            # Incorporate dominant narratives
            narratives_data = self._latest_inputs.get('narratives', {}).get('dominant_narratives', {})
            if narratives_data:
                first_narrative = list(narratives_data.values())[0]
                features['dominant_narrative_strength'] = first_narrative['strength']
                features['dominant_narrative_sentiment'] = first_narrative['sentiment']

            # --- Simple Ensemble Logic (Conceptual) ---
            # This is a highly simplified conceptual model. In a real system, this would be
            # a trained machine learning model (e.g., Random Forest, LSTM, deep neural network)
            # taking these features as input and outputting a probability or price target.
            
            # Weighing factors for different inputs
            # Positive impact factors increase price probability, negative decrease
            impact_from_ta = 0.0
            if features['sma_short'] > features['sma_long']: impact_from_ta += 0.1
            if features['rsi'] < 30: impact_from_ta += 0.05 # Oversold
            if features['rsi'] > 70: impact_from_ta -= 0.05 # Overbought

            impact_from_sentiment = features['roro_score'] * 0.2

            impact_from_narrative = features['dominant_narrative_strength'] * features['dominant_narrative_sentiment'] * 0.1 # Strength * Sentiment

            impact_from_macro = (features['lagged_1h_impact'] * 0.2 + features['lagged_24h_impact'] * 0.1) * 10 # Scale impact

            # Predict price change direction and confidence
            net_impact_score = impact_from_ta + impact_from_sentiment + impact_from_narrative + impact_from_macro

            # Map net_impact_score to a prediction and confidence
            # Simple linear mapping for demo: -1 -> strong down, 1 -> strong up
            direction_probability = (net_impact_score + 1) / 2 # Normalize to 0-1
            
            # Confidence based on magnitude of impact score
            confidence = min(0.95, 0.5 + abs(net_impact_score) * 0.4) # Min 0.5, Max 0.95

            predicted_direction = "UP" if direction_probability > 0.55 else ("DOWN" if direction_probability < 0.45 else "SIDEWAYS")
            
            # Generate a mock price target for a given horizon
            predicted_change_factor = 1 + np.clip(net_impact_score * random.uniform(0.01, 0.05), -0.1, 0.1) # Max 10% change
            
            price_targets = {}
            for horizon in Config.PREDICTION_HORIZONS:
                # Simplistic: larger horizons get more amplified change
                horizon_factor = {'1h': 1, '24h': 3, '7d': 10}[horizon]
                target_price = current_close * (1 + net_impact_score * 0.005 * horizon_factor * random.uniform(0.8, 1.2))
                price_targets[horizon] = target_price

            predictions[symbol] = {
                'timestamp': current_timestamp,
                'predicted_direction': predicted_direction,
                'confidence': confidence,
                'net_impact_score': net_impact_score,
                'price_targets': price_targets,
                'contributing_factors': {
                    'technical': impact_from_ta,
                    'sentiment': impact_from_sentiment,
                    'narrative': impact_from_narrative,
                    'macro_event_impact': impact_from_macro
                }
            }
        
        if predictions:
            self.event_dispatcher.dispatch('predictive_model_updated', prediction_data=predictions)
            logger.info(f"Predictive model updated for {len(predictions)} assets.")
            self._prediction_history.append(predictions) # Store for backtesting/analysis


# --- REPORT GENERATION ---
class ReportGenerator:
    def __init__(self, event_dispatcher):
        self.event_dispatcher = event_dispatcher
        self.analysis_cache = {} # Cache for the latest analysis results from various engines
        self.last_report_time = datetime.min

        self.event_dispatcher.subscribe('technical_analysis_updated', self._update_cache)
        self.event_dispatcher.subscribe('risk_sentiment_updated', self._update_cache)
        self.event_dispatcher.subscribe('narrative_analysis_updated', self._update_cache)
        self.event_dispatcher.subscribe('crypto_equity_beta_updated', self._update_cache)
        self.event_dispatcher.subscribe('lagged_event_impact_updated', self._update_cache)
        self.event_dispatcher.subscribe('liquidity_flow_updated', self._update_cache)
        self.event_dispatcher.subscribe('global_correlation_updated', self._update_cache)
        self.event_dispatcher.subscribe('predictive_model_updated', self._update_cache)

    def _update_cache(self, event_type, **kwargs):
        # Extract relevant data based on event type and store in cache
        if event_type == 'technical_analysis_updated':
            symbol = kwargs.get('symbol')
            data = kwargs.get('data')
            if symbol and data:
                self.analysis_cache.setdefault('technical_analysis', {})[symbol] = data
        elif event_type == 'risk_sentiment_updated':
            self.analysis_cache['risk_sentiment'] = kwargs.get('roro_data')
        elif event_type == 'narrative_analysis_updated':
            self.analysis_cache['narratives'] = kwargs.get('narrative_data')
        elif event_type == 'crypto_equity_beta_updated':
            self.analysis_cache['crypto_equity_beta'] = kwargs.get('beta_data')
        elif event_type == 'lagged_event_impact_updated':
            self.analysis_cache['lagged_impact'] = kwargs.get('impact_data')
        elif event_type == 'liquidity_flow_updated':
            self.analysis_cache['liquidity_flow'] = kwargs.get('liquidity_data')
        elif event_type == 'global_correlation_updated':
            self.analysis_cache['correlations'] = kwargs.get('correlations')
        elif event_type == 'predictive_model_updated':
            self.analysis_cache['predictions'] = kwargs.get('prediction_data')

        logger.debug(f"ReportGenerator cache updated by event: {event_type}")

    def generate_financial_report(self, assets_list):
        current_time_dt = datetime.utcnow()
        if (current_time_dt - self.last_report_time).total_seconds() < Config.REPORT_GENERATION_INTERVAL:
            return None # Don't generate if too soon

        self.last_report_time = current_time_dt

        report_sections = []
        report_sections.append("# Institutional-Grade Financial Analysis Report")
        report_sections.append(f"Report Generated: {get_current_timestamp()}\n")

        # Executive Summary (placeholder for Gemini API synthesis)
        report_sections.append("## Executive Summary")
        prompt_for_gemini = self._prepare_gemini_prompt()
        gemini_summary = self._call_gemini_api(prompt_for_gemini)
        if gemini_summary:
            report_sections.append(gemini_summary)
        else:
            report_sections.append("*(This section would be dynamically generated by the Gemini API, synthesizing insights from all analysis engines into a coherent, expert-level overview of market conditions, key risks, opportunities, and forward-looking perspectives. It leverages the detailed prompt mentioned in the Config and the current state of analysis. API key might be missing or an error occurred.)*\n")

        # Market Sentiment
        report_sections.append("## Market Sentiment Analysis")
        roro_data = self.analysis_cache.get('risk_sentiment')
        if roro_data:
            report_sections.append(f"- **Global Risk-On/Risk-Off (RORO) Score**: {roro_data['score']:.4f} ({roro_data['status']})")
            report_sections.append(f"  *Interpretation*: Reflects the prevailing market mood, influencing asset allocation decisions. A higher score indicates increased investor willingness to take on risk, favoring growth assets like technology stocks and cryptocurrencies. Conversely, a lower score suggests risk aversion, leading investors to safe-haven assets. This score is derived from aggregate news sentiment, social media opinions, and surprise factors from macroeconomic events.")
        else:
            report_sections.append("- No recent Risk-On/Risk-Off (RORO) sentiment data available.")

        narratives = self.analysis_cache.get('narratives')
        if narratives and narratives.get('dominant_narratives'):
            report_sections.append("\n- **Dominant Market Narratives**:")
            for nar, data in narratives['dominant_narratives'].items():
                report_sections.append(f"  - '{nar}': Strength {data['strength']:.2f}, Sentiment: {data['sentiment']:.2f}")
            report_sections.append(f"  *Interpretation*: These narratives are currently shaping market discourse and investor behavior, often influencing capital flows into or out of related asset classes. For example, a strong 'AI & Tech Innovation' narrative with positive sentiment often correlates with inflows into technology stocks and AI-related crypto projects.")
        else:
            report_sections.append("- No dominant market narratives identified.")
        report_sections.append("\n")

        # Asset-Specific Technical Analysis
        report_sections.append("## Asset-Specific Technical Analysis (Latest Data)")
        tech_analysis_data = self.analysis_cache.get('technical_analysis')
        if tech_analysis_data:
            for symbol in Config.ASSETS_TO_TRACK: # Ensure all tracked assets are included, even if no new TA
                data = tech_analysis_data.get(symbol)
                if data:
                    report_sections.append(f"### {symbol}")
                    report_sections.append(f"- Timestamp: {data.get('timestamp', 'N/A')}")
                    report_sections.append(f"- Current Close: {data.get('close', 'N/A'):,.2f}")
                    report_sections.append(f"- RSI: {data.get('RSI', 'N/A'):.2f} (Overbought > 70, Oversold < 30)")
                    report_sections.append(f"- SMA_SHORT ({Config.SMA_SHORT}): {data.get('SMA_SHORT', 'N/A'):,.2f}")
                    report_sections.append(f"- SMA_LONG ({Config.SMA_LONG}): {data.get('SMA_LONG', 'N/A'):,.2f}")
                    report_sections.append(f"- EMA_SHORT ({Config.EMA_SHORT}): {data.get('EMA_SHORT', 'N/A'):,.2f}")
                    report_sections.append(f"- EMA_LONG ({Config.EMA_LONG}): {data.get('EMA_LONG', 'N/A'):,.2f}")
                    report_sections.append(f"- Bollinger Bands: Upper {data.get('Upper_BB', 'N/A'):,.2f}, Lower {data.get('Lower_BB', 'N/A'):,.2f}")
                    report_sections.append(f"- Stochastic Oscillator %K: {data.get('%K', 'N/A'):.2f}, %D: {data.get('%D', 'N/A'):.2f}")
                    
                    # Add simple TA interpretation
                    ta_interpretation = []
                    if data['RSI'] >= 70: ta_interpretation.append("Overbought (potential reversal down).")
                    elif data['RSI'] <= 30: ta_interpretation.append("Oversold (potential reversal up).")
                    if data['SMA_SHORT'] > data['SMA_LONG'] and data['close'] > data['SMA_SHORT']: ta_interpretation.append("Strong bullish momentum (golden cross / above MA).")
                    elif data['SMA_SHORT'] < data['SMA_LONG'] and data['close'] < data['SMA_SHORT']: ta_interpretation.append("Strong bearish momentum (death cross / below MA).")
                    
                    if ta_interpretation: report_sections.append(f"  *Key TA Observation*: {' '.join(ta_interpretation)}")
                    else: report_sections.append("  *Key TA Observation*: Neutral or mixed signals.")
                    report_sections.append("")
                else:
                    report_sections.append(f"### {symbol}\n- No recent technical analysis data available for {symbol}.\n")
        else:
            report_sections.append("- No recent technical analysis data available for any asset.")
        report_sections.append("\n")

        # Cross-Market Linkages
        report_sections.append("## Cross-Market Linkages & Correlations")
        global_correlations = self.analysis_cache.get('correlations')
        if global_correlations:
            for window, corr_matrix_data in global_correlations.items():
                report_sections.append(f"### {window} Correlation Matrix (Selected Pairs)")
                # Print a few key correlations, not the whole matrix for brevity
                # Ensure the symbols exist in the matrix before trying to access
                btc_aapl_corr = corr_matrix_data.get('BTC', {}).get('AAPL', 'N/A')
                eth_goog_corr = corr_matrix_data.get('ETH', {}).get('GOOG', 'N/A')
                btc_mstr_corr = corr_matrix_data.get('BTC', {}).get('MSTR', 'N/A')
                eth_coin_corr = corr_matrix_data.get('ETH', {}).get('COIN', 'N/A')

                report_sections.append(f"- BTC vs AAPL: {btc_aapl_corr:.4f}" if isinstance(btc_aapl_corr, float) else f"- BTC vs AAPL: {btc_aapl_corr}")
                report_sections.append(f"- ETH vs GOOG: {eth_goog_corr:.4f}" if isinstance(eth_goog_corr, float) else f"- ETH vs GOOG: {eth_goog_corr}")
                report_sections.append(f"- BTC vs MSTR (Crypto Equity): {btc_mstr_corr:.4f}" if isinstance(btc_mstr_corr, float) else f"- BTC vs MSTR (Crypto Equity): {btc_mstr_corr}")
                report_sections.append(f"- ETH vs COIN (Crypto Equity): {eth_coin_corr:.4f}" if isinstance(eth_coin_corr, float) else f"- ETH vs COIN (Crypto Equity): {eth_coin_corr}")
                
                report_sections.append(f"  *Interpretation*: Positive correlation indicates assets tend to move in the same direction, reflecting a deeper integration between cryptocurrency and traditional financial markets, especially during periods of market stress. The strength of correlation changes can indicate shifts in market regimes.")
            report_sections.append("")
        else:
            report_sections.append("- No global correlation data available.")

        beta_data = self.analysis_cache.get('crypto_equity_beta')
        if beta_data:
            report_sections.append("### Crypto-Equity Beta (Sensitivity to Crypto Assets)")
            for pair, beta_val in beta_data.items():
                report_sections.append(f"- {pair}: {beta_val:.4f}")
            report_sections.append(f"  *Interpretation*: Beta measures the sensitivity of crypto-related stocks (like MSTR, COIN) to their underlying crypto assets. A beta > 1 suggests the stock is more volatile than the crypto asset it tracks, indicating amplified movements. This is a key metric for understanding cross-market risk exposure.")
            report_sections.append("")
        else:
            report_sections.append("- No crypto-equity beta data available.")

        # Macroeconomic Impact Analysis
        report_sections.append("## Macroeconomic Impact Analysis")
        lagged_impact = self.analysis_cache.get('lagged_impact')
        if lagged_impact:
            report_sections.append("Latest Lagged Event Impacts on Asset Prices:")
            for symbol, events in lagged_impact.items():
                report_sections.append(f"### {symbol}")
                for event_details, impacts in events.items():
                    report_sections.append(f"- Event: '{event_details}'")
                    for window, change in impacts.items():
                        report_sections.append(f"  - {window}: {change:.2%}")
                report_sections.append("")
            report_sections.append(f"  *Interpretation*: This analysis quantifies how major macroeconomic events (e.g., CPI reports, Fed decisions) impact asset prices over various time horizons, highlighting the interconnectedness driven by monetary policy and economic shifts. Significant, sustained impacts suggest strong market sensitivity to these drivers.")
        else:
            report_sections.append("- No recent lagged event impact data available.")
        report_sections.append("\n")

        # Liquidity & On-Chain Flows (for crypto assets)
        report_sections.append("## Liquidity & On-Chain Flow Analysis")
        liquidity_data = self.analysis_cache.get('liquidity_flow')
        if liquidity_data:
            report_sections.append("Latest Liquidity Metrics:")
            for symbol, metrics in liquidity_data.items():
                report_sections.append(f"### {symbol}")
                report_sections.append(f"- Bid-Ask Spread: {metrics.get('bid_ask_spread', 'N/A'):,.4f}")
                report_sections.append(f"- Relative Spread: {metrics.get('relative_spread_pct', 'N/A'):.4f}%")
                report_sections.append(f"- Total Liquidity Depth (USD): {metrics.get('total_liquidity_depth_usd', 'N/A'):,.2f}")
                report_sections.append(f"  *Interpretation*: Measures market depth and ease of trading. Lower spreads and higher liquidity depth generally indicate healthier, more efficient markets that can absorb larger trades with less price impact. Spikes in spreads or drops in depth can signal potential volatility or illiquidity concerns.")
                report_sections.append("")
        else:
            report_sections.append("- No recent liquidity flow data available.")

        report_sections.append("On-Chain Events (last tracked, mock placeholder for summary):")
        # In a real system, OnChainDataProvider would feed specific, categorized on-chain events
        # And the ReportGenerator would aggregate and summarize these.
        report_sections.append("- Recent significant on-chain activities include large transfers, exchange inflows/outflows, and miner movements. Detailed analysis indicates potential shifts in holding behavior or exchange liquidity. (Actual summary would be generated here if aggregated on-chain data was directly exposed by an engine or summarized by Gemini API).")
        report_sections.append("\n")

        # Predictive Insights
        report_sections.append("## Predictive Insights")
        predictions = self.analysis_cache.get('predictions')
        if predictions:
            for symbol, pred_data in predictions.items():
                report_sections.append(f"### {symbol} Prediction")
                report_sections.append(f"- Predicted Direction: **{pred_data['predicted_direction']}** (Confidence: {pred_data['confidence']:.2%})")
                report_sections.append(f"- Net Impact Score (Internal): {pred_data['net_impact_score']:.4f}")
                report_sections.append("- Price Targets:")
                for horizon, price in pred_data['price_targets'].items():
                    report_sections.append(f"  - {horizon} Target: {price:,.2f}")
                report_sections.append(f"- Contributing Factors: Technical {pred_data['contributing_factors']['technical']:.2f}, Sentiment {pred_data['contributing_factors']['sentiment']:.2f}, Narrative {pred_data['contributing_factors']['narrative']:.2f}, Macro Event Impact {pred_data['contributing_factors']['macro_event_impact']:.2f}")
                
                # Interpret confidence
                if pred_data['confidence'] >= Config.PREDICTIVE_CONFIDENCE_THRESHOLD:
                    report_sections.append(f"  *Interpretation*: This is a **strong conviction** prediction based on significant alignment across multiple contributing factors. Investors may consider adjusting positions according to the predicted direction.")
                elif pred_data['confidence'] >= 0.5:
                    report_sections.append(f"  *Interpretation*: This is a **moderate conviction** prediction. While indicators lean towards the predicted direction, some ambiguity or counter-signals exist. Exercise caution.")
                else:
                    report_sections.append(f"  *Interpretation*: This prediction has **low conviction** due to conflicting signals or insufficient data. Further analysis is recommended before acting.")
                report_sections.append("")
        else:
            report_sections.append("- No predictive model output available yet. Requires sufficient data across all analysis engines.")
        report_sections.append("\n")

        # Append the doc file summary as requested.
        report_sections.append("## Relevant Insights from 'Phân tích các mối liên hệ giữa thị trường tiền điện tử và thị trường chứng khoán.docx'")
        report_sections.append("The document 'Phân tích các mối liên hệ giữa thị trường tiền điện tử và thị trường chứng khoán.docx' provides an in-depth analysis of the evolving relationship between the cryptocurrency market and traditional stock markets.")
        report_sections.append("Key takeaways include:")
        report_sections.append("- Historically, cryptocurrencies like Bitcoin were seen as uncorrelated assets or 'digital gold,' offering portfolio diversification benefits.")
        report_sections.append("- Recent years have shown a deeper convergence, challenging these traditional views.")
        report_sections.append("- Both crypto and stock markets are increasingly sensitive to common underlying factors, particularly risk-on/risk-off sentiment.")
        report_sections.append("- Central bank monetary policies (interest rates, inflation concerns) significantly impact both asset classes, often synchronously.")
        report_sections.append("- Institutional capital flows, especially through spot Bitcoin ETFs, have integrated crypto into traditional finance.")
        report_sections.append("- Publicly listed companies tied to crypto (e.g., Coinbase, MicroStrategy) create direct channels for crypto market performance to reflect in traditional stock prices.")
        report_sections.append("- Blockchain technology is reshaping traditional financial infrastructure, fostering deeper convergence.")
        report_sections.append("- The relationship has evolved from uncorrelated to increasingly positively correlated, especially during market stress, necessitating re-evaluation of diversification and risk management strategies for investors.")
        report_sections.append("- The document also implicitly highlights the need for advanced analytical tools, such as the Teu.py engine, to navigate these complex, interconnected markets effectively and integrate diverse data streams from traditional finance and blockchain ecosystems.")
        report_sections.append("\n")

        return "\n".join(report_sections)
    
    def _prepare_gemini_prompt(self):
        """Prepares a detailed prompt for the Gemini API based on current analysis cache."""
        # This prompt is designed to instruct Gemini to act as a senior financial analyst
        # and synthesize the various data points into a cohesive executive summary.

        prompt_parts = []
        prompt_parts.append("You are an institutional-grade financial market analyst. Your task is to provide a concise yet comprehensive Executive Summary based on the provided market analysis data. Focus on key trends, inter-market relationships, dominant sentiments, and forward-looking insights. Highlight critical information for strategic decision-making.")
        prompt_parts.append("--- Current Market Analysis Data ---")

        if self.analysis_cache.get('risk_sentiment'):
            roro = self.analysis_cache['risk_sentiment']
            prompt_parts.append(f"Risk-On/Risk-Off (RORO) Score: {roro['score']:.4f} ({roro['status']}). This indicates the general market risk appetite.")
        
        if self.analysis_cache.get('narratives') and self.analysis_cache['narratives'].get('dominant_narratives'):
            dominant_narratives = ", ".join([f"'{n}' (Strength: {d['strength']:.2f}, Sentiment: {d['sentiment']:.2f})" for n, d in self.analysis_cache['narratives']['dominant_narratives'].items()])
            prompt_parts.append(f"Dominant Market Narratives: {dominant_narratives}. These are the prevailing themes influencing market attention and capital flows.")
        
        if self.analysis_cache.get('correlations'):
            latest_window = sorted(self.analysis_cache['correlations'].keys(), reverse=True)[0]
            corr_data = self.analysis_cache['correlations'][latest_window]
            btc_aapl_corr = corr_data.get('BTC', {}).get('AAPL', 'N/A')
            btc_mstr_corr = corr_data.get('BTC', {}).get('MSTR', 'N/A')
            prompt_parts.append(f"Recent Correlations ({latest_window}): BTC-AAPL ({btc_aapl_corr:.4f}), BTC-MSTR ({btc_mstr_corr:.4f}). This reflects the increasing interconnectedness between crypto and traditional equity markets.")

        if self.analysis_cache.get('lagged_impact'):
            # Summarize top 2-3 most impactful events and their 24h/72h impact on a key asset like BTC
            btc_impacts = self.analysis_cache['lagged_impact'].get('BTC', {})
            if btc_impacts:
                top_impacts = sorted(btc_impacts.items(), key=lambda item: max(abs(v) for v in item[1].values() if isinstance(v, float)), reverse=True)[:2]
                impact_summary = []
                for event_detail, impacts in top_impacts:
                    impact_summary.append(f"'{event_detail}' (24h: {impacts.get('24h_impact', 0):.2%}, 72h: {impacts.get('72h_impact', 0):.2%})")
                prompt_parts.append(f"Key Macro/News Event Impacts on BTC: {'; '.join(impact_summary)}. These events show the delayed reactions in the market.")

        if self.analysis_cache.get('liquidity_flow') and self.analysis_cache['liquidity_flow'].get('BTC'):
            btc_liquidity = self.analysis_cache['liquidity_flow']['BTC']
            prompt_parts.append(f"BTC Liquidity: Relative Spread {btc_liquidity['relative_spread_pct']:.2f}%, Total Depth ${btc_liquidity['total_liquidity_depth_usd']:,.0f}. Indicates market depth and ease of execution.")

        if self.analysis_cache.get('predictions'):
            predictions_summary = []
            for symbol, pred_data in self.analysis_cache['predictions'].items():
                if pred_data['confidence'] >= Config.PREDICTIVE_CONFIDENCE_THRESHOLD:
                    predictions_summary.append(f"{symbol} predicted to move {pred_data['predicted_direction']} with {pred_data['confidence']:.2%} confidence (1h target: {pred_data['price_targets']['1h']:.2f}).")
            if predictions_summary:
                prompt_parts.append(f"High-Confidence Predictive Insights: {'; '.join(predictions_summary)}")
            else:
                prompt_parts.append("No high-confidence predictive insights available at this time.")

        prompt_parts.append("\nBased on the above, provide an executive summary focusing on actionable insights and the overall market outlook. Emphasize the interconnectedness of traditional and crypto markets as highlighted in prior research.")
        
        return "\n".join(prompt_parts)

    def _call_gemini_api(self, prompt):
        """Calls the Gemini API to generate the executive summary."""
        if not Config.GEMINI_API_KEY or Config.GEMINI_API_KEY == "YOUR_GEMINI_API_KEY":
            logger.warning("Gemini API Key not configured. Skipping Executive Summary generation.")
            return None

        headers = {
            "Content-Type": "application/json"
        }
        data = {
            "contents": [
                {
                    "parts": [
                        {"text": prompt}
                    ]
                }
            ],
            "safetySettings": [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
            ],
            "generationConfig": {
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 800
            }
        }

        try:
            response = requests.post(Config.GEMINI_API_URL, headers=headers, json=data, timeout=30)
            response.raise_for_status() # Raise an exception for HTTP errors (4xx or 5xx)
            result = response.json()
            if 'candidates' in result and result['candidates']:
                # Extracting text from the response, handling potential 'parts' structure
                text_content = ""
                for part in result['candidates'][0]['content']['parts']:
                    if 'text' in part:
                        text_content += part['text']
                return text_content
            else:
                logger.error(f"Gemini API response missing 'candidates' or content: {result}")
                return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Error calling Gemini API: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding Gemini API response JSON: {e}")
            return None
        except Exception as e:
            logger.error(f"An unexpected error occurred during Gemini API call: {e}")
            return None


# --- MAIN FINANCIAL ANALYSIS ENGINE ORCHESTRATOR ---
class FinancialAnalysisEngine:
    def __init__(self, assets_to_track):
        self.event_dispatcher = EventDispatcher()
        self.assets = self._initialize_assets(assets_to_track)
        self._lock = threading.Lock() # For thread-safe access to shared resources

        # Data Providers
        self.market_data_provider = MarketDataProvider(self.event_dispatcher, assets_to_track)
        self.news_data_provider = NewsDataProvider(self.event_dispatcher, assets_to_track)
        self.macro_data_provider = MacroEconomicDataProvider(self.event_dispatcher)
        # Identify crypto assets from the initialized assets for on-chain provider
        crypto_symbols = [s for s, a in self.assets.items() if a.asset_type == 'crypto']
        self.onchain_data_provider = OnChainDataProvider(self.event_dispatcher, crypto_symbols)

        # Analysis Engines
        self.technical_analysis_engine = TechnicalAnalysisEngine(self.event_dispatcher, self.assets)
        self.risk_sentiment_engine = RiskSentimentEngine(self.event_dispatcher, self.assets)
        self.narrative_analysis_engine = NarrativeAnalysisEngine(self.event_dispatcher, self.assets)
        self.crypto_equity_beta_engine = CryptoEquityBetaEngine(self.event_dispatcher, self.assets)
        self.lagged_event_impact_engine = LaggedEventImpactEngine(self.event_dispatcher, self.assets)
        self.liquidity_flow_engine = LiquidityFlowEngine(self.event_dispatcher, self.assets)
        self.correlation_engine = CorrelationEngine(self.event_dispatcher, self.assets)
        self.predictive_model_engine = PredictiveModelEngine(self.event_dispatcher, self.assets) # New predictive model

        # Report Generator
        self.report_generator = ReportGenerator(self.event_dispatcher)

        # Global analysis storage (for direct access by report generator if needed, though cache is preferred)
        # This now directly reflects what's in the ReportGenerator's cache for simplicity
        self.global_analysis = self.report_generator.analysis_cache # Link directly to the report generator's cache

        self._load_state()

        self._analysis_thread = None
        self._report_thread = None
        self._stop_event = threading.Event()

    def _initialize_assets(self, assets_to_track):
        assets = {}
        for symbol in assets_to_track:
            asset_type = 'crypto' if symbol in ['BTC', 'ETH'] else 'equity'
            is_crypto_equity = symbol in ['MSTR', 'COIN'] # Mark these specifically
            assets[symbol] = Asset(symbol, asset_type, is_crypto_equity)
        return assets

    def _save_state(self):
        state = {
            'assets': {s: {
                'symbol': a.symbol,
                'asset_type': a.asset_type,
                'is_crypto_equity': a.is_crypto_equity,
                'price_history': [dp.to_dict() for dp in a.price_history],
                'news_history': [na.to_dict() for na in a.news_history],
                'opinion_history': [uo.to_dict() for uo in a.opinion_history],
                'onchain_history': [oe.to_dict() for oe in a.onchain_history] if a.onchain_history else []
            } for s, a in self.assets.items()},
            'analysis_cache': self.report_generator.analysis_cache # Save the report generator's cache
        }
        try:
            with open(Config.STATE_FILE, 'w') as f:
                json.dump(state, f, indent=4)
            logger.info(f"Engine state saved to {Config.STATE_FILE}")
        except Exception as e:
            logger.error(f"Error saving engine state: {e}", exc_info=True)

    def _load_state(self):
        if os.path.exists(Config.STATE_FILE):
            try:
                with open(Config.STATE_FILE, 'r') as f:
                    state = json.load(f)
                # Reconstruct assets
                for symbol, asset_data in state.get('assets', {}).items():
                    asset = Asset(asset_data['symbol'], asset_data['asset_type'], asset_data.get('is_crypto_equity', False))
                    asset.price_history.extend([DataPoint.from_dict(dp) for dp in asset_data['price_history']])
                    asset.news_history.extend([NewsArticle.from_dict(na) for na in asset_data['news_history']])
                    asset.opinion_history.extend([UserOpinion.from_dict(uo) for uo in asset_data['opinion_history']])
                    if asset_data.get('onchain_history'):
                        if asset.onchain_history is None: asset.onchain_history = deque(maxlen=Config.MAX_ONCHAIN)
                        asset.onchain_history.extend([OnChainEvent.from_dict(oe) for oe in asset_data['onchain_history']])
                    self.assets[symbol] = asset
                # Load analysis cache directly into report generator
                self.report_generator.analysis_cache.update(state.get('analysis_cache', {}))
                logger.info(f"Engine state loaded from {Config.STATE_FILE}")
            except Exception as e:
                logger.error(f"Error loading engine state: {e}", exc_info=True)
                logger.warning("Failed to load state. Starting fresh.")
        else:
            logger.info("No saved state found. Starting fresh.")

    def _analysis_loop(self):
        logger.info("Analysis loop started.")
        while not self._stop_event.is_set():
            current_timestamp = get_current_timestamp()
            logger.info(f"Running analysis cycle at {current_timestamp}")

            # Trigger analysis engines (some are event-driven, others run on schedule)
            # TA is mostly event-driven, but we can ensure a full sweep if needed
            for symbol in self.assets:
                self.technical_analysis_engine.run_analysis(symbol)
            
            self.risk_sentiment_engine.run_analysis(current_timestamp)
            self.narrative_analysis_engine.run_analysis(current_timestamp)
            self.crypto_equity_beta_engine.run_analysis(current_timestamp)
            self.lagged_event_impact_engine.run_analysis(current_timestamp)
            self.liquidity_flow_engine.run_analysis(current_timestamp)
            self.correlation_engine.run_analysis(current_timestamp)
            self.predictive_model_engine.run_analysis(current_timestamp)

            self._stop_event.wait(Config.ANALYSIS_INTERVAL)
        logger.info("Analysis loop stopped.")

    def _report_loop(self):
        logger.info("Report generation loop started.")
        while not self._stop_event.is_set():
            with self._lock: # Lock to ensure consistent data when generating report
                report = self.report_generator.generate_financial_report(list(self.assets.values()))
            
            if report:
                print("\n" + "="*80)
                print(report)
                print("="*80 + "\n")
                logger.info("Financial report generated and displayed.")
            else:
                logger.debug("Skipping report generation: too soon since last report.")

            self._stop_event.wait(Config.REPORT_GENERATION_INTERVAL)
        logger.info("Report generation loop stopped.")


    def start(self):
        logger.info("Financial Analysis Engine starting...")
        self.market_data_provider.start()
        self.news_data_provider.start()
        self.macro_data_provider.start()
        self.onchain_data_provider.start()

        self.simulator = MarketSimulator(self.event_dispatcher, self.assets) # Ensure simulator is initialized
        self.simulator.start()

        try:
            logger.info(f"Waiting for initial data collection ({Config.INITIAL_DATA_WAIT} seconds)...")
            time.sleep(Config.INITIAL_DATA_WAIT)

            self._analysis_thread = threading.Thread(target=self._analysis_loop, name="AnalysisLoopThread")
            self._analysis_thread.daemon = True
            self._analysis_thread.start()

            self._report_thread = threading.Thread(target=self._report_loop, name="ReportLoopThread")
            self._report_thread.daemon = True
            self._report_thread.start()
            
            logger.info("Engine fully operational. Press Ctrl+C to stop.")
            # Keep main thread alive
            while not self._stop_event.is_set():
                time.sleep(1) # Small sleep to prevent busy-waiting

        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received. Stopping engine.")
        finally:
            self.stop()

    def stop(self):
        self._stop_event.set() # Signal all threads to stop
        logger.info("Engine shutdown sequence initiated.")

        # Stop data providers
        self.market_data_provider.stop()
        self.news_data_provider.stop()
        self.macro_data_provider.stop()
        self.onchain_data_provider.stop()
        self.simulator.stop()

        # Wait for threads to finish
        if self._analysis_thread and self._analysis_thread.is_alive():
            self._analysis_thread.join(timeout=10)
        if self._report_thread and self._report_thread.is_alive():
            self._report_thread.join(timeout=10)

        self._save_state()
        logger.info("Financial Analysis Engine has stopped.")

# --- MARKET SIMULATOR (Placeholder/Mock for complex data generation) ---
class MarketSimulator(BaseDataProvider):
    """
    A simulated market data generator that can be influenced by system-wide
    sentiment (e.g., RORO score). This is a mock for a much more complex
    simulation environment in a real v3.0 engine.
    """
    def __init__(self, event_dispatcher, assets):
        super().__init__(event_dispatcher)
        self.assets = assets
        # Initialize current prices with some random variability
        self.current_prices = {symbol: random.uniform(100, 50000) for symbol in assets}
        self.event_dispatcher.subscribe('risk_sentiment_updated', self.handle_event)
        self.roro_score = 0.0 # Initial neutral RORO

    def handle_event(self, event_type, roro_data):
        self.roro_score = roro_data['score']
        logger.debug(f"Simulator received RORO update: {self.roro_score:.4f}")

    def _fetch_and_dispatch_data(self):
        # This method is the core loop for the simulator.
        logger.debug("Simulating market data...")
        current_time = get_current_timestamp()

        # Simulate price movement influenced by RORO score
        # Positive RORO means prices tend to go up, negative means down
        for symbol, asset_obj in self.assets.items():
            base_movement = random.uniform(-0.003, 0.003) # Baseline random daily movement (small)
            roro_influence = self.roro_score * 0.0015 # RORO can shift the movement (scaled influence)
            
            # Add asset-specific volatility/noise. Crypto generally more volatile.
            volatility_factor = 0.001 if asset_obj.asset_type == 'equity' else 0.005 # Higher volatility for crypto
            asset_specific_noise = random.uniform(-volatility_factor, volatility_factor)
            
            daily_change_factor = 1 + base_movement + roro_influence + asset_specific_noise
            
            # Ensure price doesn't go negative or too low
            self.current_prices[symbol] = max(1.0, self.current_prices[symbol] * daily_change_factor)

            # Generate OHLCV data based on the new current price
            close_p = self.current_prices[symbol]
            open_p = close_p * random.uniform(0.998, 1.002) # Open near close of previous (simulated)
            high_p = max(open_p, close_p) * random.uniform(1.0001, 1.005)
            low_p = min(open_p, close_p) * random.uniform(0.995, 0.9999)
            volume = random.uniform(50000, 10000000) # Varied volume

            dp = DataPoint(current_time, open_p, high_p, low_p, close_p, volume)
            self.event_dispatcher.dispatch('new_price_data', symbol=symbol, data_point=dp)
            logger.debug(f"Simulated new price for {symbol}: {close_p:.2f}")


# --- CLI & Entry Point ---
def run_engine(args):
    engine = FinancialAnalysisEngine(Config.ASSETS_TO_TRACK)
    engine.start()

def run_tests():
    logger.info("Running unit tests...")
    test_loader = unittest.TestLoader()
    test_suite = test_loader.loadTestsFromModule(sys.modules[__name__]) # Load tests from this module
    unittest.TextTestRunner(verbosity=2).run(test_suite)

class TeuCli:
    def __init__(self, engine):
        self.engine = engine

    def run(self):
        print("\n--- Teu.py CLI ---")
        print("Type 'help' for commands, 'exit' to quit.")
        while True:
            try:
                command = input("teu> ").strip().lower()
                if command == "exit":
                    print("Exiting CLI. Engine will continue to run in background (use Ctrl+C to stop).")
                    break
                elif command == "help":
                    self._show_help()
                elif command == "status":
                    self._show_status()
                elif command == "report":
                    self._generate_and_display_report_on_demand()
                elif command.startswith("set_interval "):
                    self._set_interval(command)
                elif command.startswith("predict "):
                    self._get_prediction(command)
                else:
                    print("Unknown command. Type 'help' for available commands.")
            except Exception as e:
                logger.error(f"Error in CLI: {e}", exc_info=True)
                print(f"An error occurred: {e}")

    def _show_help(self):
        print("\nAvailable Commands:")
        print("  help               - Show this help message.")
        print("  status             - Show current engine status and latest key metrics.")
        print("  report             - Generate and display a fresh financial report immediately.")
        print("  set_interval <type> <seconds> - Set update interval (e.g., 'set_interval analysis 300').")
        print("                             Types: 'data', 'analysis', 'report'.")
        print("  predict <symbol>   - Get the latest prediction for a specific asset (e.g., 'predict BTC').")
        print("  exit               - Exit the CLI. The engine will continue to run.")
        print("")

    def _show_status(self):
        print("\n--- Engine Status ---")
        roro_data = self.engine.report_generator.analysis_cache.get('risk_sentiment')
        if roro_data:
            print(f"Global Sentiment (RORO): {roro_data['status']} (Score: {roro_data['score']:.4f})")
        
        narratives = self.engine.report_generator.analysis_cache.get('narratives', {}).get('dominant_narratives')
        if narratives:
            print("Dominant Narratives:")
            for nar, data in narratives.items():
                print(f"  - {nar}: Strength {data['strength']:.2f}, Sentiment {data['sentiment']:.2f}")

        # Show latest price for a few assets
        print("\nLatest Asset Prices (last updated):")
        for symbol in Config.ASSETS_TO_TRACK[:3]: # Show for first 3 tracked assets
            ta_data = self.engine.report_generator.analysis_cache.get('technical_analysis', {}).get(symbol)
            if ta_data:
                print(f"  - {symbol}: {ta_data['close']:.2f} (Timestamp: {ta_data['timestamp'][:19]})")
            else:
                print(f"  - {symbol}: No price data yet.")
        print("")

    def _generate_and_display_report_on_demand(self):
        print("\nGenerating on-demand financial report...")
        with self.engine._lock: # Ensure no concurrent updates while generating
            report = self.engine.report_generator.generate_financial_report(list(self.engine.assets.values()))
        
        if report:
            print("\n" + "="*80)
            print(report)
            print("="*80 + "\n")
        else:
            print("Report not generated. Ensure enough data is available or check log for errors.")

    def _set_interval(self, command):
        parts = command.split()
        if len(parts) != 3:
            print("Usage: set_interval <type> <seconds>")
            return
        
        interval_type = parts[1]
        try:
            seconds = int(parts[2])
            if seconds <= 0:
                print("Interval seconds must be positive.")
                return
        except ValueError:
            print("Invalid seconds value. Must be an integer.")
            return

        if interval_type == "data":
            Config.DATA_PROVIDER_INTERVAL = seconds
            print(f"Data provider interval set to {seconds} seconds.")
        elif interval_type == "analysis":
            Config.ANALYSIS_INTERVAL = seconds
            print(f"Analysis interval set to {seconds} seconds.")
        elif interval_type == "report":
            Config.REPORT_GENERATION_INTERVAL = seconds
            print(f"Report generation interval set to {seconds} seconds.")
        else:
            print(f"Unknown interval type: {interval_type}. Supported types: 'data', 'analysis', 'report'.")

    def _get_prediction(self, command):
        parts = command.split()
        if len(parts) != 2:
            print("Usage: predict <symbol>")
            return
        
        symbol = parts[1].upper()
        predictions = self.engine.report_generator.analysis_cache.get('predictions', {})
        
        if symbol in predictions:
            pred_data = predictions[symbol]
            print(f"\n--- Latest Prediction for {symbol} ---")
            print(f"  Predicted Direction: {pred_data['predicted_direction']} (Confidence: {pred_data['confidence']:.2%})")
            print("  Price Targets:")
            for horizon, price in pred_data['price_targets'].items():
                print(f"    - {horizon}: {price:,.2f}")
            print(f"  Net Impact Score: {pred_data['net_impact_score']:.4f}")
            print(f"  Contributing Factors: Technical {pred_data['contributing_factors']['technical']:.2f}, Sentiment {pred_data['contributing_factors']['sentiment']:.2f}, Narrative {pred_data['contributing_factors']['narrative']:.2f}, Macro {pred_data['contributing_factors']['macro_event_impact']:.2f}")
            
            if pred_data['confidence'] >= Config.PREDICTIVE_CONFIDENCE_THRESHOLD:
                print("  Interpretation: Strong conviction prediction.")
            elif pred_data['confidence'] >= 0.5:
                print("  Interpretation: Moderate conviction prediction.")
            else:
                print("  Interpretation: Low conviction prediction.")

        else:
            print(f"No prediction available for {symbol}. Ensure symbol is tracked and engine has run a full cycle.")

if __name__ == "__main__":
    if '--test' in sys.argv:
        # Unit test suite would be expanded here.
        # For a full v3.0, extensive tests for each engine and data model would be present.
        class TestDataModels(unittest.TestCase):
            def test_data_point_serialization(self):
                dp = DataPoint("2025-06-17T10:00:00Z", 100.0, 105.0, 98.0, 102.0, 1000)
                dp_dict = dp.to_dict()
                self.assertIn('timestamp', dp_dict)
                self.assertEqual(dp_dict['close'], 102.0)
                reconstructed_dp = DataPoint.from_dict(dp_dict)
                self.assertEqual(reconstructed_dp.close, 102.0)

            def test_news_article_sentiment(self):
                na_pos = NewsArticle("2025-06-17T11:00:00Z", "Test", "Market Rallies on Breakthrough News", "Positive outlook")
                self.assertGreater(na_pos.sentiment_score, 0)
                na_neg = NewsArticle("2025-06-17T12:00:00Z", "Test", "Stocks Plunge Amidst Regulation Fears", "Negative news")
                self.assertLess(na_neg.sentiment_score, 0)

            def test_macro_event_surprise(self):
                me1 = MacroEconomicEvent("2025-06-17T13:00:00Z", "CPI", "Inflation report", 0.02, 0.025, 'high')
                self.assertAlmostEqual(me1.surprise_factor, 0.25) # (0.025 - 0.02) / 0.02
                me2 = MacroEconomicEvent("2025-06-17T14:00:00Z", "FED", "Rate decision", 0.01, 0.005, 'critical')
                self.assertAlmostEqual(me2.surprise_factor, -0.5)
                me3 = MacroEconomicEvent("2025-06-17T15:00:00Z", "GDP", "GDP report", 0.0, 0.01, 'medium')
                self.assertEqual(me3.surprise_factor, 0.0) # Expected 0, so surprise is 0

            def test_asset_dataframe_conversion(self):
                asset = Asset('TEST', 'equity')
                asset.add_data('price', DataPoint("2025-06-17T10:00:00Z", 100, 102, 99, 101, 1000))
                asset.add_data('price', DataPoint("2025-06-17T11:00:00Z", 101, 103, 100, 102, 1200))
                df = asset.get_price_dataframe()
                self.assertFalse(df.empty)
                self.assertEqual(len(df), 2)
                self.assertIn('close', df.columns)
                self.assertEqual(df['close'].iloc[1], 102)
        
        # Add a simple test for EventDispatcher
        class MockListener:
            def __init__(self): self.events = []
            def handle_event(self, event_type, *args, **kwargs): self.events.append((event_type, args, kwargs))

        class TestEventDispatcher(unittest.TestCase):
            def test_dispatch(self):
                dispatcher = EventDispatcher()
                listener = MockListener()
                dispatcher.subscribe('test_event', listener)
                dispatcher.dispatch('test_event', 'arg1', kwarg1='value1')
                self.assertEqual(len(listener.events), 1)
                self.assertEqual(listener.events[0][0], 'test_event')
                self.assertEqual(listener.events[0][1], ('arg1',))
                self.assertEqual(listener.events[0][2], {'kwarg1': 'value1'})

        run_tests()

    elif '--cli' in sys.argv:
        # Start the engine in a separate thread, then launch CLI in main thread
        engine_instance = FinancialAnalysisEngine(Config.ASSETS_TO_TRACK)
        engine_thread = threading.Thread(target=engine_instance.start, name="EngineMainThread")
        engine_thread.daemon = True # Allow main program to exit even if this thread is running
        engine_thread.start()
        
        # Give engine a moment to start up and log its initial state
        time.sleep(5) 

        cli = TeuCli(engine_instance)
        cli.run()
        
        # If CLI exits, signal engine to stop and join its thread
        engine_instance.stop()
        engine_thread.join(timeout=10) # Wait for engine to properly shut down
        logger.info("Main thread exiting.")

    else:
        # Default behavior: run the engine directly
        run_engine(sys.argv)