# BEAUTIFUL WHITE THEME TiT LAUNCHER
# Version: 1.0.1 (WHITE THEME WITH GREEN DOTS)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON>
# Date: 2025-06-17
#
# Beautiful white theme launcher with green "READY" dots for all 7 separate apps

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os

class BeautifulWhiteLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("✨ TiT Suite 1.0.1 - Professional Financial Intelligence Launcher ✨")
        self.root.geometry("1400x900")
        self.root.configure(bg='#FFFFFF')
        self.root.resizable(True, True)

        # Center window
        self.center_window()

        # Create beautiful UI
        self.create_beautiful_ui()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_beautiful_ui(self):
        """Create the beautiful white theme UI"""
        # Main container with white background
        main_frame = tk.Frame(self.root, bg='#FFFFFF', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header section
        self.create_header(main_frame)
        
        # Applications grid
        self.create_apps_grid(main_frame)
        
        # Footer section
        self.create_footer(main_frame)
    
    def create_header(self, parent):
        """Create beautiful header with white theme"""
        header_frame = tk.Frame(parent, bg='#FFFFFF')
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # Main title
        title_label = tk.Label(
            header_frame,
            text="🚀 TiT FINANCIAL INTELLIGENCE SUITE 🚀",
            font=("Segoe UI", 24, "bold"),
            fg='#2C3E50',
            bg='#FFFFFF'
        )
        title_label.pack(pady=(0, 10))
        
        # Subtitle
        subtitle_label = tk.Label(
            header_frame,
            text="💎 Advanced Market Intelligence & AI Analysis Platform 💎",
            font=("Segoe UI", 16, "italic"),
            fg='#3498DB',
            bg='#FFFFFF'
        )
        subtitle_label.pack(pady=(0, 10))
        
        # Version
        version_label = tk.Label(
            header_frame,
            text="⭐ Version 1.0.1 - Professional Edition ⭐",
            font=("Segoe UI", 12, "bold"),
            fg='#27AE60',
            bg='#FFFFFF'
        )
        version_label.pack(pady=(0, 15))
        
        # Author section with border
        author_frame = tk.LabelFrame(
            header_frame, 
            text="👨‍💻 Created By",
            font=("Segoe UI", 11, "bold"),
            fg='#2C3E50',
            bg='#FFFFFF',
            relief=tk.RIDGE,
            bd=2
        )
        author_frame.pack(pady=(10, 0))
        
        author_label = tk.Label(
            author_frame,
            text="🎯 Anh Quang 🎯",
            font=("Segoe UI", 14, "bold"),
            fg='#E74C3C',
            bg='#FFFFFF'
        )
        author_label.pack(padx=20, pady=10)
    
    def create_apps_grid(self, parent):
        """Create beautiful apps grid with white theme"""
        apps_frame = tk.LabelFrame(
            parent, 
            text="Available Applications",
            font=("Segoe UI", 14, "bold"),
            fg='#2C3E50',
            bg='#FFFFFF',
            relief=tk.RIDGE,
            bd=2
        )
        apps_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Grid container with scrollable canvas
        canvas = tk.Canvas(apps_frame, bg='#FFFFFF', highlightthickness=0)
        scrollbar = ttk.Scrollbar(apps_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#FFFFFF')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=15, pady=15)
        scrollbar.pack(side="right", fill="y")

        # Configure grid weights for 3 columns in scrollable frame
        for i in range(3):
            scrollable_frame.columnconfigure(i, weight=1, minsize=400)
        for i in range(3):
            scrollable_frame.rowconfigure(i, weight=1, minsize=200)
        
        # Define ALL 7 apps with complete information
        self.applications = [
            {
                'name': 'TiT Crypto App',
                'description': 'Advanced Cryptocurrency Intelligence Suite',
                'file': 'Teu 1.0.1 MAin dancer.py',
                'icon': '₿',
                'color': '#F7931A',
                'features': ['Real-time crypto prices', 'DeFi analytics', 'News intelligence', 'AI predictions']
            },
            {
                'name': 'TiT Stock App',
                'description': 'Global Stock Market Intelligence Suite',
                'file': 'TiT_Stock_App_1.0.1.py',
                'icon': '📈',
                'color': '#2E8B57',
                'features': ['20+ countries coverage', 'Real-time indices', 'Earnings calendar', 'AI analysis']
            },
            {
                'name': 'TiT Oil App',
                'description': 'Oil Market Intelligence Suite',
                'file': 'TiT_Oil_App_1.0.1.py',
                'icon': '🛢️',
                'color': '#8B4513',
                'features': ['Oil futures tracking', 'OPEC analysis', 'Energy companies', 'Geopolitical impact']
            },
            {
                'name': 'TiT Gold App',
                'description': 'Precious Metals Intelligence Suite',
                'file': 'TiT_Gold_App_1.0.1.py',
                'icon': '🥇',
                'color': '#FFD700',
                'features': ['Precious metals prices', 'Mining companies', 'Central bank data', 'Safe haven analysis']
            },
            {
                'name': 'TiT Health App',
                'description': 'Health & Biotech Intelligence Suite',
                'file': 'TiT_Health_App_1.0.1.py',
                'icon': '🧬',
                'color': '#2E8B57',
                'features': ['Biotech companies', 'Drug pipeline', 'FDA approvals', 'Healthcare trends']
            },
            {
                'name': 'TiT Defense App',
                'description': 'Geopolitical & Defense Intelligence Suite',
                'file': 'TiT_Defense_App_1.0.1.py',
                'icon': '⚔️',
                'color': '#2F4F4F',
                'features': ['Defense contractors', 'Conflict monitoring', 'Arms trade', 'Geopolitical analysis']
            },
            {
                'name': 'TiT Science App',
                'description': 'Science, Technology & Space Intelligence Suite',
                'file': 'TiT_Science_App_1.0.1.py',
                'icon': '🚀',
                'color': '#4169E1',
                'features': ['Tech companies', 'Space exploration', 'AI developments', 'Innovation tracking']
            }
        ]
        
        # Create app cards in 3x3 grid
        row, col = 0, 0
        for app in self.applications:
            self.create_beautiful_app_card(scrollable_frame, app, row, col)

            col += 1
            if col >= 3:
                col = 0
                row += 1
    
    def create_beautiful_app_card(self, parent, app, row, col):
        """Create beautiful app card with white theme and green dots"""
        # Card frame with white background and border
        card_frame = tk.LabelFrame(
            parent,
            text=f"  {app['icon']} {app['name']}  ",
            font=("Segoe UI", 12, "bold"),
            fg='#2C3E50',
            bg='#FFFFFF',
            relief=tk.RIDGE,
            bd=2
        )
        card_frame.grid(row=row, column=col, padx=15, pady=15, sticky="nsew", ipadx=10, ipady=10)
        
        # Status with GREEN DOT
        status_frame = tk.Frame(card_frame, bg='#FFFFFF')
        status_frame.pack(fill=tk.X, padx=10, pady=(5, 0))
        
        status_label = tk.Label(
            status_frame,
            text="🟢 READY",
            font=("Segoe UI", 10, "bold"),
            fg='#27AE60',
            bg='#FFFFFF'
        )
        status_label.pack(side=tk.RIGHT)
        
        # Large icon
        icon_frame = tk.Frame(card_frame, bg='#FFFFFF')
        icon_frame.pack(fill=tk.X, padx=10, pady=(10, 5))
        
        large_icon_label = tk.Label(
            icon_frame,
            text=app['icon'],
            font=("Segoe UI", 36),
            bg='#FFFFFF'
        )
        large_icon_label.pack()
        
        # Description
        desc_label = tk.Label(
            card_frame,
            text=f"📋 {app['description']}",
            font=("Segoe UI", 10, "italic"),
            wraplength=350,
            fg='#2C3E50',
            bg='#FFFFFF',
            justify=tk.CENTER
        )
        desc_label.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Features section
        features_frame = tk.LabelFrame(
            card_frame, 
            text="✨ Key Features",
            font=("Segoe UI", 9, "bold"),
            fg='#2C3E50',
            bg='#FFFFFF'
        )
        features_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        for i, feature in enumerate(app['features']):
            feature_emojis = ['🎯', '⚡', '🔥', '💎']
            emoji = feature_emojis[i % len(feature_emojis)]
            
            feature_label = tk.Label(
                features_frame,
                text=f"{emoji} {feature}",
                font=("Segoe UI", 9),
                fg='#34495E',
                bg='#FFFFFF'
            )
            feature_label.pack(anchor=tk.W, padx=8, pady=1)
        
        # Launch button
        button_frame = tk.Frame(card_frame, bg='#FFFFFF')
        button_frame.pack(fill=tk.X, padx=10, pady=(5, 10))

        launch_btn = tk.Button(
            button_frame,
            text=f"🚀 LAUNCH",
            font=("Segoe UI", 12, "bold"),
            bg='#3498DB',
            fg='white',
            relief=tk.RAISED,
            bd=3,
            height=2,
            cursor="hand2",
            command=lambda a=app: self.launch_application(a)
        )
        launch_btn.pack(fill=tk.X, pady=(0, 5))
        
        # File status
        file_status_label = tk.Label(
            button_frame,
            text=f"📁 {app['file']}",
            font=("Segoe UI", 8),
            fg='#7F8C8D',
            bg='#FFFFFF'
        )
        file_status_label.pack(pady=(2, 0))
    
    def create_footer(self, parent):
        """Create beautiful footer with white theme"""
        footer_frame = tk.Frame(parent, bg='#FFFFFF')
        footer_frame.pack(fill=tk.X)
        
        # Control buttons
        controls_frame = tk.LabelFrame(
            footer_frame, 
            text="🎮 Control Center",
            font=("Segoe UI", 12, "bold"),
            fg='#2C3E50',
            bg='#FFFFFF',
            relief=tk.RIDGE,
            bd=2
        )
        controls_frame.pack(fill=tk.X, pady=(0, 15))
        
        button_container = tk.Frame(controls_frame, bg='#FFFFFF')
        button_container.pack(fill=tk.X, padx=15, pady=15)
        
        # Launch All button
        launch_all_btn = tk.Button(
            button_container,
            text="🚀 LAUNCH ALL APPLICATIONS",
            font=("Segoe UI", 12, "bold"),
            bg='#27AE60',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            command=self.launch_all_applications
        )
        launch_all_btn.pack(side=tk.LEFT, padx=(0, 15))
        
        # Exit button
        exit_btn = tk.Button(
            button_container,
            text="❌ EXIT LAUNCHER",
            font=("Segoe UI", 12, "bold"),
            bg='#E74C3C',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            command=self.root.quit
        )
        exit_btn.pack(side=tk.RIGHT)
        
        # Status section
        status_frame = tk.LabelFrame(
            footer_frame, 
            text="📋 System Information",
            font=("Segoe UI", 10, "bold"),
            fg='#2C3E50',
            bg='#FFFFFF',
            relief=tk.RIDGE,
            bd=2
        )
        status_frame.pack(fill=tk.X)
        
        # Copyright
        copyright_label = tk.Label(
            status_frame,
            text="© 2025 Nguyen Le Vinh Quang. All rights reserved. | Professional Financial Intelligence Suite",
            font=("Segoe UI", 9, "italic"),
            fg='#7F8C8D',
            bg='#FFFFFF'
        )
        copyright_label.pack(side=tk.LEFT, padx=10, pady=8)
        
        # Version
        version_label = tk.Label(
            status_frame,
            text="🎯 TiT Suite v1.0.1 Professional",
            font=("Segoe UI", 9, "bold"),
            fg='#2C3E50',
            bg='#FFFFFF'
        )
        version_label.pack(side=tk.RIGHT, padx=10, pady=8)
    
    def launch_application(self, app):
        """Launch a specific application"""
        try:
            app_file = app['file']
            
            if not os.path.exists(app_file):
                messagebox.showerror(
                    "❌ File Not Found",
                    f"Application file '{app_file}' not found.\n\n"
                    f"📁 Please ensure all TiT apps are in the same directory."
                )
                return
            
            # Launch the app
            process = subprocess.Popen([sys.executable, app_file])
            
            messagebox.showinfo(
                "🚀 Launch Successful!",
                f"✅ {app['name']} is starting!\n\n"
                f"⚡ Application window will appear shortly\n"
                f"🎯 Process ID: {process.pid}"
            )
            
        except Exception as e:
            messagebox.showerror("❌ Launch Error", f"Failed to launch {app['name']}:\n\n{str(e)}")
    
    def launch_all_applications(self):
        """Launch all applications"""
        try:
            result = messagebox.askyesno(
                "Launch All Applications",
                "This will launch all 7 TiT applications.\n\nContinue?"
            )
            
            if not result:
                return
            
            launched = 0
            failed = []
            
            for app in self.applications:
                try:
                    if os.path.exists(app['file']):
                        subprocess.Popen([sys.executable, app['file']])
                        launched += 1
                    else:
                        failed.append(f"{app['name']} (file not found)")
                except Exception as e:
                    failed.append(f"{app['name']} (error: {str(e)})")
            
            message = f"✅ Successfully launched {launched} applications!"
            if failed:
                message += f"\n\n❌ Failed to launch:\n" + "\n".join(failed)
            
            messagebox.showinfo("Launch Results", message)
            
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to launch applications: {str(e)}")

def main():
    """Main function"""
    print("🚀 Starting BEAUTIFUL WHITE THEME TiT Launcher...")
    print("💎 7 SEPARATE APPS with GREEN READY DOTS")
    print("👨‍💻 Created by Anh Quang")

    root = tk.Tk()
    launcher = BeautifulWhiteLauncher(root)

    print("✅ Beautiful launcher ready!")
    root.mainloop()

if __name__ == "__main__":
    main()
