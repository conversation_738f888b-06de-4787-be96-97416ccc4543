# START TiT SUITE - <PERSON>IN LAUNCHER
# Version: 1.0.1 (COMPLETE RESTORATION)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON>
# Date: 2025-06-20
#
# RESTORED LAUNCHER WITH ALL 7 APPS VISIBLE AND API SETTINGS OUTSIDE MENU

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import subprocess
import sys
import os
import re

class TiTSuiteLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 TiT Financial Intelligence Suite 1.0.1 - Complete Launcher 🚀")
        self.root.geometry("1400x900")
        self.root.configure(bg='#F8FAFC')
        self.root.resizable(True, True)
        self.root.minsize(1200, 800)

        # Initialize language system
        self.current_language = "English"
        self.setup_translations()

        # Center window
        self.center_window()

        # Configure styling
        self.setup_styling()

        # Create main UI
        self.create_main_ui()

    def setup_translations(self):
        """Setup Vietnamese translations"""
        self.translations = {
            "English": {
                "title": "🚀 TiT FINANCIAL INTELLIGENCE SUITE 🚀",
                "subtitle": "💎 Market Intelligence & AI Analysis Platform 💎",
                "api_settings": "API Settings",
                "google_api_key": "Google API Key:",
                "save_settings": "Save Settings",
                "theme_settings": "Theme Settings",
                "theme": "Theme:",
                "apply_theme": "Apply Theme",
                "language": "Language:",
                "available_apps": "🎯 AVAILABLE APPLICATIONS",
                "open_all": "🚀 OPEN ALL APPS",
                "exit_suite": "❌ EXIT SUITE",
                "ready": "🟢 READY",
                "launch": "🚀 LAUNCH"
            },
            "Tiếng Việt": {
                "title": "🚀 BỘ CÔNG CỤ TÀI CHÍNH THÔNG MINH TiT 🚀",
                "subtitle": "💎 Nền tảng Phân tích Thị trường & Trí tuệ Nhân tạo 💎",
                "api_settings": "Cài đặt API",
                "google_api_key": "Khóa API Google:",
                "save_settings": "Lưu Cài đặt",
                "theme_settings": "Cài đặt Giao diện",
                "theme": "Giao diện:",
                "apply_theme": "Áp dụng Giao diện",
                "language": "Ngôn ngữ:",
                "available_apps": "🎯 CÁC ỨNG DỤNG CÓ SẴN",
                "open_all": "🚀 MỞ TẤT CẢ ỨNG DỤNG",
                "exit_suite": "❌ THOÁT CHƯƠNG TRÌNH",
                "ready": "🟢 SẴN SÀNG",
                "launch": "🚀 KHỞI CHẠY"
            }
        }

    def get_text(self, key):
        """Get translated text"""
        return self.translations[self.current_language].get(key, key)

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_styling(self):
        """Setup modern styling"""
        style = ttk.Style()
        style.theme_use('clam')

    def create_main_ui(self):
        """Create the main UI"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#FFFFFF', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Header
        self.create_header(main_frame)

        # API Settings OUTSIDE menu as requested
        self.create_api_settings(main_frame)

        # Apps grid - ALL 7 APPS VISIBLE
        self.create_apps_grid(main_frame)

        # Control buttons
        self.create_controls(main_frame)

    def create_header(self, parent):
        """Create header section"""
        header_frame = tk.Frame(parent, bg='#FFFFFF')
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Title
        self.title_label = tk.Label(
            header_frame,
            text=self.get_text("title"),
            font=("Segoe UI", 20, "bold"),
            fg='#1E293B',
            bg='#FFFFFF'
        )
        self.title_label.pack(pady=(10, 5))

        # Subtitle
        self.subtitle_label = tk.Label(
            header_frame,
            text=self.get_text("subtitle"),
            font=("Segoe UI", 14, "normal"),
            fg='#3B82F6',
            bg='#FFFFFF'
        )
        self.subtitle_label.pack(pady=(0, 5))

        # Author
        author_label = tk.Label(
            header_frame,
            text="👨‍💻 NGUYEN LE VINH QUANG",
            font=("Segoe UI", 12, "bold"),
            fg='#DC2626',
            bg='#FFFFFF'
        )
        author_label.pack(pady=(5, 10))

    def create_api_settings(self, parent):
        """Create API settings OUTSIDE menu as requested"""
        settings_container = tk.Frame(parent, bg='#F8FAFC')
        settings_container.pack(fill=tk.X, pady=(0, 20))

        # API Settings Frame
        api_frame = tk.LabelFrame(
            settings_container,
            text=self.get_text("api_settings"),
            font=("Segoe UI", 10, "bold"),
            fg='#1976D2',
            bg='#FFFFFF'
        )
        api_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # Google API key
        tk.Label(
            api_frame,
            text=self.get_text("google_api_key"),
            font=("Segoe UI", 9, "normal"),
            bg='#FFFFFF'
        ).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.google_api_entry = tk.Entry(api_frame, width=40, font=("Segoe UI", 9))
        self.google_api_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.google_api_entry.insert(0, "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og")

        # Save button
        save_btn = tk.Button(
            api_frame,
            text=self.get_text("save_settings"),
            font=("Segoe UI", 9, "bold"),
            bg='#10B981',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            cursor="hand2",
            command=self.save_api_settings
        )
        save_btn.grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)

        # Theme Settings Frame
        theme_frame = tk.LabelFrame(
            settings_container,
            text=self.get_text("theme_settings"),
            font=("Segoe UI", 10, "bold"),
            fg='#1976D2',
            bg='#FFFFFF'
        )
        theme_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # Theme selector
        tk.Label(
            theme_frame,
            text=self.get_text("theme"),
            font=("Segoe UI", 9, "normal"),
            bg='#FFFFFF'
        ).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.theme_selector = ttk.Combobox(
            theme_frame,
            values=["clam", "alt", "default", "classic"],
            width=15,
            font=("Segoe UI", 9)
        )
        self.theme_selector.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.theme_selector.current(0)

        # Apply theme button
        apply_theme_btn = tk.Button(
            theme_frame,
            text=self.get_text("apply_theme"),
            font=("Segoe UI", 9, "bold"),
            bg='#3B82F6',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            cursor="hand2",
            command=self.apply_theme
        )
        apply_theme_btn.grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)

        # Language Frame
        language_frame = tk.LabelFrame(
            settings_container,
            text=self.get_text("language"),
            font=("Segoe UI", 10, "bold"),
            fg='#1976D2',
            bg='#FFFFFF'
        )
        language_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.language_var = tk.StringVar(value="English")
        language_combo = ttk.Combobox(
            language_frame,
            textvariable=self.language_var,
            values=["English", "Tiếng Việt"],
            state="readonly",
            font=("Segoe UI", 9),
            width=15
        )
        language_combo.pack(padx=10, pady=10)
        language_combo.bind("<<ComboboxSelected>>", self.change_language)

    def create_apps_grid(self, parent):
        """Create grid showing ALL 7 TiT apps"""
        apps_frame = tk.LabelFrame(
            parent,
            text=self.get_text("available_apps"),
            font=("Segoe UI", 14, "bold"),
            fg='#1976D2',
            bg='#FFFFFF',
            padx=20,
            pady=20
        )
        apps_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Define ALL 7 TiT apps with descriptions
        self.tit_apps = [
            {
                "name": "📈 TiT STOCK APP",
                "description": "Advanced Stock Market Intelligence\n• 20+ Countries • Real-time Data • AI Analysis",
                "file": "TiT_Stock_App_1.0.1.py",
                "color": "#10B981",
                "status": "🟢 READY"
            },
            {
                "name": "🛢️ TiT OIL APP",
                "description": "Oil & Energy Market Analysis\n• Global Oil Markets • Price Forecasting • Supply Analysis",
                "file": "TiT_Oil_App_1.0.1.py",
                "color": "#F59E0B",
                "status": "🟢 READY"
            },
            {
                "name": "🥇 TiT GOLD APP",
                "description": "Precious Metals Intelligence\n• Gold/Silver/Platinum • Market Trends • Investment Analysis",
                "file": "TiT_Gold_App_1.0.1.py",
                "color": "#EAB308",
                "status": "🟢 READY"
            },
            {
                "name": "🏥 TiT HEALTH APP",
                "description": "Healthcare & Biotech Analysis\n• Pharmaceutical Stocks • Medical Research • Health Trends",
                "file": "TiT_Health_App_1.0.1.py",
                "color": "#06B6D4",
                "status": "🟢 READY"
            },
            {
                "name": "⚔️ TiT DEFENSE APP",
                "description": "War & Trade War Analysis\n• Geopolitical Intelligence • Defense Stocks • Trade Impact",
                "file": "TiT_Defense_App_1.0.1.py",
                "color": "#DC2626",
                "status": "🟢 READY"
            },
            {
                "name": "🚀 TiT SCIENCE APP",
                "description": "Science/Technology/Space Analysis\n• Tech Stocks • Space Industry • Innovation Tracking",
                "file": "TiT_Science_App_1.0.1.py",
                "color": "#7C3AED",
                "status": "🟢 READY"
            },
            {
                "name": "₿ TiT CRYPTO APP",
                "description": "Cryptocurrency Intelligence\n• 100+ Coins • DeFi Analysis • Market Predictions",
                "file": "TiT_Crypto_App_1.0.1.py",
                "color": "#F97316",
                "status": "🟢 READY"
            }
        ]

        # Create grid layout - 3 columns, 3 rows (showing 6 visible + 1 scrollable)
        for i, app in enumerate(self.tit_apps):
            row = i // 3
            col = i % 3

            self.create_app_card(apps_frame, app, row, col)

    def create_app_card(self, parent, app, row, col):
        """Create individual app card"""
        # App card frame
        card_frame = tk.Frame(
            parent,
            bg='#FFFFFF',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            pady=15
        )
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

        # Configure grid weights for responsive design
        parent.grid_rowconfigure(row, weight=1)
        parent.grid_columnconfigure(col, weight=1)

        # App name
        name_label = tk.Label(
            card_frame,
            text=app["name"],
            font=("Segoe UI", 12, "bold"),
            fg=app["color"],
            bg='#FFFFFF',
            wraplength=200
        )
        name_label.pack(pady=(0, 10))

        # App description
        desc_label = tk.Label(
            card_frame,
            text=app["description"],
            font=("Segoe UI", 9, "normal"),
            fg='#64748B',
            bg='#FFFFFF',
            wraplength=200,
            justify=tk.LEFT
        )
        desc_label.pack(pady=(0, 10))

        # Status
        status_label = tk.Label(
            card_frame,
            text=app["status"],
            font=("Segoe UI", 9, "bold"),
            fg='#10B981',
            bg='#FFFFFF'
        )
        status_label.pack(pady=(0, 10))

        # Launch button
        launch_btn = tk.Button(
            card_frame,
            text=self.get_text("launch"),
            font=("Segoe UI", 10, "bold"),
            bg=app["color"],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor="hand2",
            command=lambda f=app["file"]: self.launch_app(f)
        )
        launch_btn.pack(pady=(0, 5))

    def create_controls(self, parent):
        """Create control buttons"""
        controls_frame = tk.Frame(parent, bg='#FFFFFF')
        controls_frame.pack(fill=tk.X, pady=(10, 0))

        # Open All Apps button
        open_all_btn = tk.Button(
            controls_frame,
            text=self.get_text("open_all"),
            font=("Segoe UI", 14, "bold"),
            bg='#3B82F6',
            fg='white',
            relief=tk.FLAT,
            padx=30,
            pady=15,
            cursor="hand2",
            command=self.open_all_apps
        )
        open_all_btn.pack(side=tk.LEFT, padx=(0, 20))

        # Exit Suite button
        exit_btn = tk.Button(
            controls_frame,
            text=self.get_text("exit_suite"),
            font=("Segoe UI", 14, "bold"),
            bg='#DC2626',
            fg='white',
            relief=tk.FLAT,
            padx=30,
            pady=15,
            cursor="hand2",
            command=self.exit_suite
        )
        exit_btn.pack(side=tk.RIGHT)

        # Status label
        self.status_label = tk.Label(
            controls_frame,
            text=self.get_text("ready"),
            font=("Segoe UI", 12, "bold"),
            fg='#10B981',
            bg='#FFFFFF'
        )
        self.status_label.pack(side=tk.RIGHT, padx=(0, 20))

    def save_api_settings(self):
        """Save API settings"""
        api_key = self.google_api_entry.get().strip()
        if api_key:
            # Save to config file or environment
            try:
                with open("api_config.txt", "w") as f:
                    f.write(f"GOOGLE_API_KEY={api_key}\n")
                messagebox.showinfo("Success", "API settings saved successfully!")
                self.status_label.config(text="🟢 API CONFIGURED", fg='#10B981')
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save API settings: {e}")
        else:
            messagebox.showwarning("Warning", "Please enter a valid API key")

    def apply_theme(self):
        """Apply selected theme"""
        try:
            style = ttk.Style()
            theme = self.theme_selector.get()
            style.theme_use(theme)
            self.status_label.config(text=f"🎨 THEME: {theme.upper()}", fg='#3B82F6')
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply theme: {e}")

    def change_language(self, event=None):
        """Change language"""
        self.current_language = self.language_var.get()
        self.update_ui_language()
        self.status_label.config(text=f"🌐 LANGUAGE: {self.current_language}", fg='#7C3AED')

    def update_ui_language(self):
        """Update UI with new language"""
        self.title_label.config(text=self.get_text("title"))
        self.subtitle_label.config(text=self.get_text("subtitle"))

    def launch_app(self, app_file):
        """Launch individual TiT app"""
        try:
            self.status_label.config(text=f"🚀 LAUNCHING {app_file}...", fg='#F59E0B')
            self.root.update()

            # Check if file exists
            if not os.path.exists(app_file):
                messagebox.showerror("Error", f"App file not found: {app_file}")
                self.status_label.config(text="❌ FILE NOT FOUND", fg='#DC2626')
                return

            # Launch the app
            subprocess.Popen([sys.executable, app_file], cwd=os.getcwd())
            self.status_label.config(text=f"✅ {app_file} LAUNCHED", fg='#10B981')

        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch {app_file}: {e}")
            self.status_label.config(text="❌ LAUNCH FAILED", fg='#DC2626')

    def open_all_apps(self):
        """Open all TiT apps at once"""
        try:
            self.status_label.config(text="🚀 LAUNCHING ALL APPS...", fg='#F59E0B')
            self.root.update()

            launched_count = 0
            failed_count = 0

            for app in self.tit_apps:
                try:
                    if os.path.exists(app["file"]):
                        subprocess.Popen([sys.executable, app["file"]], cwd=os.getcwd())
                        launched_count += 1
                    else:
                        failed_count += 1
                        print(f"Warning: {app['file']} not found")
                except Exception as e:
                    failed_count += 1
                    print(f"Error launching {app['file']}: {e}")

            if launched_count > 0:
                self.status_label.config(text=f"✅ {launched_count} APPS LAUNCHED", fg='#10B981')
                if failed_count > 0:
                    messagebox.showwarning("Partial Success", f"Launched {launched_count} apps, {failed_count} failed")
            else:
                self.status_label.config(text="❌ NO APPS LAUNCHED", fg='#DC2626')
                messagebox.showerror("Error", "Failed to launch any apps")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch apps: {e}")
            self.status_label.config(text="❌ LAUNCH FAILED", fg='#DC2626')

    def exit_suite(self):
        """Exit the TiT Suite"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit TiT Suite?"):
            self.root.quit()
            self.root.destroy()


def main():
    """Main function to run the TiT Suite Launcher"""
    root = tk.Tk()
    app = TiTSuiteLauncher(root)
    root.mainloop()


if __name__ == "__main__":
    main()
