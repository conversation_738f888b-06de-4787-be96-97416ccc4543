# Enhanced News Integration Module for TiT Suite
# Globe and Mail priority news with comprehensive display
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.

import requests
import logging
import time
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
import re
from concurrent.futures import ThreadPoolExecutor, as_completed

class EnhancedNewsEngine:
    """Enhanced news engine with Globe and Mail priority and comprehensive display"""
    
    def __init__(self):
        self.news_cache = {}
        self.cache_duration = 900  # 15 minutes
        self.thread_pool = ThreadPoolExecutor(max_workers=5)
        self.rate_limiter = NewsRateLimiter()
        
        # News sources with Globe and Mail priority
        self.news_sources = {
            'globe_and_mail': {
                'name': 'The Globe and Mail',
                'priority': 1,
                'url_template': 'https://www.theglobeandmail.com/business/',
                'api_key': None,
                'enabled': True
            },
            'reuters': {
                'name': 'Reuters',
                'priority': 2,
                'url_template': 'https://www.reuters.com/business/',
                'api_key': None,
                'enabled': True
            },
            'bloomberg': {
                'name': 'Bloomberg',
                'priority': 3,
                'url_template': 'https://www.bloomberg.com/markets',
                'api_key': None,
                'enabled': True
            },
            'financial_post': {
                'name': 'Financial Post',
                'priority': 4,
                'url_template': 'https://financialpost.com/',
                'api_key': None,
                'enabled': True
            }
        }
        
        logging.info("✅ Enhanced news engine initialized with Globe and Mail priority")
    
    def get_comprehensive_news(self, sector: str = "general", count: int = 50) -> List[Dict]:
        """Get comprehensive news with 30-50 articles and clickable light blue links"""
        cache_key = f"news_{sector}_{count}"
        
        # Check cache
        if self._is_cached(cache_key):
            return self.news_cache[cache_key]
        
        all_news = []
        
        # Get news from multiple sources in parallel
        futures = {}
        for source_id, source_config in self.news_sources.items():
            if source_config['enabled']:
                future = self.thread_pool.submit(
                    self._fetch_source_news, 
                    source_id, 
                    source_config, 
                    sector, 
                    count // len(self.news_sources)
                )
                futures[future] = source_id
        
        # Collect results
        for future in as_completed(futures, timeout=30):
            source_id = futures[future]
            try:
                source_news = future.result()
                if source_news:
                    all_news.extend(source_news)
            except Exception as e:
                logging.error(f"❌ Failed to fetch news from {source_id}: {e}")
        
        # Sort by priority and timestamp
        all_news.sort(key=lambda x: (x.get('priority', 999), -x.get('timestamp', 0)))
        
        # Limit to requested count
        final_news = all_news[:count]
        
        # Add clickable light blue links
        for article in final_news:
            article['clickable_link'] = self._create_clickable_link(article)
        
        # Cache results
        self.news_cache[cache_key] = final_news
        
        logging.info(f"✅ Retrieved {len(final_news)} news articles with Globe and Mail priority")
        return final_news
    
    def _fetch_source_news(self, source_id: str, source_config: Dict, sector: str, count: int) -> List[Dict]:
        """Fetch news from a specific source"""
        try:
            self.rate_limiter.wait_if_needed()
            
            # Generate mock news data (in real implementation, would fetch from actual APIs)
            news_articles = self._generate_mock_news(source_id, source_config, sector, count)
            
            return news_articles
            
        except Exception as e:
            logging.error(f"❌ Error fetching from {source_id}: {e}")
            return []
    
    def _generate_mock_news(self, source_id: str, source_config: Dict, sector: str, count: int) -> List[Dict]:
        """Generate mock news data (replace with real API calls in production)"""
        articles = []
        
        # Globe and Mail gets priority treatment
        if source_id == 'globe_and_mail':
            priority = 1
            base_titles = [
                "TSX rises as energy stocks gain on oil price surge",
                "Bank of Canada signals potential rate cuts amid economic slowdown",
                "Canadian housing market shows signs of stabilization",
                "Tech sector leads Toronto market gains",
                "Resource stocks climb on commodity price strength",
                "Canadian dollar strengthens against US dollar",
                "Manufacturing sector shows resilience in latest data",
                "Energy transition investments boost clean tech stocks",
                "Financial sector earnings exceed expectations",
                "Infrastructure spending drives construction stocks higher"
            ]
        else:
            priority = source_config['priority']
            base_titles = [
                f"Market analysis from {source_config['name']}",
                f"Economic outlook update - {source_config['name']}",
                f"Sector rotation trends - {source_config['name']}",
                f"Global market impact - {source_config['name']}",
                f"Investment strategy update - {source_config['name']}"
            ]
        
        for i in range(min(count, len(base_titles))):
            article = {
                'title': base_titles[i % len(base_titles)],
                'description': f"Comprehensive analysis of market conditions and investment opportunities. {source_config['name']} provides detailed insights into current market trends and future outlook.",
                'url': f"{source_config['url_template']}article-{i+1}",
                'source': source_config['name'],
                'priority': priority,
                'timestamp': int(time.time()) - (i * 300),  # 5 minutes apart
                'published_at': datetime.now() - timedelta(minutes=i*5),
                'sector': sector,
                'impact_score': 8.5 - (i * 0.1),  # Decreasing impact
                'sentiment': 'positive' if i % 3 == 0 else 'neutral' if i % 3 == 1 else 'negative'
            }
            articles.append(article)
        
        return articles
    
    def _create_clickable_link(self, article: Dict) -> str:
        """Create clickable light blue link HTML"""
        url = article.get('url', '#')
        title = article.get('title', 'News Article')
        
        # Create light blue clickable link
        clickable_html = f'<a href="{url}" style="color: #4A90E2; text-decoration: underline; cursor: pointer;" target="_blank">{title}</a>'
        
        return clickable_html
    
    def get_sector_news(self, sector: str, count: int = 30) -> List[Dict]:
        """Get sector-specific news"""
        return self.get_comprehensive_news(sector, count)
    
    def _is_cached(self, cache_key: str) -> bool:
        """Check if news is cached and still valid"""
        if cache_key not in self.news_cache:
            return False
        
        # Check if cache is still valid (15 minutes)
        cached_time = getattr(self.news_cache[cache_key], '_cache_time', 0)
        return (time.time() - cached_time) < self.cache_duration

class NewsRateLimiter:
    """Rate limiter for news API calls"""
    
    def __init__(self, max_requests_per_minute: int = 30):
        self.max_requests_per_minute = max_requests_per_minute
        self.min_interval = 60.0 / max_requests_per_minute
        self.last_request_time = 0
        self.lock = threading.Lock()
    
    def wait_if_needed(self):
        """Wait if necessary to respect rate limits"""
        with self.lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            
            if time_since_last < self.min_interval:
                sleep_time = self.min_interval - time_since_last
                time.sleep(sleep_time)
            
            self.last_request_time = time.time()

class NewsDisplayManager:
    """Manager for displaying news with enhanced formatting"""
    
    @staticmethod
    def format_news_for_display(news_articles: List[Dict]) -> str:
        """Format news articles for display with clickable links"""
        if not news_articles:
            return "No news articles available at this time."
        
        formatted_news = "📰 COMPREHENSIVE NEWS FEED\n"
        formatted_news += "═" * 80 + "\n\n"
        
        for i, article in enumerate(news_articles, 1):
            # Priority indicator
            priority_indicator = "🔥" if article.get('priority', 999) == 1 else "📈" if article.get('priority', 999) <= 3 else "📊"
            
            # Sentiment indicator
            sentiment = article.get('sentiment', 'neutral')
            sentiment_indicator = "🟢" if sentiment == 'positive' else "🔴" if sentiment == 'negative' else "🟡"
            
            formatted_news += f"{i:2d}. {priority_indicator} {sentiment_indicator} {article.get('title', 'No Title')}\n"
            formatted_news += f"    📅 {article.get('published_at', datetime.now()).strftime('%Y-%m-%d %H:%M')}\n"
            formatted_news += f"    📰 {article.get('source', 'Unknown Source')}\n"
            
            if article.get('description'):
                # Truncate description to 150 characters
                desc = article['description'][:150] + "..." if len(article['description']) > 150 else article['description']
                formatted_news += f"    📝 {desc}\n"
            
            formatted_news += f"    🔗 {article.get('url', 'No URL')}\n"
            formatted_news += "    " + "─" * 76 + "\n\n"
        
        formatted_news += f"📊 Total Articles: {len(news_articles)}\n"
        formatted_news += f"🕒 Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        formatted_news += "═" * 80 + "\n"
        
        return formatted_news

# Global instances
enhanced_news_engine = EnhancedNewsEngine()
news_display_manager = NewsDisplayManager()

# Convenience functions
def enhanced_news(sector: str = "general", count: int = 50) -> List[Dict]:
    """Get enhanced news with Globe and Mail priority"""
    return enhanced_news_engine.get_comprehensive_news(sector, count)

def get_sector_news(sector: str, count: int = 30) -> List[Dict]:
    """Get sector-specific news"""
    return enhanced_news_engine.get_sector_news(sector, count)

def format_news_display(news_articles: List[Dict]) -> str:
    """Format news for display"""
    return news_display_manager.format_news_for_display(news_articles)

def get_globe_and_mail_priority_news(count: int = 30) -> List[Dict]:
    """Get Globe and Mail priority news specifically"""
    all_news = enhanced_news("general", count * 2)
    # Filter for Globe and Mail articles first
    globe_news = [article for article in all_news if article.get('source') == 'The Globe and Mail']
    other_news = [article for article in all_news if article.get('source') != 'The Globe and Mail']
    
    # Combine with Globe and Mail first
    return (globe_news + other_news)[:count]

logging.info("✅ Enhanced news integration module loaded successfully!")
