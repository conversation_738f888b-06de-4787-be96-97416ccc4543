# BEAUTIFUL WHITE THEME TiT LAUNCHER
# Version: 1.0.1 (WHITE THEME WITH GREEN DOTS)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON>
# Date: 2025-06-17
#
# Beautiful white theme launcher with green "READY" dots for all 7 separate apps

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os

class BeautifulWhiteLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("✨ TiT Suite 1.0.1 - ULTRA-PROFESSIONAL Financial Intelligence Launcher ✨")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#F8FAFC')  # Modern light background
        self.root.resizable(True, True)

        # Set minimum size
        self.root.minsize(1200, 800)

        # Center window
        self.center_window()

        # Configure modern styling
        self.setup_modern_styling()

        # Create beautiful UI
        self.create_beautiful_ui()

    def setup_modern_styling(self):
        """Setup modern styling and fonts"""
        # Configure ttk styles for modern look
        style = ttk.Style()
        style.theme_use('clam')

        # Configure modern scrollbar
        style.configure("Modern.Vertical.TScrollbar",
                       background='#E2E8F0',
                       troughcolor='#F1F5F9',
                       bordercolor='#CBD5E1',
                       arrowcolor='#64748B',
                       darkcolor='#94A3B8',
                       lightcolor='#F8FAFC')

        # Configure modern frame
        style.configure("Modern.TFrame",
                       background='#FFFFFF',
                       relief='flat',
                       borderwidth=1)
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_beautiful_ui(self):
        """Create the beautiful white theme UI"""
        # Main container with white background
        main_frame = tk.Frame(self.root, bg='#FFFFFF', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header section
        self.create_header(main_frame)
        
        # Applications grid
        self.create_apps_grid(main_frame)
        
        # Footer section
        self.create_footer(main_frame)
    
    def create_header(self, parent):
        """Create ultra-modern header with premium typography"""
        # Header container with gradient-like effect
        header_container = tk.Frame(parent, bg='#F8FAFC')
        header_container.pack(fill=tk.X, pady=(0, 40))

        # Main header frame with modern styling
        header_frame = tk.Frame(header_container, bg='#FFFFFF', relief=tk.FLAT, bd=0)
        header_frame.pack(fill=tk.X, padx=20, pady=20)

        # Add subtle shadow effect with frame
        shadow_frame = tk.Frame(header_container, bg='#E2E8F0', height=2)
        shadow_frame.pack(fill=tk.X, padx=25)

        # Main title with premium typography
        title_label = tk.Label(
            header_frame,
            text="🚀 TiT FINANCIAL INTELLIGENCE SUITE 🚀",
            font=("Segoe UI", 32, "bold"),  # Larger, more impactful
            fg='#1E293B',  # Darker, more professional
            bg='#FFFFFF'
        )
        title_label.pack(pady=(20, 15))

        # Subtitle with enhanced styling
        subtitle_label = tk.Label(
            header_frame,
            text="💎 ULTRA-PROFESSIONAL Market Intelligence & AI Analysis Platform 💎",
            font=("Segoe UI", 18, "normal"),  # Cleaner, more readable
            fg='#3B82F6',  # Modern blue
            bg='#FFFFFF'
        )
        subtitle_label.pack(pady=(0, 15))

        # Version with modern badge-like appearance
        version_frame = tk.Frame(header_frame, bg='#FFFFFF')
        version_frame.pack(pady=(0, 20))

        version_badge = tk.Label(
            version_frame,
            text="⭐ VERSION 1.0.1 - PROFESSIONAL EDITION ⭐",
            font=("Segoe UI", 14, "bold"),
            fg='#FFFFFF',
            bg='#10B981',  # Modern green
            padx=20,
            pady=8,
            relief=tk.FLAT
        )
        version_badge.pack()

        # Enhanced author section with modern card design
        author_container = tk.Frame(header_frame, bg='#FFFFFF')
        author_container.pack(pady=(15, 20))

        author_card = tk.Frame(
            author_container,
            bg='#F1F5F9',  # Light background
            relief=tk.FLAT,
            bd=0
        )
        author_card.pack(padx=40, pady=10)

        # Author header
        author_header = tk.Label(
            author_card,
            text="👨‍💻 CREATED BY",
            font=("Segoe UI", 12, "bold"),
            fg='#64748B',
            bg='#F1F5F9'
        )
        author_header.pack(pady=(15, 5))

        # Author name with premium styling
        author_label = tk.Label(
            author_card,
            text="🎯 NGUYEN LE VINH QUANG 🎯",
            font=("Segoe UI", 16, "bold"),
            fg='#DC2626',  # Modern red
            bg='#F1F5F9'
        )
        author_label.pack(padx=30, pady=(0, 15))
    
    def create_apps_grid(self, parent):
        """Create ultra-modern apps grid with enhanced scrolling"""
        # Apps container with modern styling
        apps_container = tk.Frame(parent, bg='#F8FAFC')
        apps_container.pack(fill=tk.BOTH, expand=True, pady=(0, 30))

        # Header for apps section
        apps_header = tk.Label(
            apps_container,
            text="🎯 AVAILABLE APPLICATIONS",
            font=("Segoe UI", 20, "bold"),
            fg='#1E293B',
            bg='#F8FAFC'
        )
        apps_header.pack(pady=(0, 20))

        # Main apps frame with modern card design
        apps_frame = tk.Frame(
            apps_container,
            bg='#FFFFFF',
            relief=tk.FLAT,
            bd=0
        )
        apps_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # Add subtle border
        border_frame = tk.Frame(apps_frame, bg='#E2E8F0', height=1)
        border_frame.pack(fill=tk.X)

        # Grid container with enhanced scrollable canvas
        canvas = tk.Canvas(
            apps_frame,
            bg='#FFFFFF',
            highlightthickness=0,
            relief=tk.FLAT,
            bd=0
        )

        # Modern scrollbar with custom styling
        scrollbar = ttk.Scrollbar(
            apps_frame,
            orient="vertical",
            command=canvas.yview,
            style="Modern.Vertical.TScrollbar"
        )

        scrollable_frame = tk.Frame(canvas, bg='#FFFFFF')

        # Enhanced scrolling configuration
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y", padx=(0, 10), pady=20)

        # ENHANCED MOUSE WHEEL SCROLLING SUPPORT
        def _on_mousewheel(event):
            """Handle mouse wheel scrolling"""
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            """Bind mouse wheel to canvas"""
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            """Unbind mouse wheel from canvas"""
            canvas.unbind_all("<MouseWheel>")

        # Bind mouse wheel events
        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)

        # Also bind to scrollable frame for better coverage
        scrollable_frame.bind('<Enter>', _bind_to_mousewheel)
        scrollable_frame.bind('<Leave>', _unbind_from_mousewheel)

        # Configure enhanced grid weights for 3 columns
        for i in range(3):
            scrollable_frame.columnconfigure(i, weight=1, minsize=450)
        for i in range(3):
            scrollable_frame.rowconfigure(i, weight=1, minsize=220)

        # Store canvas reference for scrolling
        self.canvas = canvas
        
        # Define ALL 7 apps with complete information
        self.applications = [
            {
                'name': 'TiT Crypto App',
                'description': 'Advanced Cryptocurrency Intelligence Suite',
                'file': 'Teu 1.0.1 MAin dancer.py',
                'icon': '₿',
                'color': '#F7931A',
                'features': ['Real-time crypto prices', 'DeFi analytics', 'News intelligence', 'AI predictions']
            },
            {
                'name': 'TiT Stock App',
                'description': 'Global Stock Market Intelligence Suite',
                'file': 'TiT_Stock_App_1.0.1.py',
                'icon': '📈',
                'color': '#2E8B57',
                'features': ['20+ countries coverage', 'Real-time indices', 'Earnings calendar', 'AI analysis']
            },
            {
                'name': 'TiT Oil App',
                'description': 'Oil Market Intelligence Suite',
                'file': 'TiT_Oil_App_1.0.1.py',
                'icon': '🛢️',
                'color': '#8B4513',
                'features': ['Oil futures tracking', 'OPEC analysis', 'Energy companies', 'Geopolitical impact']
            },
            {
                'name': 'TiT Gold App',
                'description': 'Precious Metals Intelligence Suite',
                'file': 'TiT_Gold_App_1.0.1.py',
                'icon': '🥇',
                'color': '#FFD700',
                'features': ['Precious metals prices', 'Mining companies', 'Central bank data', 'Safe haven analysis']
            },
            {
                'name': 'TiT Health App',
                'description': 'Health & Biotech Intelligence Suite',
                'file': 'TiT_Health_App_1.0.1.py',
                'icon': '🧬',
                'color': '#2E8B57',
                'features': ['Biotech companies', 'Drug pipeline', 'FDA approvals', 'Healthcare trends']
            },
            {
                'name': 'TiT Defense App',
                'description': 'Geopolitical & Defense Intelligence Suite',
                'file': 'TiT_Defense_App_1.0.1.py',
                'icon': '⚔️',
                'color': '#2F4F4F',
                'features': ['Defense contractors', 'Conflict monitoring', 'Arms trade', 'Geopolitical analysis']
            },
            {
                'name': 'TiT Science App',
                'description': 'Science, Technology & Space Intelligence Suite',
                'file': 'TiT_Science_App_1.0.1.py',
                'icon': '🚀',
                'color': '#4169E1',
                'features': ['Tech companies', 'Space exploration', 'AI developments', 'Innovation tracking']
            }
        ]
        
        # Create app cards in 3x3 grid
        row, col = 0, 0
        for app in self.applications:
            self.create_beautiful_app_card(scrollable_frame, app, row, col)

            col += 1
            if col >= 3:
                col = 0
                row += 1
    
    def create_beautiful_app_card(self, parent, app, row, col):
        """Create ultra-modern app card with premium design"""
        # Modern card container with shadow effect
        card_container = tk.Frame(parent, bg='#FFFFFF')
        card_container.grid(row=row, column=col, padx=20, pady=20, sticky="nsew")

        # Main card frame with modern styling
        card_frame = tk.Frame(
            card_container,
            bg='#FFFFFF',
            relief=tk.FLAT,
            bd=0
        )
        card_frame.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)

        # Add modern border effect
        border_frame = tk.Frame(card_frame, bg='#E2E8F0', height=1)
        border_frame.pack(fill=tk.X)

        # Card header with app name and icon
        header_frame = tk.Frame(card_frame, bg='#F8FAFC')
        header_frame.pack(fill=tk.X, padx=0, pady=0)

        # App title with enhanced typography
        title_label = tk.Label(
            header_frame,
            text=f"{app['icon']} {app['name']}",
            font=("Segoe UI", 16, "bold"),
            fg='#1E293B',
            bg='#F8FAFC'
        )
        title_label.pack(pady=(15, 10))

        # Status with ENHANCED GREEN DOT
        status_frame = tk.Frame(header_frame, bg='#F8FAFC')
        status_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        status_badge = tk.Label(
            status_frame,
            text="🟢 READY",
            font=("Segoe UI", 11, "bold"),
            fg='#FFFFFF',
            bg='#10B981',  # Modern green
            padx=12,
            pady=4,
            relief=tk.FLAT
        )
        status_badge.pack(side=tk.RIGHT)
        
        # Main content area
        content_frame = tk.Frame(card_frame, bg='#FFFFFF')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # Large icon with modern styling
        icon_frame = tk.Frame(content_frame, bg='#FFFFFF')
        icon_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        large_icon_label = tk.Label(
            icon_frame,
            text=app['icon'],
            font=("Segoe UI", 48),  # Larger, more impactful
            bg='#FFFFFF'
        )
        large_icon_label.pack()

        # Description with enhanced typography
        desc_label = tk.Label(
            content_frame,
            text=f"📋 {app['description']}",
            font=("Segoe UI", 12, "normal"),  # Larger, more readable
            wraplength=400,
            fg='#475569',  # Modern gray
            bg='#FFFFFF',
            justify=tk.CENTER
        )
        desc_label.pack(fill=tk.X, padx=15, pady=(0, 15))

        # Enhanced features section with modern card design
        features_container = tk.Frame(content_frame, bg='#FFFFFF')
        features_container.pack(fill=tk.X, padx=15, pady=(0, 15))

        features_header = tk.Label(
            features_container,
            text="✨ KEY FEATURES",
            font=("Segoe UI", 11, "bold"),
            fg='#374151',
            bg='#FFFFFF'
        )
        features_header.pack(anchor=tk.W, pady=(0, 8))

        features_frame = tk.Frame(
            features_container,
            bg='#F8FAFC',
            relief=tk.FLAT,
            bd=0
        )
        features_frame.pack(fill=tk.X, pady=(0, 5))

        for i, feature in enumerate(app['features']):
            feature_emojis = ['🎯', '⚡', '🔥', '💎']
            emoji = feature_emojis[i % len(feature_emojis)]

            feature_label = tk.Label(
                features_frame,
                text=f"{emoji} {feature}",
                font=("Segoe UI", 10, "normal"),
                fg='#1F2937',
                bg='#F8FAFC'
            )
            feature_label.pack(anchor=tk.W, padx=12, pady=3)
        
        # Enhanced launch button section
        button_container = tk.Frame(content_frame, bg='#FFFFFF')
        button_container.pack(fill=tk.X, padx=15, pady=(10, 20))

        # Modern launch button with gradient-like effect
        launch_btn = tk.Button(
            button_container,
            text="🚀 LAUNCH APPLICATION",
            font=("Segoe UI", 13, "bold"),
            bg='#3B82F6',  # Modern blue
            fg='white',
            relief=tk.FLAT,
            bd=0,
            height=2,
            cursor="hand2",
            activebackground='#2563EB',  # Darker blue on hover
            activeforeground='white',
            command=lambda a=app: self.launch_application(a)
        )
        launch_btn.pack(fill=tk.X, pady=(0, 8))

        # Enhanced file status with modern styling
        file_status_frame = tk.Frame(button_container, bg='#F1F5F9')
        file_status_frame.pack(fill=tk.X, pady=(0, 5))

        file_status_label = tk.Label(
            file_status_frame,
            text=f"📁 {app['file']}",
            font=("Segoe UI", 9, "normal"),
            fg='#64748B',
            bg='#F1F5F9'
        )
        file_status_label.pack(pady=6)

        # Add bottom border for card separation
        bottom_border = tk.Frame(card_frame, bg='#E2E8F0', height=1)
        bottom_border.pack(fill=tk.X, side=tk.BOTTOM)
    
    def create_footer(self, parent):
        """Create ultra-modern footer with premium controls"""
        # Footer container with modern styling
        footer_container = tk.Frame(parent, bg='#F8FAFC')
        footer_container.pack(fill=tk.X, pady=(20, 0))

        # Control center with enhanced design
        controls_container = tk.Frame(footer_container, bg='#FFFFFF')
        controls_container.pack(fill=tk.X, padx=20, pady=20)

        # Add top border
        top_border = tk.Frame(controls_container, bg='#E2E8F0', height=1)
        top_border.pack(fill=tk.X)

        # Controls header
        controls_header = tk.Label(
            controls_container,
            text="🎮 CONTROL CENTER",
            font=("Segoe UI", 16, "bold"),
            fg='#1E293B',
            bg='#FFFFFF'
        )
        controls_header.pack(pady=(20, 15))

        # Button container with modern layout
        button_container = tk.Frame(controls_container, bg='#FFFFFF')
        button_container.pack(fill=tk.X, padx=30, pady=(0, 20))

        # Enhanced Launch All button
        launch_all_btn = tk.Button(
            button_container,
            text="🚀 LAUNCH ALL APPLICATIONS",
            font=("Segoe UI", 14, "bold"),
            bg='#10B981',  # Modern green
            fg='white',
            relief=tk.FLAT,
            bd=0,
            height=2,
            cursor="hand2",
            activebackground='#059669',
            activeforeground='white',
            command=self.launch_all_applications
        )
        launch_all_btn.pack(side=tk.LEFT, padx=(0, 20), ipadx=20)

        # Enhanced Exit button
        exit_btn = tk.Button(
            button_container,
            text="❌ EXIT LAUNCHER",
            font=("Segoe UI", 14, "bold"),
            bg='#EF4444',  # Modern red
            fg='white',
            relief=tk.FLAT,
            bd=0,
            height=2,
            cursor="hand2",
            activebackground='#DC2626',
            activeforeground='white',
            command=self.root.quit
        )
        exit_btn.pack(side=tk.RIGHT, ipadx=20)
        
        # Enhanced status section
        status_container = tk.Frame(footer_container, bg='#F1F5F9')
        status_container.pack(fill=tk.X, padx=20, pady=(0, 20))

        # Status content frame
        status_content = tk.Frame(status_container, bg='#F1F5F9')
        status_content.pack(fill=tk.X, padx=20, pady=15)

        # System info header
        info_header = tk.Label(
            status_content,
            text="📋 SYSTEM INFORMATION",
            font=("Segoe UI", 12, "bold"),
            fg='#374151',
            bg='#F1F5F9'
        )
        info_header.pack(pady=(0, 10))

        # Info container
        info_container = tk.Frame(status_content, bg='#F1F5F9')
        info_container.pack(fill=tk.X)

        # Enhanced Copyright
        copyright_label = tk.Label(
            info_container,
            text="© 2025 NGUYEN LE VINH QUANG. All rights reserved. | ULTRA-PROFESSIONAL Financial Intelligence Suite",
            font=("Segoe UI", 10, "normal"),
            fg='#6B7280',
            bg='#F1F5F9'
        )
        copyright_label.pack(side=tk.LEFT, padx=5)

        # Enhanced Version
        version_label = tk.Label(
            info_container,
            text="🎯 TiT Suite v1.0.1 PROFESSIONAL EDITION",
            font=("Segoe UI", 10, "bold"),
            fg='#1F2937',
            bg='#F1F5F9'
        )
        version_label.pack(side=tk.RIGHT, padx=5)
    
    def launch_application(self, app):
        """Launch a specific application"""
        try:
            app_file = app['file']
            
            if not os.path.exists(app_file):
                messagebox.showerror(
                    "❌ File Not Found",
                    f"Application file '{app_file}' not found.\n\n"
                    f"📁 Please ensure all TiT apps are in the same directory."
                )
                return
            
            # Launch the app
            process = subprocess.Popen([sys.executable, app_file])
            
            messagebox.showinfo(
                "🚀 Launch Successful!",
                f"✅ {app['name']} is starting!\n\n"
                f"⚡ Application window will appear shortly\n"
                f"🎯 Process ID: {process.pid}"
            )
            
        except Exception as e:
            messagebox.showerror("❌ Launch Error", f"Failed to launch {app['name']}:\n\n{str(e)}")
    
    def launch_all_applications(self):
        """Launch all applications"""
        try:
            result = messagebox.askyesno(
                "Launch All Applications",
                "This will launch all 7 TiT applications.\n\nContinue?"
            )
            
            if not result:
                return
            
            launched = 0
            failed = []
            
            for app in self.applications:
                try:
                    if os.path.exists(app['file']):
                        subprocess.Popen([sys.executable, app['file']])
                        launched += 1
                    else:
                        failed.append(f"{app['name']} (file not found)")
                except Exception as e:
                    failed.append(f"{app['name']} (error: {str(e)})")
            
            message = f"✅ Successfully launched {launched} applications!"
            if failed:
                message += f"\n\n❌ Failed to launch:\n" + "\n".join(failed)
            
            messagebox.showinfo("Launch Results", message)
            
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to launch applications: {str(e)}")

def main():
    """Main function"""
    print("🚀 Starting ULTRA-PROFESSIONAL TiT Launcher...")
    print("💎 7 SEPARATE APPS with ENHANCED DESIGN & MOUSE SCROLLING")
    print("👨‍💻 Created by Nguyen Le Vinh Quang")

    root = tk.Tk()
    BeautifulWhiteLauncher(root)  # Fixed unused variable warning

    print("✅ Ultra-professional launcher ready!")
    root.mainloop()

if __name__ == "__main__":
    main()
