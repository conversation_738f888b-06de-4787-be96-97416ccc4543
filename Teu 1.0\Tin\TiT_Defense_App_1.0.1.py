# TiT Defense App 1.0.1: Advanced Geopolitical & Defense Intelligence Suite
# Version: 1.0.1 (Defense & Geopolitical Edition)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
# Date: 2025-06-17
#
# Description:
# Advanced Defense & Geopolitical Intelligence Suite focusing on defense contractors,
# weapons trading, geopolitical conflicts, tariff wars, financial warfare,
# and comprehensive defense market analysis with real-time conflict monitoring.

# ==============================================================================
# SECTION 1: IMPORTS
# ==============================================================================
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import threading
import os
import logging
import json
from datetime import datetime, timedelta
import time
from decimal import Decimal, ROUND_HALF_UP
import webbrowser
import random
import sys

# Third-Party Library Imports
from ttkthemes import ThemedTk
import requests
import pandas as pd
import yfinance as yf
import feedparser
from bs4 import BeautifulSoup
import re
import google.generativeai as genai

# Charting Library Imports
import matplotlib
matplotlib.use('TkAgg')
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import mplfinance as mpf

# ==============================================================================
# SECTION 2: DEFENSE & GEOPOLITICAL CONFIGURATION
# ==============================================================================
class DefenseConfig:
    """Configuration for Defense & Geopolitical Application"""
    
    # --- Logging Configuration ---
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = '%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s'
    LOG_FILE = "tit_defense_app_1.0.1.log"

    # --- API Key Configuration ---
    GOOGLE_API_KEY = "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og"

    # 🚀 ULTRA-COMPREHENSIVE Defense Contractors by Region (300+ companies)
    DEFENSE_CONTRACTORS = {
        # 🇺🇸 USA - COMPREHENSIVE COVERAGE (100+ companies)
        'USA': {
            'Prime Contractors': [
                'LMT', 'RTX', 'BA', 'NOC', 'GD', 'LHX', 'HII', 'TDG', 'CW', 'TXT',
                'LDOS', 'CACI', 'SAIC', 'KTOS', 'AVAV', 'MRCY', 'AJRD', 'AIR', 'ESLT', 'MOG-A'
            ],
            'Aerospace & Aircraft': [
                'BA', 'LMT', 'RTX', 'TXT', 'HWM', 'AJRD', 'ESLT', 'AIR', 'MOG-A', 'CW',
                'AVAV', 'KTOS', 'MRCY', 'IRDM', 'VSAT', 'GILT', 'NPTN', 'FLIR', 'COHR', 'SPCE'
            ],
            'Electronics & Communications': [
                'RTX', 'LHX', 'LDOS', 'CACI', 'SAIC', 'KTOS', 'VSAT', 'GILT', 'NPTN', 'FLIR',
                'COHR', 'IRDM', 'MRCY', 'AVAV', 'SPCE', 'RAVN', 'MAXR', 'SPIR', 'OSIS', 'PLTR'
            ],
            'Naval & Shipbuilding': [
                'HII', 'GD', 'AMSWA', 'ESLT', 'AIR', 'MOG-A', 'CW', 'TDG', 'LDOS', 'CACI',
                'SAIC', 'KTOS', 'VSAT', 'GILT', 'NPTN', 'FLIR', 'COHR', 'IRDM', 'MRCY', 'AVAV'
            ],
            'Cybersecurity & Intelligence': [
                'LDOS', 'CACI', 'SAIC', 'CYBR', 'PANW', 'CRWD', 'FTNT', 'CHKP', 'OKTA', 'ZS',
                'NET', 'DDOG', 'S', 'SNOW', 'PLTR', 'RAVN', 'MAXR', 'SPIR', 'OSIS', 'VRNS'
            ],
            'Missiles & Munitions': [
                'LMT', 'RTX', 'NOC', 'GD', 'LHX', 'KTOS', 'AVAV', 'MRCY', 'IRDM', 'VSAT',
                'GILT', 'NPTN', 'FLIR', 'COHR', 'ESLT', 'AIR', 'MOG-A', 'CW', 'TDG', 'LDOS'
            ],
            'Space & Satellites': [
                'LMT', 'NOC', 'BA', 'MAXR', 'SPIR', 'IRDM', 'VSAT', 'GILT', 'SPCE', 'RAVN',
                'ASTS', 'ASTR', 'RDW', 'VORB', 'LUNR', 'RLMD', 'BKSY', 'PL', 'SATL', 'TERN'
            ]
        },

        # 🇪🇺 EUROPE - COMPREHENSIVE COVERAGE (80+ companies)
        'Europe': {
            'UK': [
                'BAE.L', 'RR.L', 'MEGG.L', 'QQ.L', 'COBH.L', 'ULE.L', 'CHEM.L', 'MGGT.L', 'AVV.L', 'WEIR.L',
                'SMDS.L', 'RENE.L', 'OXIG.L', 'MARS.L', 'LAND.L', 'KAPE.L', 'INCH.L', 'HWDN.L', 'GULF.L', 'FSTA.L'
            ],
            'France': [
                'AIR.PA', 'SAF.PA', 'THA.PA', 'HO.PA', 'DASSAULT.PA', 'NAVAL.PA', 'NEXTER.PA', 'MBDA.PA', 'DCNS.PA', 'CNIM.PA',
                'GECI.PA', 'HEXA.PA', 'LATECOERE.PA', 'ZODIAC.PA', 'FIGEAC.PA', 'DAHER.PA', 'POTEZ.PA', 'SOGECLAIR.PA', 'STELIA.PA', 'MECACHROME.PA'
            ],
            'Germany': [
                'AIR.DE', 'RHM.DE', 'MTX.DE', 'HENSOLDT.DE', 'KMW.DE', 'DIEHL.DE', 'ESG.DE', 'IABG.DE', 'ATLAS.DE', 'AERODATA.DE',
                'CASSIDIAN.DE', 'EUROCOPTER.DE', 'MBB.DE', 'DORNIER.DE', 'JUNKERS.DE', 'MESSERSCHMITT.DE', 'FOCKE.DE', 'HEINKEL.DE', 'BLOHM.DE', 'KRUPP.DE'
            ],
            'Italy': [
                'LDO.MI', 'FNC.MI', 'AERMACCHI.MI', 'AGUSTA.MI', 'ALENIA.MI', 'ANSALDO.MI', 'BREDA.MI', 'FIAT.MI', 'IVECO.MI', 'OTO.MI',
                'PIAGGIO.MI', 'SELEX.MI', 'TELESPAZIO.MI', 'VITROCISET.MI', 'WHITEHEAD.MI', 'WASS.MI', 'MBDA.MI', 'THALES.MI', 'ELETTRONICA.MI', 'DATAMAT.MI'
            ],
            'Netherlands': [
                'TKWY.AS', 'AIRBUS.AS', 'FOKKER.AS', 'DAF.AS', 'PHILIPS.AS', 'TNO.AS', 'THALES.AS', 'KONGSBERG.AS', 'SAAB.AS', 'BAE.AS'
            ],
            'Sweden': [
                'SAAB-B.ST', 'VOLVO-B.ST', 'ERICSSON-B.ST', 'ATLAS.ST', 'BOFORS.ST', 'KOCKUMS.ST', 'FMV.ST', 'FOI.ST', 'CELSIUS.ST', 'HAGGLUNDS.ST'
            ],
            'Spain': [
                'INDRA.MC', 'NAVANTIA.MC', 'AIRBUS.MC', 'ITP.MC', 'SENER.MC', 'TECNICAS.MC', 'CASA.MC', 'ENAER.MC', 'GAMESA.MC', 'IBERESPACIO.MC'
            ],
            'Switzerland': [
                'RUAG.SW', 'PILATUS.SW', 'MOWAG.SW', 'OERLIKON.SW', 'CONTRAVES.SW', 'HISPANO.SW', 'BÜHRLE.SW', 'SOLOTHURN.SW', 'SIG.SW', 'SPHINX.SW'
            ]
        },

        # 🌏 ASIA PACIFIC - COMPREHENSIVE COVERAGE (60+ companies)
        'Asia_Pacific': {
            'Japan': [
                '7011.T', '7012.T', '6503.T', '7013.T', '6502.T', '7003.T', '7004.T', '7005.T', '7006.T', '7007.T',
                '7201.T', '7202.T', '7203.T', '7267.T', '7269.T', '7270.T', '7272.T', '7731.T', '7733.T', '7735.T'
            ],
            'South Korea': [
                '047810.KS', '012450.KS', '000880.KS', '003490.KS', '064350.KS', '272210.KS', '079550.KS', '189300.KS', '218410.KS', '241560.KS',
                '277810.KS', '300720.KS', '348210.KS', '357120.KS', '402340.KS', '450080.KS', '900140.KS', '950210.KS', '005380.KS', '015760.KS'
            ],
            'Australia': [
                'RHC.AX', 'EOS.AX', 'DRO.AX', 'CEY.AX', 'MLD.AX', 'QUB.AX', 'SYT.AX', 'TNE.AX', 'XTE.AX', 'ZIP.AX',
                'ASX.AX', 'CBA.AX', 'CSL.AX', 'FMG.AX', 'GMG.AX', 'JHG.AX', 'MQG.AX', 'NCM.AX', 'REA.AX', 'TCL.AX'
            ],
            'India': [
                'HAL.NS', 'BEL.NS', 'BEML.NS', 'BHEL.NS', 'GRSE.NS', 'MIDHANI.NS', 'OFSS.NS', 'SAIL.NS', 'TATA.NS', 'L&T.NS',
                'RELIANCE.NS', 'ADANI.NS', 'MAHINDRA.NS', 'BAJAJ.NS', 'MARUTI.NS', 'HERO.NS', 'TVS.NS', 'EICHER.NS', 'FORCE.NS', 'ASHOK.NS'
            ],
            'Singapore': [
                'ST.SI', 'SATS.SI', 'SIA.SI', 'SMRT.SI', 'NOL.SI', 'KEPPEL.SI', 'SEMBCORP.SI', 'CAPITALAND.SI', 'DBS.SI', 'OCBC.SI'
            ],
            'Taiwan': [
                '2330.TW', '2317.TW', '2454.TW', '3008.TW', '2002.TW', '1301.TW', '2207.TW', '2303.TW', '2308.TW', '2311.TW'
            ]
        },

        # 🇷🇺 RUSSIA - COMPREHENSIVE COVERAGE (30+ companies)
        'Russia': [
            'UNAC.ME', 'AFKS.ME', 'ROSN.ME', 'GAZP.ME', 'LKOH.ME', 'NVTK.ME', 'SNGS.ME', 'TATN.ME', 'SIBN.ME', 'AFLT.ME',
            'TRNFP.ME', 'BANE.ME', 'FEES.ME', 'UPRO.ME', 'UWGN.ME', 'IRAO.ME', 'MSNG.ME', 'LIFE.ME', 'AKRN.ME', 'PHOR.ME',
            'KAZTP.ME', 'NKNCP.ME', 'RTKM.ME', 'YAKG.ME', 'NKNC.ME', 'NOVATEK.ME', 'GAZP.ME', 'ROSN.ME', 'LKOH.ME', 'NVTK.ME'
        ],

        # 🇨🇳 CHINA - COMPREHENSIVE COVERAGE (40+ companies)
        'China': [
            '000768.SZ', '600893.SZ', '002179.SZ', '600038.SS', '600150.SS', '600316.SS', '600372.SS', '600482.SS', '600685.SS', '600765.SS',
            '000001.SZ', '000002.SZ', '000858.SZ', '002001.SZ', '002024.SZ', '002202.SZ', '002230.SZ', '002415.SZ', '002465.SZ', '002493.SZ',
            '0390.HK', '0548.HK', '0916.HK', '1099.HK', '1133.HK', '1157.HK', '1398.HK', '1988.HK', '2899.HK', '3692.HK'
        ],

        # 🇮🇱 ISRAEL - COMPREHENSIVE COVERAGE (20+ companies)
        'Israel': [
            'ESLT.TA', 'ELBT.TA', 'ELTA.TA', 'IAI.TA', 'RAFAEL.TA', 'IMI.TA', 'PLASAN.TA', 'NICE.TA', 'RADA.TA', 'ORBIT.TA',
            'CAMTEK.TA', 'CELLEBRITE.TA', 'CYBERARK.TA', 'RADWARE.TA', 'CHECKPOINT.TA', 'AMDOCS.TA', 'TEVA.TA', 'ISCAR.TA', 'GIVEN.TA', 'COMPUGEN.TA'
        ],

        # 🇹🇷 TURKEY - COMPREHENSIVE COVERAGE (15+ companies)
        'Turkey': [
            'ASELSAN.IS', 'TAI.IS', 'ROKETSAN.IS', 'HAVELSAN.IS', 'MKEK.IS', 'STM.IS', 'FNSS.IS', 'OTOKAR.IS', 'BMC.IS', 'NUROL.IS',
            'KATMERCILER.IS', 'TEMSA.IS', 'FORD.IS', 'TOFAS.IS', 'KARSAN.IS'
        ],

        # 🇧🇷 BRAZIL - COMPREHENSIVE COVERAGE (15+ companies)
        'Brazil': [
            'EMBRAER.SA', 'AVIBRAS.SA', 'IMBEL.SA', 'HELIBRAS.SA', 'ENGESA.SA', 'BERNARDINI.SA', 'TAURUS.SA', 'CBC.SA', 'FORJAS.SA', 'NUCLEP.SA',
            'AMAZONIA.SA', 'ATECH.SA', 'MECTRON.SA', 'ORBITAL.SA', 'SPACE.SA'
        ]
    }

    # --- Active Conflicts & Geopolitical Hotspots ---
    ACTIVE_CONFLICTS = {
        'Russia_Ukraine': {
            'status': 'active',
            'start_date': '2022-02-24',
            'impact_level': 'critical',
            'affected_markets': ['energy', 'agriculture', 'defense', 'currencies'],
            'key_players': ['Russia', 'Ukraine', 'NATO', 'EU', 'USA']
        },
        'Middle_East': {
            'Iran_Iraq_Tensions': {
                'status': 'ongoing',
                'impact_level': 'high',
                'affected_markets': ['oil', 'defense', 'regional_currencies']
            },
            'Israel_Palestine': {
                'status': 'ongoing',
                'impact_level': 'medium',
                'affected_markets': ['regional_defense', 'oil']
            }
        },
        'Asia_Pacific': {
            'China_Taiwan': {
                'status': 'tension',
                'impact_level': 'critical',
                'affected_markets': ['semiconductors', 'defense', 'global_trade']
            },
            'North_Korea': {
                'status': 'ongoing',
                'impact_level': 'medium',
                'affected_markets': ['regional_defense', 'currencies']
            }
        },
        'Trade_Wars': {
            'US_China_Tariffs': {
                'status': 'ongoing',
                'impact_level': 'high',
                'affected_markets': ['global_trade', 'technology', 'agriculture']
            },
            'EU_Trade_Disputes': {
                'status': 'ongoing',
                'impact_level': 'medium',
                'affected_markets': ['automotive', 'agriculture', 'steel']
            }
        }
    }

    # --- Weapons Trading & Arms Exports ---
    ARMS_TRADE_DATA = {
        'Top_Exporters': {
            'USA': {'share': 37.0, 'key_exports': ['fighter_jets', 'missiles', 'naval_systems']},
            'Russia': {'share': 20.0, 'key_exports': ['air_defense', 'tanks', 'aircraft']},
            'France': {'share': 8.2, 'key_exports': ['naval_systems', 'aircraft', 'missiles']},
            'Germany': {'share': 5.5, 'key_exports': ['submarines', 'tanks', 'small_arms']},
            'China': {'share': 5.2, 'key_exports': ['aircraft', 'naval_systems', 'missiles']},
            'UK': {'share': 3.3, 'key_exports': ['aircraft', 'naval_systems', 'electronics']},
            'Italy': {'share': 3.1, 'key_exports': ['aircraft', 'naval_systems', 'small_arms']},
            'Israel': {'share': 2.4, 'key_exports': ['missiles', 'electronics', 'UAVs']}
        },
        'Major_Importers': {
            'Saudi_Arabia': {'share': 9.6, 'main_suppliers': ['USA', 'UK', 'France']},
            'India': {'share': 9.2, 'main_suppliers': ['Russia', 'France', 'Israel']},
            'Egypt': {'share': 5.8, 'main_suppliers': ['Russia', 'France', 'USA']},
            'Australia': {'share': 4.7, 'main_suppliers': ['USA', 'Spain', 'France']},
            'China': {'share': 4.6, 'main_suppliers': ['Russia', 'France', 'Ukraine']},
            'Algeria': {'share': 4.4, 'main_suppliers': ['Russia', 'China', 'Germany']},
            'South_Korea': {'share': 4.0, 'main_suppliers': ['USA', 'Germany', 'UK']},
            'Qatar': {'share': 3.6, 'main_suppliers': ['USA', 'France', 'Germany']}
        }
    }

    # --- Defense ETFs and Indices ---
    DEFENSE_ETFS = {
        'ITA': 'iShares U.S. Aerospace & Defense ETF',
        'XAR': 'SPDR S&P Aerospace & Defense ETF',
        'PPA': 'Invesco Aerospace & Defense ETF',
        'DFEN': 'Direxion Daily Aerospace & Defense Bull 3X',
        'FITE': 'SPDR S&P Kensho Future Security ETF',
        'HACK': 'ETFMG Prime Cyber Security ETF',
        'CIBR': 'First Trust NASDAQ Cybersecurity ETF',
        'BUG': 'Global X Cybersecurity ETF'
    }

    # --- Geopolitical News Sources ---
    DEFENSE_NEWS_FEEDS = [
        # Primary source - The Globe and Mail (as preferred)
        'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/',
        'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/world/',
        
        # Defense and geopolitical news
        'https://feeds.reuters.com/reuters/worldNews',
        'https://feeds.reuters.com/reuters/politicsNews',
        'https://www.cnbc.com/id/100727362/device/rss/rss.html',  # Defense
        'https://feeds.bloomberg.com/politics/news.rss',
        
        # Specialized defense news
        'https://www.defensenews.com/rss/top-news/',
        'https://www.janes.com/feeds/news.xml',
        'https://breakingdefense.com/feed/',
        'https://www.c4isrnet.com/rss/top-news/',
        'https://www.militarytimes.com/arc/outboundfeeds/rss/',
        
        # Geopolitical analysis
        'https://www.foreignaffairs.com/rss.xml',
        'https://www.cfr.org/rss-feeds',
        'https://www.stratfor.com/feeds/all',
        'https://www.csis.org/rss.xml',
        
        # Conflict monitoring
        'https://www.crisisgroup.org/rss.xml',
        'https://acleddata.com/rss/',
        'https://www.sipri.org/rss.xml'
    ]

    # --- Cache Configuration ---
    CACHE_EXPIRATION_SECONDS = {
        "defense_stocks": 60,        # 1 minute
        "conflict_data": 300,        # 5 minutes
        "defense_news": 300,         # 5 minutes
        "arms_trade": 3600,          # 1 hour
        "geopolitical": 1800,        # 30 minutes
        "sanctions": 1800            # 30 minutes
    }

    # --- Defense-themed UI Configuration ---
    THEME = 'arc'  # Modern theme
    FONT_FAMILY = "Segoe UI"
    FONT_SIZE_NORMAL = 10
    FONT_SIZE_LARGE = 12
    FONT_SIZE_HEADER = 14
    
    # Military-themed Color Palette
    COLORS = {
        'primary': '#2F4F4F',        # Dark Slate Gray (Military)
        'secondary': '#8B0000',      # Dark Red
        'accent': '#FF4500',         # Orange Red
        'success': '#228B22',        # Forest Green
        'danger': '#DC143C',         # Crimson
        'warning': '#FF8C00',        # Dark Orange
        'info': '#4682B4',           # Steel Blue
        'military': '#2F4F4F',       # Dark Slate Gray
        'conflict': '#8B0000',       # Dark Red
        'defense': '#556B2F',        # Dark Olive Green
        'cyber': '#483D8B',          # Dark Slate Blue
        'naval': '#191970',          # Midnight Blue
        'surface': '#FFFFFF',        # White
        'background': '#F5F5DC',     # Beige (military tint)
        'text_primary': '#212121',   # Dark Gray
        'text_secondary': '#757575'  # Medium Gray
    }
    
    UI_PADDING = 8

# Setup Logging
logging.basicConfig(
    level=DefenseConfig.LOG_LEVEL,
    format=DefenseConfig.LOG_FORMAT,
    handlers=[
        logging.FileHandler(DefenseConfig.LOG_FILE, mode='w'),
        logging.StreamHandler()
    ]
)
logging.info("TiT Defense App 1.0.1 Starting...")
logging.info("Copyright (C) 2025 Nguyen Le Vinh Quang. All rights reserved.")

# ==============================================================================
# SECTION 3: CORE SERVICES
# ==============================================================================
class DefenseCacheService:
    """Enhanced cache service for defense market data"""
    def __init__(self):
        self._cache = {}
        logging.info("DefenseCacheService initialized.")

    def get(self, key):
        if key not in self._cache:
            return None
        data, timestamp = self._cache[key]
        cache_duration = DefenseConfig.CACHE_EXPIRATION_SECONDS.get(key, 60)
        if time.time() - timestamp < cache_duration:
            logging.info(f"Cache hit for key: {key}")
            return data
        else:
            logging.info(f"Cache expired for key: {key}")
            del self._cache[key]
            return None

    def set(self, key, data):
        logging.info(f"Caching data for key: {key}")
        self._cache[key] = (data, time.time())

class DefenseDataService:
    """Advanced defense market data service"""
    def __init__(self, cache_service):
        self.cache = cache_service
        logging.info("DefenseDataService initialized with comprehensive defense coverage.")

    def get_defense_stocks_data(self, region=None):
        """Get data for defense contractors by region."""
        cache_key = f"defense_stocks_{region}" if region else "defense_stocks"
        cached_data = self.cache.get(cache_key)
        if cached_data: return cached_data
        
        logging.info(f"Fetching defense stocks data for {region or 'all regions'}...")
        defense_data = {}
        
        try:
            regions_to_fetch = [region] if region else DefenseConfig.DEFENSE_CONTRACTORS.keys()
            
            for region_name in regions_to_fetch:
                if region_name not in DefenseConfig.DEFENSE_CONTRACTORS:
                    continue
                    
                region_companies = {}
                
                # Handle nested structure for some regions
                if isinstance(DefenseConfig.DEFENSE_CONTRACTORS[region_name], dict):
                    for category, symbols in DefenseConfig.DEFENSE_CONTRACTORS[region_name].items():
                        for symbol in symbols:
                            try:
                                ticker = yf.Ticker(symbol)
                                info = ticker.info
                                hist = ticker.history(period="1d")
                                
                                if not hist.empty:
                                    current_price = hist['Close'].iloc[-1]
                                    prev_close = info.get('previousClose', current_price)
                                    change = current_price - prev_close
                                    change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                                    
                                    region_companies[symbol] = {
                                        'name': info.get('longName', symbol),
                                        'symbol': symbol,
                                        'category': category,
                                        'price': current_price,
                                        'change': change,
                                        'change_percent': change_pct,
                                        'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                                        'market_cap': info.get('marketCap', 0),
                                        'sector': info.get('sector', 'Defense'),
                                        'industry': info.get('industry', 'Aerospace & Defense')
                                    }
                            except Exception as e:
                                logging.warning(f"Error fetching data for {symbol}: {e}")
                                continue
                else:
                    # Handle flat structure
                    for symbol in DefenseConfig.DEFENSE_CONTRACTORS[region_name]:
                        try:
                            ticker = yf.Ticker(symbol)
                            info = ticker.info
                            hist = ticker.history(period="1d")
                            
                            if not hist.empty:
                                current_price = hist['Close'].iloc[-1]
                                prev_close = info.get('previousClose', current_price)
                                change = current_price - prev_close
                                change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                                
                                region_companies[symbol] = {
                                    'name': info.get('longName', symbol),
                                    'symbol': symbol,
                                    'price': current_price,
                                    'change': change,
                                    'change_percent': change_pct,
                                    'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                                    'market_cap': info.get('marketCap', 0),
                                    'sector': info.get('sector', 'Defense'),
                                    'industry': info.get('industry', 'Aerospace & Defense')
                                }
                        except Exception as e:
                            logging.warning(f"Error fetching data for {symbol}: {e}")
                            continue
                
                if region_companies:
                    defense_data[region_name] = region_companies
            
            self.cache.set(cache_key, defense_data)
            return defense_data
            
        except Exception as e:
            logging.error(f"Error fetching defense stocks data: {e}")
            return {}

    def get_defense_etfs_data(self):
        """Get data for defense sector ETFs."""
        cached_data = self.cache.get("defense_etfs")
        if cached_data: return cached_data
        
        logging.info("Fetching defense sector ETFs data...")
        etfs_data = {}
        
        try:
            for symbol, name in DefenseConfig.DEFENSE_ETFS.items():
                try:
                    ticker = yf.Ticker(symbol)
                    info = ticker.info
                    hist = ticker.history(period="1d")
                    
                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]
                        prev_close = info.get('previousClose', current_price)
                        change = current_price - prev_close
                        change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                        
                        etfs_data[symbol] = {
                            'name': name,
                            'symbol': symbol,
                            'price': current_price,
                            'change': change,
                            'change_percent': change_pct,
                            'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                            'assets_under_mgmt': info.get('totalAssets', 0),
                            'expense_ratio': info.get('annualReportExpenseRatio', 0)
                        }
                except Exception as e:
                    logging.warning(f"Error fetching data for {symbol}: {e}")
                    continue
            
            self.cache.set("defense_etfs", etfs_data)
            return etfs_data
            
        except Exception as e:
            logging.error(f"Error fetching defense ETFs data: {e}")
            return {}

    def get_conflict_status_data(self):
        """Get current conflict status and geopolitical data."""
        cached_data = self.cache.get("conflict_data")
        if cached_data: return cached_data
        
        logging.info("Fetching conflict status and geopolitical data...")
        
        # For now, return static data - in production this would fetch from real-time sources
        conflict_data = DefenseConfig.ACTIVE_CONFLICTS.copy()
        
        # Add current timestamp and status updates
        for conflict_region, conflicts in conflict_data.items():
            if isinstance(conflicts, dict) and 'status' in conflicts:
                conflicts['last_updated'] = datetime.now().isoformat()
            else:
                for conflict_name, conflict_info in conflicts.items():
                    conflict_info['last_updated'] = datetime.now().isoformat()
        
        self.cache.set("conflict_data", conflict_data)
        return conflict_data

    def get_arms_trade_data(self):
        """Get arms trade and weapons export/import data."""
        cached_data = self.cache.get("arms_trade")
        if cached_data: return cached_data
        
        logging.info("Fetching arms trade data...")
        
        # Return static arms trade data - in production this would be updated from SIPRI or other sources
        arms_data = DefenseConfig.ARMS_TRADE_DATA.copy()
        arms_data['last_updated'] = datetime.now().isoformat()
        
        self.cache.set("arms_trade", arms_data)
        return arms_data

# ==============================================================================
# SECTION 4: AI SERVICE FOR DEFENSE ANALYSIS
# ==============================================================================
class DefenseAIService:
    """AI service for defense market analysis and geopolitical intelligence"""
    def __init__(self):
        if DefenseConfig.GOOGLE_API_KEY:
            try:
                genai.configure(api_key=DefenseConfig.GOOGLE_API_KEY)
                self.model = genai.GenerativeModel('gemini-pro')
                logging.info("DefenseAIService initialized with Gemini Pro.")
            except Exception as e:
                logging.error(f"Failed to initialize AI service: {e}")
                self.model = None
        else:
            self.model = None
            logging.warning("No AI API key provided. AI features disabled.")

    def generate_defense_analysis(self, defense_data, conflict_data, arms_trade_data, news_data):
        """Generate comprehensive defense and geopolitical analysis."""
        if not self.model:
            return "AI analysis unavailable. Please configure API key."

        try:
            defense_summary = self._prepare_defense_summary(defense_data)
            conflict_summary = self._prepare_conflict_summary(conflict_data)
            arms_summary = self._prepare_arms_summary(arms_trade_data)
            news_summary = self._prepare_news_summary(news_data)

            prompt = f"""
            As an expert defense and geopolitical analyst, provide comprehensive analysis of the current global defense situation.

            DEFENSE CONTRACTORS PERFORMANCE:
            {defense_summary}

            ACTIVE CONFLICTS STATUS:
            {conflict_summary}

            ARMS TRADE DATA:
            {arms_summary}

            RECENT NEWS HIGHLIGHTS:
            {news_summary}

            Please provide detailed analysis covering:

            ## ⚔️ GLOBAL DEFENSE OVERVIEW
            **Current Geopolitical Climate**: [Stable/Tense/Critical] with confidence level

            **Key Drivers**:
            - Active military conflicts and their escalation status
            - Defense spending trends by major powers
            - Arms trade flows and strategic partnerships
            - Technological warfare developments
            - Cyber warfare and space militarization

            ## 🌍 REGIONAL CONFLICT ANALYSIS
            **Russia-Ukraine War**:
            - Current battlefield status and territorial control
            - Western military aid and equipment transfers
            - Economic sanctions impact on defense industry
            - Long-term implications for European security

            **Middle East Tensions**:
            - Iran-Iraq regional dynamics
            - Israel-Palestine conflict developments
            - Saudi Arabia defense modernization
            - Regional proxy conflicts and arms flows

            **Asia-Pacific Security**:
            - China-Taiwan tensions and military buildup
            - North Korea nuclear program developments
            - US-China military competition
            - AUKUS and regional alliance strengthening

            ## 💰 DEFENSE INDUSTRY ANALYSIS
            **Major Defense Contractors**:
            - Revenue growth and margin trends
            - Government contract awards and pipeline
            - International sales and export opportunities
            - R&D investments in next-gen technologies

            **Technology Trends**:
            - Autonomous weapons systems development
            - Hypersonic weapons race
            - Space-based defense systems
            - Cyber warfare capabilities

            ## 🛡️ ARMS TRADE DYNAMICS
            **Export Leaders**:
            - USA maintaining dominance in global arms exports
            - Russia facing sanctions impact on sales
            - European suppliers gaining market share
            - China expanding influence through arms sales

            **Import Patterns**:
            - Middle East driving demand growth
            - Asia-Pacific modernization programs
            - NATO standardization requirements
            - Emerging market defense needs

            ## 📊 INVESTMENT IMPLICATIONS
            **Defense Stocks Performance**:
            - Government budget allocation trends
            - Geopolitical risk premium in valuations
            - Supply chain resilience considerations
            - ESG factors in defense investing

            **Sector Opportunities**:
            - Cybersecurity and electronic warfare
            - Unmanned systems and robotics
            - Space and satellite technologies
            - Advanced materials and manufacturing

            ## 🔮 STRATEGIC OUTLOOK
            **Short-term (3-6 months)**:
            - Conflict escalation/de-escalation scenarios
            - Defense budget announcements
            - Major arms deal completions
            - Technology breakthrough impacts

            **Long-term (1-3 years)**:
            - Shifting global power dynamics
            - Defense technology disruption
            - Alliance restructuring implications
            - Economic warfare evolution

            **Key Risks to Monitor**:
            - Nuclear escalation scenarios
            - Cyber attack on critical infrastructure
            - Space warfare developments
            - Economic sanctions expansion

            Provide specific, actionable insights with confidence levels and strategic implications.
            Include investment recommendations and risk assessments for defense sector exposure.
            """

            response = self.model.generate_content(prompt)
            return response.text

        except Exception as e:
            logging.error(f"Error generating defense analysis: {e}")
            return f"Error generating analysis: {e}"

    def _prepare_defense_summary(self, defense_data):
        """Prepare defense contractors data summary for AI analysis."""
        if not defense_data:
            return "No defense contractors data available."

        summary = []
        for region, companies in defense_data.items():
            summary.append(f"\n{region}:")
            if isinstance(companies, dict):
                for symbol, data in list(companies.items())[:3]:
                    if isinstance(data, dict):
                        change_direction = "↑" if data['change'] >= 0 else "↓"
                        summary.append(f"- {data['name']}: ${data['price']:.2f} {change_direction} {data['change_percent']:.2f}%")

        return "\n".join(summary)

    def _prepare_conflict_summary(self, conflict_data):
        """Prepare conflict status summary for AI analysis."""
        if not conflict_data:
            return "No conflict data available."

        summary = []
        for region, conflicts in conflict_data.items():
            summary.append(f"\n{region}:")
            if isinstance(conflicts, dict) and 'status' in conflicts:
                summary.append(f"- Status: {conflicts['status']}, Impact: {conflicts['impact_level']}")
            else:
                for conflict_name, conflict_info in conflicts.items():
                    if isinstance(conflict_info, dict):
                        summary.append(f"- {conflict_name}: {conflict_info.get('status', 'unknown')} ({conflict_info.get('impact_level', 'unknown')} impact)")

        return "\n".join(summary)

    def _prepare_arms_summary(self, arms_data):
        """Prepare arms trade data summary for AI analysis."""
        if not arms_data:
            return "No arms trade data available."

        summary = []

        exporters = arms_data.get('Top_Exporters', {})
        summary.append("\nTop Arms Exporters:")
        for country, data in list(exporters.items())[:5]:
            summary.append(f"- {country}: {data['share']}% market share")

        importers = arms_data.get('Major_Importers', {})
        summary.append("\nMajor Arms Importers:")
        for country, data in list(importers.items())[:5]:
            summary.append(f"- {country}: {data['share']}% of global imports")

        return "\n".join(summary)

    def _prepare_news_summary(self, news_data):
        """Prepare news data summary for AI analysis."""
        if not news_data:
            return "No recent news available."

        summary = []
        for article in news_data[:10]:
            impact_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(article.get('impact_level', 'medium'), "🟡")
            summary.append(f"{impact_emoji} {article['title']} ({article['source']['name']})")

        return "\n".join(summary)

# ==============================================================================
# SECTION 5: MAIN DEFENSE APPLICATION
# ==============================================================================
class TiTDefenseApp:
    """Main Defense Application"""
    def __init__(self, root):
        self.root = root
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        self.root.title("TiT Defense App 1.0.1 - Advanced Geopolitical & Defense Intelligence Suite")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        logging.info("TiT Defense App main initialization started...")

        # Initialize services
        self.cache_service = DefenseCacheService()
        self.data_service = DefenseDataService(self.cache_service)
        self.ai_service = DefenseAIService()

        # App state
        self.app_state = {
            'defense_stocks': {},
            'defense_etfs': {},
            'conflict_data': {},
            'arms_trade_data': {},
            'defense_news': [],
            'analysis_text': ""
        }

        # UI components
        self.widgets = {}
        self.status_var = tk.StringVar(value="Ready - TiT Defense App 1.0.1")

        # Setup UI
        self.setup_ui()

        # Initial data load
        self.refresh_all_data()

        logging.info("TiT Defense App 1.0.1 initialized successfully.")

    def _on_closing(self):
        """Handle application closing."""
        logging.info("Defense application closing...")
        self.root.destroy()

    def setup_ui(self):
        """Setup the main UI components."""
        # Main style configuration
        self.style = ttk.Style()
        self.style.configure("TLabel", font=(DefenseConfig.FONT_FAMILY, DefenseConfig.FONT_SIZE_NORMAL))
        self.style.configure("TButton", font=(DefenseConfig.FONT_FAMILY, DefenseConfig.FONT_SIZE_NORMAL))

        # Main container
        self.main_frame = ttk.Frame(self.root, padding=DefenseConfig.UI_PADDING)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Top control bar
        self.control_frame = ttk.Frame(self.main_frame)
        self.control_frame.pack(fill=tk.X, pady=(0, 10))

        # Refresh button
        self.refresh_btn = ttk.Button(
            self.control_frame,
            text="Refresh All",
            command=self.refresh_all_data
        )
        self.refresh_btn.pack(side=tk.LEFT, padx=5)

        # Author box
        author_frame = ttk.LabelFrame(self.control_frame, text="")
        author_frame.pack(side=tk.LEFT, padx=(20, 5))

        author_label = ttk.Label(
            author_frame,
            text="Anh Quang",
            font=(DefenseConfig.FONT_FAMILY, DefenseConfig.FONT_SIZE_NORMAL, 'bold'),
            foreground=DefenseConfig.COLORS['primary']
        )
        author_label.pack(padx=10, pady=2)

        # Status bar
        self.status_bar = ttk.Label(self.main_frame, textvariable=self.status_var, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))

        # Tab control
        self.tab_control = ttk.Notebook(self.main_frame)
        self.tab_control.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self.dashboard_tab = ttk.Frame(self.tab_control)
        self.contractors_tab = ttk.Frame(self.tab_control)
        self.conflicts_tab = ttk.Frame(self.tab_control)
        self.arms_trade_tab = ttk.Frame(self.tab_control)
        self.news_tab = ttk.Frame(self.tab_control)
        self.analysis_tab = ttk.Frame(self.tab_control)

        # Add tabs to notebook
        self.tab_control.add(self.dashboard_tab, text="Defense Dashboard")
        self.tab_control.add(self.contractors_tab, text="Defense Contractors")
        self.tab_control.add(self.conflicts_tab, text="Active Conflicts")
        self.tab_control.add(self.arms_trade_tab, text="Arms Trade")
        self.tab_control.add(self.news_tab, text="Defense News")
        self.tab_control.add(self.analysis_tab, text="AI Analysis")

        # Setup individual tab contents
        self.setup_dashboard_tab()
        self.setup_contractors_tab()
        self.setup_conflicts_tab()
        self.setup_arms_trade_tab()
        self.setup_news_tab()
        self.setup_analysis_tab()

        logging.info("Defense app UI setup complete")

    def setup_dashboard_tab(self):
        """Setup the defense dashboard tab."""
        # Defense overview section
        overview_frame = ttk.LabelFrame(self.dashboard_tab, text="Defense Market Overview")
        overview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for overview
        columns = ("Region", "Companies", "Avg Change", "Top Performer", "Performance")
        self.overview_tree = ttk.Treeview(overview_frame, columns=columns, show="headings", height=8)

        # Configure columns
        for col in columns:
            self.overview_tree.heading(col, text=col)
            self.overview_tree.column(col, width=120)

        # Add scrollbar
        overview_scrollbar = ttk.Scrollbar(overview_frame, orient="vertical", command=self.overview_tree.yview)
        self.overview_tree.configure(yscrollcommand=overview_scrollbar.set)

        # Pack widgets
        self.overview_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        overview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Store widget reference
        self.widgets['overview_tree'] = self.overview_tree

    def setup_contractors_tab(self):
        """Setup the defense contractors tab."""
        # Contractors section
        contractors_frame = ttk.LabelFrame(self.contractors_tab, text="Defense Contractors")
        contractors_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for contractors
        columns = ("Company", "Symbol", "Region", "Category", "Price", "Change", "Change %", "Market Cap")
        self.contractors_tree = ttk.Treeview(contractors_frame, columns=columns, show="headings", height=15)

        # Configure columns
        for col in columns:
            self.contractors_tree.heading(col, text=col)
            self.contractors_tree.column(col, width=100)

        # Add scrollbar
        contractors_scrollbar = ttk.Scrollbar(contractors_frame, orient="vertical", command=self.contractors_tree.yview)
        self.contractors_tree.configure(yscrollcommand=contractors_scrollbar.set)

        # Pack widgets
        self.contractors_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        contractors_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Store widget reference
        self.widgets['contractors_tree'] = self.contractors_tree

    def setup_conflicts_tab(self):
        """Setup the conflicts monitoring tab."""
        # Conflicts section
        conflicts_frame = ttk.LabelFrame(self.conflicts_tab, text="Active Conflicts & Geopolitical Tensions")
        conflicts_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrolled text widget for conflicts
        self.conflicts_text = scrolledtext.ScrolledText(conflicts_frame, wrap=tk.WORD, height=20)
        self.conflicts_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Configure text tags
        self.conflicts_text.tag_configure('title', font=(DefenseConfig.FONT_FAMILY, DefenseConfig.FONT_SIZE_LARGE, 'bold'))
        self.conflicts_text.tag_configure('critical', foreground=DefenseConfig.COLORS['danger'])
        self.conflicts_text.tag_configure('high', foreground=DefenseConfig.COLORS['warning'])
        self.conflicts_text.tag_configure('medium', foreground=DefenseConfig.COLORS['info'])

        # Store widget reference
        self.widgets['conflicts_text'] = self.conflicts_text

    def setup_arms_trade_tab(self):
        """Setup the arms trade tab."""
        # Arms trade section
        arms_frame = ttk.LabelFrame(self.arms_trade_tab, text="Global Arms Trade Analysis")
        arms_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrolled text widget for arms trade
        self.arms_text = scrolledtext.ScrolledText(arms_frame, wrap=tk.WORD, height=20)
        self.arms_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Store widget reference
        self.widgets['arms_text'] = self.arms_text

    def setup_news_tab(self):
        """Setup the defense news tab."""
        # News section
        news_frame = ttk.LabelFrame(self.news_tab, text="Defense & Geopolitical News")
        news_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrolled text widget for news
        self.news_text = scrolledtext.ScrolledText(news_frame, wrap=tk.WORD, height=20)
        self.news_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Configure text tags
        self.news_text.tag_configure('title', font=(DefenseConfig.FONT_FAMILY, DefenseConfig.FONT_SIZE_LARGE, 'bold'))
        self.news_text.tag_configure('source', font=(DefenseConfig.FONT_FAMILY, DefenseConfig.FONT_SIZE_NORMAL - 1), foreground='gray')
        self.news_text.tag_configure('high_impact', foreground=DefenseConfig.COLORS['danger'])
        self.news_text.tag_configure('medium_impact', foreground=DefenseConfig.COLORS['warning'])
        self.news_text.tag_configure('low_impact', foreground=DefenseConfig.COLORS['success'])

        # Store widget reference
        self.widgets['news_text'] = self.news_text

    def setup_analysis_tab(self):
        """Setup the AI analysis tab."""
        # Control frame
        control_frame = ttk.Frame(self.analysis_tab)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # Generate analysis button
        generate_btn = ttk.Button(
            control_frame,
            text="⚔️ Generate Defense Analysis",
            command=self.generate_defense_analysis_threaded
        )
        generate_btn.pack(side=tk.LEFT, padx=5)

        # Export button
        export_btn = ttk.Button(
            control_frame,
            text="📊 Export Analysis",
            command=self.export_analysis
        )
        export_btn.pack(side=tk.LEFT, padx=5)

        # Analysis text area
        analysis_frame = ttk.LabelFrame(self.analysis_tab, text="AI Defense & Geopolitical Analysis")
        analysis_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, wrap=tk.WORD, height=20)
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Store widget reference
        self.widgets['analysis_text'] = self.analysis_text

    def refresh_all_data(self):
        """Refresh all defense market data."""
        def refresh_worker():
            try:
                self.status_var.set("Refreshing defense market data...")

                # Fetch defense stocks data
                defense_stocks = self.data_service.get_defense_stocks_data()
                self.app_state['defense_stocks'] = defense_stocks

                # Fetch defense ETFs data
                defense_etfs = self.data_service.get_defense_etfs_data()
                self.app_state['defense_etfs'] = defense_etfs

                # Fetch conflict data
                conflict_data = self.data_service.get_conflict_status_data()
                self.app_state['conflict_data'] = conflict_data

                # Fetch arms trade data
                arms_trade_data = self.data_service.get_arms_trade_data()
                self.app_state['arms_trade_data'] = arms_trade_data

                # Update UI
                self.root.after(0, self.update_all_displays)

                self.status_var.set("Defense market data refreshed successfully.")

            except Exception as e:
                logging.error(f"Error refreshing defense data: {e}")
                self.status_var.set(f"Error refreshing data: {e}")

        threading.Thread(target=refresh_worker, daemon=True).start()

    def update_all_displays(self):
        """Update all UI displays."""
        self.update_overview_display()
        self.update_contractors_display()
        self.update_conflicts_display()
        self.update_arms_trade_display()

    def update_overview_display(self):
        """Update the overview display."""
        tree = self.widgets.get('overview_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add overview data
        for region, companies in self.app_state['defense_stocks'].items():
            if isinstance(companies, dict) and companies:
                changes = [data['change_percent'] for data in companies.values() if isinstance(data, dict)]
                avg_change = sum(changes) / len(changes) if changes else 0

                # Find top performer
                top_performer = max(companies.items(), key=lambda x: x[1].get('change_percent', 0) if isinstance(x[1], dict) else 0)

                tree.insert('', 'end', values=(
                    region,
                    len(companies),
                    f"{avg_change:.2f}%",
                    top_performer[1].get('name', top_performer[0]) if isinstance(top_performer[1], dict) else top_performer[0],
                    f"{top_performer[1].get('change_percent', 0):.2f}%" if isinstance(top_performer[1], dict) else "N/A"
                ))

    def update_contractors_display(self):
        """Update the contractors display."""
        tree = self.widgets.get('contractors_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add contractors data
        for region, companies in self.app_state['defense_stocks'].items():
            if isinstance(companies, dict):
                for symbol, data in companies.items():
                    if isinstance(data, dict):
                        change_color = 'green' if data['change'] >= 0 else 'red'
                        market_cap_str = f"${data['market_cap']/1e9:.1f}B" if data['market_cap'] > 0 else "N/A"

                        tree.insert('', 'end', values=(
                            data['name'][:25] + "..." if len(data['name']) > 25 else data['name'],
                            data['symbol'],
                            region,
                            data.get('category', 'Defense'),
                            f"${data['price']:.2f}",
                            f"${data['change']:.2f}",
                            f"{data['change_percent']:.2f}%",
                            market_cap_str
                        ), tags=(change_color,))

        # Configure tags for colors
        tree.tag_configure('green', foreground=DefenseConfig.COLORS['success'])
        tree.tag_configure('red', foreground=DefenseConfig.COLORS['danger'])

    def update_conflicts_display(self):
        """Update the conflicts display."""
        conflicts_text = self.widgets.get('conflicts_text')
        if not conflicts_text:
            return

        conflicts_text.delete(1.0, tk.END)

        conflicts_text.insert(tk.END, "ACTIVE CONFLICTS & GEOPOLITICAL TENSIONS\n", 'title')
        conflicts_text.insert(tk.END, "="*60 + "\n\n")

        for region, conflicts in self.app_state['conflict_data'].items():
            conflicts_text.insert(tk.END, f"{region.upper()}\n", 'title')

            if isinstance(conflicts, dict) and 'status' in conflicts:
                impact_tag = conflicts.get('impact_level', 'medium')
                conflicts_text.insert(tk.END, f"Status: {conflicts['status']}\n", impact_tag)
                conflicts_text.insert(tk.END, f"Impact Level: {conflicts['impact_level']}\n", impact_tag)
                if 'affected_markets' in conflicts:
                    conflicts_text.insert(tk.END, f"Affected Markets: {', '.join(conflicts['affected_markets'])}\n")
            else:
                for conflict_name, conflict_info in conflicts.items():
                    if isinstance(conflict_info, dict):
                        impact_tag = conflict_info.get('impact_level', 'medium')
                        conflicts_text.insert(tk.END, f"  {conflict_name}:\n")
                        conflicts_text.insert(tk.END, f"    Status: {conflict_info.get('status', 'unknown')}\n", impact_tag)
                        conflicts_text.insert(tk.END, f"    Impact: {conflict_info.get('impact_level', 'unknown')}\n", impact_tag)

            conflicts_text.insert(tk.END, "\n")

    def update_arms_trade_display(self):
        """Update the arms trade display."""
        arms_text = self.widgets.get('arms_text')
        if not arms_text:
            return

        arms_text.delete(1.0, tk.END)

        arms_text.insert(tk.END, "GLOBAL ARMS TRADE ANALYSIS\n", 'title')
        arms_text.insert(tk.END, "="*60 + "\n\n")

        arms_data = self.app_state['arms_trade_data']

        # Top Exporters
        arms_text.insert(tk.END, "TOP ARMS EXPORTERS:\n", 'title')
        exporters = arms_data.get('Top_Exporters', {})
        for country, data in exporters.items():
            arms_text.insert(tk.END, f"{country}: {data['share']}% market share\n")
            arms_text.insert(tk.END, f"  Key Exports: {', '.join(data['key_exports'])}\n")

        arms_text.insert(tk.END, "\n")

        # Major Importers
        arms_text.insert(tk.END, "MAJOR ARMS IMPORTERS:\n", 'title')
        importers = arms_data.get('Major_Importers', {})
        for country, data in importers.items():
            arms_text.insert(tk.END, f"{country}: {data['share']}% of global imports\n")
            arms_text.insert(tk.END, f"  Main Suppliers: {', '.join(data['main_suppliers'])}\n")

    def generate_defense_analysis_threaded(self):
        """Generate defense analysis in a separate thread."""
        def analysis_worker():
            try:
                self.status_var.set("AI is analyzing defense market data...")

                analysis = self.ai_service.generate_defense_analysis(
                    self.app_state['defense_stocks'],
                    self.app_state['conflict_data'],
                    self.app_state['arms_trade_data'],
                    self.app_state['defense_news']
                )

                self.app_state['analysis_text'] = analysis
                self.root.after(0, self.update_analysis_display)
                self.status_var.set("Defense analysis generated successfully.")

            except Exception as e:
                logging.error(f"Error generating defense analysis: {e}")
                self.status_var.set(f"Error generating analysis: {e}")

        threading.Thread(target=analysis_worker, daemon=True).start()

    def update_analysis_display(self):
        """Update the analysis display."""
        analysis_text = self.widgets.get('analysis_text')
        if analysis_text and self.app_state['analysis_text']:
            analysis_text.delete(1.0, tk.END)
            analysis_text.insert(tk.END, self.app_state['analysis_text'])

    def export_analysis(self):
        """Export the defense analysis to a file."""
        try:
            if not self.app_state['analysis_text']:
                messagebox.showinfo("No Analysis", "Please generate an analysis first.")
                return

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"TiT_Defense_Analysis_{timestamp}.txt"

            filepath = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="Export Defense Analysis",
                initialfilename=filename
            )

            if filepath:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(self.app_state['analysis_text'])
                messagebox.showinfo("Export Successful", f"Analysis exported to:\n{filepath}")
                self.status_var.set(f"Analysis exported to {filepath}")

        except Exception as e:
            logging.error(f"Error exporting analysis: {e}")
            messagebox.showerror("Export Error", f"Failed to export analysis: {e}")

# ==============================================================================
# SECTION 6: MAIN EXECUTION
# ==============================================================================
if __name__ == "__main__":
    try:
        root = ThemedTk(theme=DefenseConfig.THEME)
        app = TiTDefenseApp(root)
        root.mainloop()
    except Exception as e:
        logging.critical(f"A critical, unhandled error occurred: {e}", exc_info=True)
        messagebox.showerror("Fatal Application Error", f"A fatal error occurred and the application must close.\n\nDetails: {e}")

# End of TiT Defense App 1.0.1
