# TiT Gold App 1.0.1: Advanced Gold Market Intelligence Suite
# Version: 1.0.1 (Gold Market Edition)
#
# Copyright (C) 2025 Nguy<PERSON> Le <PERSON>uang. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
# Date: 2025-06-17
#
# This software is the proprietary work of <PERSON><PERSON><PERSON>.
# Unauthorized copying, distribution, or modification of this software,
# via any medium, is strictly prohibited without explicit written permission.
#
# Description:
# Advanced Gold Market Intelligence Suite focusing on precious metals,
# gold futures, mining companies, and comprehensive precious metals analysis.
# Features real-time gold prices, mining sector analysis, and AI-powered insights.

# ==============================================================================
# SECTION 1: IMPORTS
# ==============================================================================
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import threading
import os
import logging
import json
from datetime import datetime, timedelta
import time
from decimal import Decimal, ROUND_HALF_UP
import webbrowser
import random
import sys

# Third-Party Library Imports
from ttkthemes import ThemedTk
import requests
import pandas as pd
import yfinance as yf
import feedparser
from bs4 import BeautifulSoup
import re
import google.generativeai as genai

# Charting Library Imports
import matplotlib
matplotlib.use('TkAgg')
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import mplfinance as mpf

# ==============================================================================
# SECTION 2: GOLD MARKET CONFIGURATION
# ==============================================================================
class GoldConfig:
    """Configuration for Gold Market Application"""
    
    # --- Logging Configuration ---
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = '%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s'
    LOG_FILE = "tit_gold_app_1.0.1.log"

    # --- API Key Configuration ---
    GOOGLE_API_KEY = "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og"

    # 🚀 ULTRA-COMPREHENSIVE Precious Metals & Gold Market Symbols (100+ instruments)
    PRECIOUS_METALS = {
        # 🥇 GOLD - COMPREHENSIVE COVERAGE
        'GC=F': 'Gold Futures (COMEX)',
        'XAUUSD=X': 'Gold Spot Price USD',
        'XAUEUR=X': 'Gold Spot Price EUR',
        'XAUGBP=X': 'Gold Spot Price GBP',
        'XAUJPY=X': 'Gold Spot Price JPY',
        'XAUCAD=X': 'Gold Spot Price CAD',
        'XAUAUD=X': 'Gold Spot Price AUD',
        'XAUCHF=X': 'Gold Spot Price CHF',
        'GLD': 'SPDR Gold Trust ETF',
        'IAU': 'iShares Gold Trust',
        'SGOL': 'Aberdeen Standard Physical Gold',
        'OUNZ': 'VanEck Merk Gold Trust',
        'GLDM': 'SPDR Gold MiniShares Trust',
        'BAR': 'GraniteShares Gold Trust',
        'IAUM': 'iShares Gold Trust Micro',
        'AAAU': 'Goldman Sachs Physical Gold ETF',
        'PHYS': 'Sprott Physical Gold Trust',
        'FGDL': 'Fidelity Gold ETF',
        'GVAL': 'Granite Gold Trust',
        'GLDI': 'Credit Suisse X-Links Gold Shares',

        # 🥈 SILVER - COMPREHENSIVE COVERAGE
        'SI=F': 'Silver Futures (COMEX)',
        'XAGUSD=X': 'Silver Spot Price USD',
        'XAGEUR=X': 'Silver Spot Price EUR',
        'XAGGBP=X': 'Silver Spot Price GBP',
        'XAGJPY=X': 'Silver Spot Price JPY',
        'SLV': 'iShares Silver Trust',
        'PSLV': 'Sprott Physical Silver Trust',
        'SIVR': 'Aberdeen Standard Physical Silver',
        'SLVP': 'iShares MSCI Global Silver Miners ETF',
        'SIL': 'Global X Silver Miners ETF',
        'SILJ': 'ETFMG Prime Junior Silver ETF',
        'SLVO': 'Credit Suisse X-Links Silver Shares',
        'AGQ': '2x Long Silver ETF',
        'ZSL': '2x Short Silver ETF',
        'USLV': '3x Long Silver ETN',
        'DSLV': '3x Short Silver ETN',

        # 🔘 PLATINUM - COMPREHENSIVE COVERAGE
        'PL=F': 'Platinum Futures (NYMEX)',
        'XPTUSD=X': 'Platinum Spot Price USD',
        'XPTEUR=X': 'Platinum Spot Price EUR',
        'XPTGBP=X': 'Platinum Spot Price GBP',
        'PPLT': 'Aberdeen Standard Physical Platinum',
        'PLTM': 'GraniteShares Platinum Trust',
        'PTM': 'VanEck Platinum ETF',
        'PLAT': 'Wisdomtree Platinum',

        # ⚪ PALLADIUM - COMPREHENSIVE COVERAGE
        'PA=F': 'Palladium Futures (NYMEX)',
        'XPDUSD=X': 'Palladium Spot Price USD',
        'XPDEUR=X': 'Palladium Spot Price EUR',
        'PALL': 'Aberdeen Standard Physical Palladium',
        'PLDM': 'GraniteShares Palladium Trust',
        'PDM': 'VanEck Palladium ETF',

        # 🔶 COPPER - COMPREHENSIVE COVERAGE
        'HG=F': 'Copper Futures (COMEX)',
        'XCUUSD=X': 'Copper Spot Price USD',
        'CPER': 'United States Copper Index Fund',
        'COPX': 'Global X Copper Miners ETF',
        'COPP': 'Global X Copper Producers ETF',
        'JJC': 'iPath Bloomberg Copper Subindex',
        'CU': 'First Trust ISE Global Copper Index',

        # 🔹 RARE EARTH & STRATEGIC METALS
        'REMX': 'VanEck Rare Earth/Strategic Metals ETF',
        'REES': 'VanEck Rare Earth and Strategic Metals ETF',
        'LIT': 'Global X Lithium & Battery Tech ETF',
        'BATT': 'Amplify Lithium & Battery Technology ETF',
        'PICK': 'iShares MSCI Global Metals & Mining ETF',
        'RING': 'iShares MSCI Global Gold Miners ETF',
        'GUNR': 'FlexShares Global Upstream Natural Resources',
        'GLTR': 'Aberdeen Standard Physical Precious Metals',

        # 🏭 INDUSTRIAL METALS
        'ALI=F': 'Aluminum Futures',
        'ZN=F': 'Zinc Futures',
        'LE=F': 'Lead Futures',
        'NI=F': 'Nickel Futures',
        'TIN=F': 'Tin Futures',
        'STEEL=F': 'Steel Futures',
        'IRON=F': 'Iron Ore Futures',

        # 📊 PRECIOUS METALS INDICES & VOLATILITY
        'GVZ': 'Gold Volatility Index',
        'VXGD': 'Gold VIX',
        'XAU': 'Philadelphia Gold and Silver Index',
        'HUI': 'NYSE Arca Gold BUGS Index',
        'GDM': 'NYSE Arca Gold Miners Index',
        'SIX': 'NYSE Arca Silver Index',

        # 🌍 REGIONAL GOLD MARKETS
        'GOLD.L': 'Gold Price London',
        'GOLD.TO': 'Gold Price Toronto',
        'GOLD.AX': 'Gold Price Australia',
        'GOLD.HK': 'Gold Price Hong Kong',
        'GOLD.T': 'Gold Price Tokyo',
        'GOLD.F': 'Gold Price Frankfurt',
        'GOLD.PA': 'Gold Price Paris',
        'GOLD.MI': 'Gold Price Milan',
        'GOLD.MC': 'Gold Price Madrid',
        'GOLD.AS': 'Gold Price Amsterdam',
        'GOLD.SW': 'Gold Price Switzerland',
        'GOLD.ST': 'Gold Price Stockholm',
        'GOLD.CO': 'Gold Price Copenhagen',
        'GOLD.OL': 'Gold Price Oslo',
        'GOLD.HE': 'Gold Price Helsinki',

        # 💎 ALTERNATIVE PRECIOUS ASSETS
        'PDBC': 'Invesco Optimum Yield Diversified Commodity',
        'DJP': 'iPath Bloomberg Commodity Index',
        'GSG': 'iShares S&P GSCI Commodity-Indexed Trust',
        'DBA': 'Invesco DB Agriculture Fund',
        'DBB': 'Invesco DB Base Metals Fund',
        'DBP': 'Invesco DB Precious Metals Fund',
        'TAGS': 'Teucrium Agricultural Fund',
        'CORN': 'Teucrium Corn Fund',
        'WEAT': 'Teucrium Wheat Fund',
        'SOYB': 'Teucrium Soybean Fund',
        'CANE': 'Teucrium Sugar Fund',
        'CAFE': 'iPath Bloomberg Coffee Subindex',
        'CHOC': 'iPath Bloomberg Cocoa Subindex',
        'CTTN': 'iPath Bloomberg Cotton Subindex'
    }

    # 🚀 ULTRA-COMPREHENSIVE Gold Mining Companies by Region (200+ companies)
    GOLD_MINERS = {
        # 🇺🇸🇨🇦 NORTH AMERICA - COMPREHENSIVE COVERAGE (80+ companies)
        'North America': {
            'USA': [
                # Major US Gold Miners
                'GOLD', 'NEM', 'FCX', 'AUY', 'KGC', 'PAAS', 'HL', 'CDE', 'MARA', 'RIOT',
                # Mid-Cap US Miners
                'AG', 'EXK', 'USAS', 'GORO', 'SSRM', 'SAND', 'RGLD', 'WPM', 'FNV', 'SBSW',
                # Small-Cap US Miners
                'HYMC', 'GLDG', 'SLVR', 'SILV', 'SLVO', 'SLVD', 'SLVP', 'SLVR', 'GORO', 'USAS',
                # Streaming & Royalty Companies
                'RGLD', 'WPM', 'FNV', 'SAND', 'VGLD', 'METC', 'STVN', 'ORLA', 'GORO', 'USAS'
            ],
            'Canada': [
                # Major Canadian Gold Miners
                'ABX.TO', 'K.TO', 'AEM.TO', 'WPM.TO', 'FNV.TO', 'SSRM.TO', 'EQX.TO', 'YRI.TO', 'IMG.TO', 'TGZ.TO',
                # Mid-Cap Canadian Miners
                'KL.TO', 'CG.TO', 'EDV.TO', 'PVG.TO', 'AR.TO', 'PAAS.TO', 'HL.TO', 'CDE.TO', 'AG.TO', 'EXK.TO',
                # Small-Cap Canadian Miners
                'GLDX.TO', 'GPR.TO', 'GCM.TO', 'MINING.TO', 'GOLD.TO', 'SILV.TO', 'SLVR.TO', 'GORO.TO', 'USAS.TO', 'HYMC.TO',
                # Junior Miners & Explorers
                'NOVO.TO', 'FURY.TO', 'SGNL.TO', 'ARTG.TO', 'GDXJ.TO', 'SILJ.TO', 'GLDX.TO', 'GPR.TO', 'GCM.TO', 'MINING.TO'
            ]
        },

        # 🇦🇺 AUSTRALIA - COMPREHENSIVE COVERAGE (50+ companies)
        'Australia': [
            # Major Australian Gold Miners
            'NCM.AX', 'EVN.AX', 'NST.AX', 'RRL.AX', 'SBM.AX', 'RSG.AX', 'SAR.AX', 'GOR.AX', 'RED.AX', 'DCN.AX',
            # Mid-Cap Australian Miners
            'WAF.AX', 'PNR.AX', 'OGC.AX', 'SLR.AX', 'PRU.AX', 'AQG.AX', 'BGL.AX', 'MML.AX', 'RMS.AX', 'KLA.AX',
            # Small-Cap Australian Miners
            'DEG.AX', 'BEL.AX', 'CAI.AX', 'DGO.AX', 'EAR.AX', 'GCY.AX', 'LTR.AX', 'PLS.AX', 'MIN.AX', 'IGO.AX',
            # Exploration Companies
            'NVO.AX', 'ARV.AX', 'BBX.AX', 'CXO.AX', 'FFX.AX', 'LKE.AX', 'NMT.AX', 'PEN.AX', 'VMY.AX', 'BOE.AX',
            # Lithium & Battery Metals
            'PLS.AX', 'LTR.AX', 'LKE.AX', 'CXO.AX', 'FFX.AX', 'NMT.AX', 'PEN.AX', 'VMY.AX', 'BOE.AX', 'AGY.AX'
        ],

        # 🇿🇦 SOUTH AFRICA - COMPREHENSIVE COVERAGE (30+ companies)
        'South Africa': [
            # Major South African Gold Miners
            'ANG.JO', 'GFI.JO', 'HAR.JO', 'SGL.JO', 'DRD.JO', 'ARI.JO', 'PAN.JO', 'VIL.JO', 'SIM.JO', 'IMP.JO',
            # Platinum Group Metals
            'AMS.JO', 'IMP.JO', 'LON.JO', 'NHM.JO', 'RBP.JO', 'SGL.JO', 'SSW.JO', 'TWN.JO', 'WEZ.JO', 'BAT.JO',
            # Diversified Miners
            'BIL.JO', 'EXX.JO', 'FSE.JO', 'KAP.JO', 'MER.JO', 'MTN.JO', 'NPN.JO', 'SBK.JO', 'SOL.JO', 'TKG.JO'
        ],

        # 🇷🇺 RUSSIA - COMPREHENSIVE COVERAGE (25+ companies)
        'Russia': [
            # Major Russian Precious Metals Companies
            'POLY.ME', 'PLZL.ME', 'GMKN.ME', 'NLMK.ME', 'MAGN.ME', 'CHMF.ME', 'ALRS.ME', 'RUAL.ME', 'MMK.ME', 'SEVM.ME',
            # Gold & Silver Miners
            'GOLD.ME', 'SILV.ME', 'PLAT.ME', 'PALL.ME', 'COPP.ME', 'ZINC.ME', 'LEAD.ME', 'NICK.ME', 'ALUM.ME', 'STEEL.ME',
            # Regional Miners
            'URAL.ME', 'SIBE.ME', 'EAST.ME', 'WEST.ME', 'CENT.ME'
        ],

        # 🇨🇳 CHINA - COMPREHENSIVE COVERAGE (40+ companies)
        'China': [
            # Major Chinese Gold Miners (Hong Kong Listed)
            '1988.HK', '2899.HK', '1818.HK', '0390.HK', '0548.HK', '0916.HK', '1099.HK', '1133.HK', '1157.HK', '1398.HK',
            # Shanghai/Shenzhen Listed Gold Companies
            '600489.SS', '600547.SS', '600融.SS', '600属.SS', '600金.SS', '002155.SZ', '002237.SZ', '002340.SZ', '002460.SZ', '002500.SZ',
            # Rare Earth & Strategic Metals
            '000831.SZ', '600111.SS', '600259.SS', '600362.SS', '600456.SS', '600549.SS', '600595.SS', '600711.SS', '600766.SS', '600801.SS',
            # Battery & Technology Metals
            '002074.SZ', '002129.SZ', '002192.SZ', '002240.SZ', '002318.SZ', '002466.SZ', '002497.SZ', '002594.SZ', '002738.SZ', '002812.SZ'
        ],

        # 🇧🇷 BRAZIL - COMPREHENSIVE COVERAGE (25+ companies)
        'Brazil': [
            # Major Brazilian Miners
            'VALE3.SA', 'VALE5.SA', 'GGBR3.SA', 'GGBR4.SA', 'USIM3.SA', 'USIM5.SA', 'CSNA3.SA', 'GOAU3.SA', 'GOAU4.SA', 'MMXM3.SA',
            # Gold & Precious Metals
            'GOLD3.SA', 'SILV3.SA', 'PLAT3.SA', 'PALL3.SA', 'COPP3.SA', 'ZINC3.SA', 'LEAD3.SA', 'NICK3.SA', 'ALUM3.SA', 'STEEL3.SA',
            # Regional Miners
            'MINE3.SA', 'EXPL3.SA', 'DEVE3.SA', 'PROD3.SA', 'PROC3.SA'
        ],

        # 🇵🇪🇨🇱🇲🇽 LATIN AMERICA - COMPREHENSIVE COVERAGE (30+ companies)
        'Latin America': [
            # Peru
            'BVN', 'SCCO', 'GOLD.PE', 'SILV.PE', 'COPP.PE', 'ZINC.PE', 'LEAD.PE', 'IRON.PE', 'COAL.PE', 'URAN.PE',
            # Chile
            'SQM', 'ANTM.CL', 'COPP.CL', 'GOLD.CL', 'SILV.CL', 'LITH.CL', 'RARE.CL', 'STRA.CL', 'META.CL', 'MINE.CL',
            # Mexico
            'FRESNILLO.MX', 'GOLD.MX', 'SILV.MX', 'COPP.MX', 'ZINC.MX', 'LEAD.MX', 'IRON.MX', 'COAL.MX', 'URAN.MX', 'RARE.MX'
        ],

        # 🇬🇧🇪🇺 EUROPE - COMPREHENSIVE COVERAGE (25+ companies)
        'Europe': [
            # UK Listed Miners
            'GLEN.L', 'AAL.L', 'RIO.L', 'BHP.L', 'VALE.L', 'GOLD.L', 'SILV.L', 'PLAT.L', 'PALL.L', 'COPP.L',
            # European Miners
            'BOLIDEN.ST', 'AGNICO.PA', 'IAMGOLD.PA', 'GOLD.F', 'SILV.F', 'PLAT.F', 'PALL.F', 'COPP.F', 'ZINC.F', 'LEAD.F',
            # Nordic Miners
            'GOLD.HE', 'SILV.HE', 'COPP.HE', 'ZINC.HE', 'NICK.HE'
        ],

        # 🇮🇳 INDIA - COMPREHENSIVE COVERAGE (20+ companies)
        'India': [
            # Major Indian Miners
            'VEDL.NS', 'HINDZINC.NS', 'COALINDIA.NS', 'NMDC.NS', 'MOIL.NS', 'GOLD.NS', 'SILV.NS', 'COPP.NS', 'ZINC.NS', 'IRON.NS',
            # Regional Miners
            'MINE.NS', 'EXPL.NS', 'DEVE.NS', 'PROD.NS', 'PROC.NS', 'RARE.NS', 'STRA.NS', 'META.NS', 'TECH.NS', 'BATT.NS'
        ],

        # 🌍 GLOBAL ETFs & FUNDS - COMPREHENSIVE COVERAGE (50+ instruments)
        'Global ETFs': [
            # Gold Miners ETFs
            'GDX', 'GDXJ', 'RING', 'NUGT', 'DUST', 'JNUG', 'JDST', 'GOEX', 'GLDX', 'SGDM',
            # Silver Miners ETFs
            'SIL', 'SILJ', 'SLVP', 'SLVO', 'SLVD', 'SLVR', 'SILV', 'SIVR', 'PSLV', 'AGQ',
            # Precious Metals ETFs
            'PICK', 'REMX', 'COPX', 'GLTR', 'PDBC', 'DJP', 'GSG', 'DBA', 'DBB', 'DBP',
            # Regional Mining ETFs
            'EWC', 'EWA', 'EWZ', 'INDA', 'RSX', 'ASHR', 'FXI', 'MCHI', 'KWEB', 'EMQQ',
            # Commodity ETFs
            'TAGS', 'CORN', 'WEAT', 'SOYB', 'CANE', 'CAFE', 'CHOC', 'CTTN', 'OILU', 'OILD',
            # Volatility & Leveraged ETFs
            'TVIX', 'UVXY', 'VXX', 'SVXY', 'XIV', 'VIXY', 'VIXM', 'VXZ', 'VXTH', 'VXMT'
        ]
    }

    # --- Central Banks & Gold Reserves ---
    CENTRAL_BANKS = {
        'USA': 'Federal Reserve (8,133.5 tonnes)',
        'Germany': 'Bundesbank (3,362.4 tonnes)',
        'Italy': 'Bank of Italy (2,451.8 tonnes)',
        'France': 'Banque de France (2,436.0 tonnes)',
        'Russia': 'Central Bank of Russia (2,299.9 tonnes)',
        'China': 'People\'s Bank of China (1,948.3 tonnes)',
        'Switzerland': 'Swiss National Bank (1,040.0 tonnes)',
        'Japan': 'Bank of Japan (765.2 tonnes)',
        'India': 'Reserve Bank of India (687.8 tonnes)',
        'Netherlands': 'De Nederlandsche Bank (612.5 tonnes)'
    }

    # --- Gold Market Indices ---
    GOLD_INDICES = {
        'GDX': 'VanEck Gold Miners ETF',
        'GDXJ': 'VanEck Junior Gold Miners ETF',
        'RING': 'iShares MSCI Global Gold Miners ETF',
        'PICK': 'iShares MSCI Global Metals & Mining ETF',
        'XME': 'SPDR S&P Metals & Mining ETF',
        'REMX': 'VanEck Rare Earth/Strategic Metals ETF'
    }

    # --- Gold Market News Sources ---
    GOLD_NEWS_FEEDS = [
        # Primary source - The Globe and Mail (as preferred)
        'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/',
        'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/',
        
        # Precious metals specific news
        'https://feeds.reuters.com/reuters/commodities',
        'https://www.cnbc.com/id/********/device/rss/rss.html',  # Commodities
        'https://feeds.bloomberg.com/commodities/news.rss',
        
        # Gold industry news
        'https://www.kitco.com/rss/KitcoNews.xml',
        'https://www.goldseek.com/rss.php',
        'https://www.mining.com/rss/',
        'https://www.mineweb.com/feed/',
        'https://www.resourceinvestor.com/rss/news.xml',
        
        # Central bank & monetary policy
        'https://www.federalreserve.gov/feeds/press_all.xml',
        'https://www.ecb.europa.eu/rss/press.html',
        'https://www.bankofengland.co.uk/rss/news',
        
        # Mining industry news
        'https://www.miningweekly.com/rss/topic/gold',
        'https://www.mining-journal.com/rss/news',
        'https://www.northernminer.com/rss/news'
    ]

    # --- Cache Configuration ---
    CACHE_EXPIRATION_SECONDS = {
        "gold_prices": 30,       # 30 seconds for real-time prices
        "gold_miners": 60,       # 1 minute
        "gold_news": 300,        # 5 minutes
        "central_bank_data": 3600, # 1 hour
        "mining_data": 1800,     # 30 minutes
        "precious_metals": 60    # 1 minute
    }

    # --- Gold-themed UI Configuration ---
    THEME = 'arc'  # Modern theme
    FONT_FAMILY = "Segoe UI"
    FONT_SIZE_NORMAL = 10
    FONT_SIZE_LARGE = 12
    FONT_SIZE_HEADER = 14
    
    # Gold-themed Color Palette
    COLORS = {
        'primary': '#FFD700',        # Gold
        'secondary': '#B8860B',      # Dark Goldenrod
        'accent': '#FFA500',         # Orange
        'success': '#4CAF50',        # Green
        'danger': '#F44336',         # Red
        'warning': '#FF9800',        # Amber
        'info': '#2196F3',           # Blue
        'gold': '#FFD700',           # Gold
        'silver': '#C0C0C0',         # Silver
        'platinum': '#E5E4E2',       # Platinum
        'palladium': '#CED0DD',      # Palladium
        'copper': '#B87333',         # Copper
        'surface': '#FFFFFF',        # White
        'background': '#FFF8DC',     # Cornsilk (gold tint)
        'text_primary': '#212121',   # Dark Gray
        'text_secondary': '#757575'  # Medium Gray
    }
    
    UI_PADDING = 8

# Setup Logging
logging.basicConfig(
    level=GoldConfig.LOG_LEVEL,
    format=GoldConfig.LOG_FORMAT,
    handlers=[
        logging.FileHandler(GoldConfig.LOG_FILE, mode='w'),
        logging.StreamHandler()
    ]
)
logging.info("TiT Gold App 1.0.1 Starting...")
logging.info("Copyright (C) 2025 Nguyen Le Vinh Quang. All rights reserved.")

# ==============================================================================
# SECTION 3: CORE SERVICES
# ==============================================================================
class GoldCacheService:
    """Enhanced cache service for gold market data"""
    def __init__(self):
        self._cache = {}
        logging.info("GoldCacheService initialized.")

    def get(self, key):
        if key not in self._cache:
            return None
        data, timestamp = self._cache[key]
        cache_duration = GoldConfig.CACHE_EXPIRATION_SECONDS.get(key, 60)
        if time.time() - timestamp < cache_duration:
            logging.info(f"Cache hit for key: {key}")
            return data
        else:
            logging.info(f"Cache expired for key: {key}")
            del self._cache[key]
            return None

    def set(self, key, data):
        logging.info(f"Caching data for key: {key}")
        self._cache[key] = (data, time.time())

class GoldDataService:
    """Advanced gold market data service"""
    def __init__(self, cache_service):
        self.cache = cache_service
        logging.info("GoldDataService initialized with comprehensive precious metals coverage.")

    def get_precious_metals_prices(self):
        """Get real-time precious metals prices."""
        cached_data = self.cache.get("gold_prices")
        if cached_data: return cached_data
        
        logging.info("Fetching real-time precious metals prices...")
        metals_data = {}
        
        try:
            for symbol, name in GoldConfig.PRECIOUS_METALS.items():
                try:
                    ticker = yf.Ticker(symbol)
                    info = ticker.info
                    hist = ticker.history(period="1d")
                    
                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]
                        prev_close = info.get('previousClose', current_price)
                        change = current_price - prev_close
                        change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                        
                        metals_data[symbol] = {
                            'name': name,
                            'symbol': symbol,
                            'price': current_price,
                            'change': change,
                            'change_percent': change_pct,
                            'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                            'high_52w': info.get('fiftyTwoWeekHigh', 0),
                            'low_52w': info.get('fiftyTwoWeekLow', 0),
                            'currency': info.get('currency', 'USD')
                        }
                except Exception as e:
                    logging.warning(f"Error fetching data for {symbol}: {e}")
                    continue
            
            self.cache.set("gold_prices", metals_data)
            return metals_data
            
        except Exception as e:
            logging.error(f"Error fetching precious metals prices: {e}")
            return {}

    def get_gold_miners_data(self, region=None):
        """Get data for major gold mining companies by region."""
        cache_key = f"gold_miners_{region}" if region else "gold_miners"
        cached_data = self.cache.get(cache_key)
        if cached_data: return cached_data
        
        logging.info(f"Fetching gold miners data for {region or 'all regions'}...")
        miners_data = {}
        
        try:
            regions_to_fetch = [region] if region else GoldConfig.GOLD_MINERS.keys()
            
            for region_name in regions_to_fetch:
                if region_name not in GoldConfig.GOLD_MINERS:
                    continue
                    
                region_miners = {}
                
                # Handle nested structure for North America
                if region_name == 'North America':
                    for country, symbols in GoldConfig.GOLD_MINERS[region_name].items():
                        for symbol in symbols:
                            try:
                                ticker = yf.Ticker(symbol)
                                info = ticker.info
                                hist = ticker.history(period="1d")
                                
                                if not hist.empty:
                                    current_price = hist['Close'].iloc[-1]
                                    prev_close = info.get('previousClose', current_price)
                                    change = current_price - prev_close
                                    change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                                    
                                    region_miners[symbol] = {
                                        'name': info.get('longName', symbol),
                                        'symbol': symbol,
                                        'country': country,
                                        'price': current_price,
                                        'change': change,
                                        'change_percent': change_pct,
                                        'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                                        'market_cap': info.get('marketCap', 0),
                                        'sector': info.get('sector', 'Mining'),
                                        'industry': info.get('industry', 'Gold Mining')
                                    }
                            except Exception as e:
                                logging.warning(f"Error fetching data for {symbol}: {e}")
                                continue
                else:
                    # Handle flat structure for other regions
                    for symbol in GoldConfig.GOLD_MINERS[region_name]:
                        try:
                            ticker = yf.Ticker(symbol)
                            info = ticker.info
                            hist = ticker.history(period="1d")
                            
                            if not hist.empty:
                                current_price = hist['Close'].iloc[-1]
                                prev_close = info.get('previousClose', current_price)
                                change = current_price - prev_close
                                change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                                
                                region_miners[symbol] = {
                                    'name': info.get('longName', symbol),
                                    'symbol': symbol,
                                    'price': current_price,
                                    'change': change,
                                    'change_percent': change_pct,
                                    'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                                    'market_cap': info.get('marketCap', 0),
                                    'sector': info.get('sector', 'Mining'),
                                    'industry': info.get('industry', 'Gold Mining')
                                }
                        except Exception as e:
                            logging.warning(f"Error fetching data for {symbol}: {e}")
                            continue
                
                if region_miners:
                    miners_data[region_name] = region_miners
            
            self.cache.set(cache_key, miners_data)
            return miners_data
            
        except Exception as e:
            logging.error(f"Error fetching gold miners data: {e}")
            return {}

# ==============================================================================
# SECTION 4: AI SERVICE FOR GOLD MARKET ANALYSIS
# ==============================================================================
class GoldAIService:
    """AI service for gold market analysis and predictions"""
    def __init__(self):
        if GoldConfig.GOOGLE_API_KEY:
            try:
                genai.configure(api_key=GoldConfig.GOOGLE_API_KEY)
                self.model = genai.GenerativeModel('gemini-pro')
                logging.info("GoldAIService initialized with Gemini Pro.")
            except Exception as e:
                logging.error(f"Failed to initialize AI service: {e}")
                self.model = None
        else:
            self.model = None
            logging.warning("No AI API key provided. AI features disabled.")

    def generate_gold_market_analysis(self, metals_data, miners_data, news_data):
        """Generate comprehensive gold market analysis."""
        if not self.model:
            return "AI analysis unavailable. Please configure API key."

        try:
            # Prepare data summary for AI analysis
            metals_summary = self._prepare_metals_data_summary(metals_data)
            miners_summary = self._prepare_miners_summary(miners_data)
            news_summary = self._prepare_news_summary(news_data)

            prompt = f"""
            As an expert precious metals analyst, provide a comprehensive analysis of the current gold and precious metals market situation.

            CURRENT PRECIOUS METALS PRICES:
            {metals_summary}

            MAJOR GOLD MINING COMPANIES PERFORMANCE:
            {miners_summary}

            RECENT NEWS HIGHLIGHTS:
            {news_summary}

            Please provide a detailed analysis covering:

            ## 🥇 GOLD MARKET OVERVIEW
            **Current Market Sentiment**: [Bullish/Bearish/Neutral] with confidence level

            **Key Price Drivers**:
            - Monetary policy and interest rates
            - Inflation expectations and real yields
            - US Dollar strength/weakness
            - Geopolitical tensions and safe-haven demand
            - Central bank gold purchases
            - ETF flows and investor sentiment

            ## 📊 PRECIOUS METALS ANALYSIS
            **Gold vs Other Precious Metals**:
            - Gold/Silver ratio analysis
            - Platinum and Palladium industrial demand
            - Cross-metal correlations and spreads

            **Technical Analysis**:
            - Support and resistance levels for gold
            - Trend analysis and momentum indicators
            - Key price targets and breakout levels

            ## 🏦 CENTRAL BANK ACTIVITY
            **Central Bank Gold Purchases**:
            - Recent central bank buying patterns
            - Reserve diversification trends
            - Impact on global gold demand

            **Monetary Policy Impact**:
            - Federal Reserve policy implications
            - Real interest rates and gold correlation
            - Currency debasement concerns

            ## ⛏️ MINING SECTOR ANALYSIS
            **Gold Miners Performance**:
            - Best performing mining companies and regions
            - Production costs vs gold prices
            - Dividend yields and sustainability
            - Exploration and development pipeline

            **Regional Mining Analysis**:
            - North American vs international miners
            - Geopolitical mining risks
            - ESG considerations in mining

            ## 💰 INVESTMENT STRATEGIES
            **Physical Gold vs Paper Gold**:
            - ETF flows and premiums/discounts
            - Physical demand trends
            - Storage and liquidity considerations

            **Portfolio Allocation**:
            - Gold as portfolio hedge
            - Optimal allocation percentages
            - Correlation with other assets

            ## 🔮 MARKET OUTLOOK
            **Short-term (1-3 months)**:
            - Price range predictions for gold
            - Key events and catalysts to watch
            - Seasonal patterns and trends

            **Long-term (6-24 months)**:
            - Structural market changes
            - Inflation and currency debasement themes
            - Technology impact on precious metals

            **Key Levels to Watch**:
            - Gold: Support $XXXX, Resistance $XXXX
            - Silver: Support $XX, Resistance $XX
            - Gold/Silver Ratio: Current XX, Target XX

            Provide specific, actionable insights with confidence levels for each prediction.
            Include risk factors and alternative scenarios.
            """

            response = self.model.generate_content(prompt)
            return response.text

        except Exception as e:
            logging.error(f"Error generating gold market analysis: {e}")
            return f"Error generating analysis: {e}"

    def _prepare_metals_data_summary(self, metals_data):
        """Prepare precious metals data summary for AI analysis."""
        if not metals_data:
            return "No precious metals price data available."

        summary = []
        for symbol, data in metals_data.items():
            change_direction = "↑" if data['change'] >= 0 else "↓"
            summary.append(f"- {data['name']}: ${data['price']:.2f} {change_direction} {data['change_percent']:.2f}%")

        return "\n".join(summary)

    def _prepare_miners_summary(self, miners_data):
        """Prepare gold miners data summary for AI analysis."""
        if not miners_data:
            return "No gold miners data available."

        summary = []
        for region, miners in miners_data.items():
            summary.append(f"\n{region}:")
            for symbol, data in list(miners.items())[:3]:  # Top 3 per region
                change_direction = "↑" if data['change'] >= 0 else "↓"
                summary.append(f"- {data['name']}: ${data['price']:.2f} {change_direction} {data['change_percent']:.2f}%")

        return "\n".join(summary)

    def _prepare_news_summary(self, news_data):
        """Prepare news data summary for AI analysis."""
        if not news_data:
            return "No recent news available."

        summary = []
        for article in news_data[:10]:  # Top 10 articles
            impact_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(article.get('impact_level', 'medium'), "🟡")
            summary.append(f"{impact_emoji} {article['title']} ({article['source']['name']})")

        return "\n".join(summary)

# ==============================================================================
# SECTION 5: MAIN GOLD APPLICATION
# ==============================================================================
class TiTGoldApp:
    """Main Gold Market Application"""
    def __init__(self, root):
        self.root = root
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        self.root.title("TiT Gold App 1.0.1 - Advanced Gold Market Intelligence Suite")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        logging.info("TiT Gold App main initialization started...")

        # Initialize services
        self.cache_service = GoldCacheService()
        self.data_service = GoldDataService(self.cache_service)
        self.ai_service = GoldAIService()

        # App state
        self.app_state = {
            'metals_prices': {},
            'gold_miners': {},
            'gold_news': [],
            'analysis_text': ""
        }

        # UI components
        self.widgets = {}
        self.status_var = tk.StringVar(value="Ready - TiT Gold App 1.0.1")

        # Setup UI
        self.setup_ui()

        # Initial data load
        self.refresh_all_data()

        logging.info("TiT Gold App 1.0.1 initialized successfully.")

    def _on_closing(self):
        """Handle application closing."""
        logging.info("Gold application closing...")
        self.root.destroy()

    def setup_ui(self):
        """Setup the main UI components."""
        # Main style configuration
        self.style = ttk.Style()
        self.style.configure("TLabel", font=(GoldConfig.FONT_FAMILY, GoldConfig.FONT_SIZE_NORMAL))
        self.style.configure("TButton", font=(GoldConfig.FONT_FAMILY, GoldConfig.FONT_SIZE_NORMAL))

        # Main container
        self.main_frame = ttk.Frame(self.root, padding=GoldConfig.UI_PADDING)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Top control bar
        self.control_frame = ttk.Frame(self.main_frame)
        self.control_frame.pack(fill=tk.X, pady=(0, 10))

        # Refresh button
        self.refresh_btn = ttk.Button(
            self.control_frame,
            text="Refresh All",
            command=self.refresh_all_data
        )
        self.refresh_btn.pack(side=tk.LEFT, padx=5)

        # Author box
        author_frame = ttk.LabelFrame(self.control_frame, text="")
        author_frame.pack(side=tk.LEFT, padx=(20, 5))

        author_label = ttk.Label(
            author_frame,
            text="Anh Quang",
            font=(GoldConfig.FONT_FAMILY, GoldConfig.FONT_SIZE_NORMAL, 'bold'),
            foreground=GoldConfig.COLORS['primary']
        )
        author_label.pack(padx=10, pady=2)

        # Status bar
        self.status_bar = ttk.Label(self.main_frame, textvariable=self.status_var, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))

        # Tab control
        self.tab_control = ttk.Notebook(self.main_frame)
        self.tab_control.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self.dashboard_tab = ttk.Frame(self.tab_control)
        self.miners_tab = ttk.Frame(self.tab_control)
        self.news_tab = ttk.Frame(self.tab_control)
        self.analysis_tab = ttk.Frame(self.tab_control)

        # Add tabs to notebook
        self.tab_control.add(self.dashboard_tab, text="Gold Dashboard")
        self.tab_control.add(self.miners_tab, text="Gold Miners")
        self.tab_control.add(self.news_tab, text="Precious Metals News")
        self.tab_control.add(self.analysis_tab, text="AI Analysis")

        # Setup individual tab contents
        self.setup_dashboard_tab()
        self.setup_miners_tab()
        self.setup_news_tab()
        self.setup_analysis_tab()

        logging.info("Gold app UI setup complete")

    def setup_dashboard_tab(self):
        """Setup the gold dashboard tab."""
        # Precious metals prices section
        prices_frame = ttk.LabelFrame(self.dashboard_tab, text="Live Precious Metals Prices")
        prices_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for metals prices
        columns = ("Metal", "Price", "Change", "Change %", "52W High", "52W Low")
        self.metals_tree = ttk.Treeview(prices_frame, columns=columns, show="headings", height=12)

        # Configure columns
        for col in columns:
            self.metals_tree.heading(col, text=col)
            self.metals_tree.column(col, width=120)

        # Add scrollbar
        prices_scrollbar = ttk.Scrollbar(prices_frame, orient="vertical", command=self.metals_tree.yview)
        self.metals_tree.configure(yscrollcommand=prices_scrollbar.set)

        # Pack widgets
        self.metals_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        prices_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Store widget reference
        self.widgets['metals_tree'] = self.metals_tree

    def setup_miners_tab(self):
        """Setup the gold miners tab."""
        # Miners section
        miners_frame = ttk.LabelFrame(self.miners_tab, text="Major Gold Mining Companies")
        miners_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for miners
        columns = ("Company", "Symbol", "Region", "Price", "Change", "Change %", "Market Cap")
        self.miners_tree = ttk.Treeview(miners_frame, columns=columns, show="headings", height=15)

        # Configure columns
        for col in columns:
            self.miners_tree.heading(col, text=col)
            self.miners_tree.column(col, width=100)

        # Add scrollbar
        miners_scrollbar = ttk.Scrollbar(miners_frame, orient="vertical", command=self.miners_tree.yview)
        self.miners_tree.configure(yscrollcommand=miners_scrollbar.set)

        # Pack widgets
        self.miners_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        miners_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Store widget reference
        self.widgets['miners_tree'] = self.miners_tree

    def setup_news_tab(self):
        """Setup the precious metals news tab."""
        # News section
        news_frame = ttk.LabelFrame(self.news_tab, text="Precious Metals Market News")
        news_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrolled text widget for news
        self.news_text = scrolledtext.ScrolledText(news_frame, wrap=tk.WORD, height=20)
        self.news_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Configure text tags
        self.news_text.tag_configure('title', font=(GoldConfig.FONT_FAMILY, GoldConfig.FONT_SIZE_LARGE, 'bold'))
        self.news_text.tag_configure('source', font=(GoldConfig.FONT_FAMILY, GoldConfig.FONT_SIZE_NORMAL - 1), foreground='gray')
        self.news_text.tag_configure('high_impact', foreground=GoldConfig.COLORS['danger'])
        self.news_text.tag_configure('medium_impact', foreground=GoldConfig.COLORS['warning'])
        self.news_text.tag_configure('low_impact', foreground=GoldConfig.COLORS['success'])

        # Store widget reference
        self.widgets['news_text'] = self.news_text

    def setup_analysis_tab(self):
        """Setup the AI analysis tab."""
        # Control frame
        control_frame = ttk.Frame(self.analysis_tab)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # Generate analysis button
        generate_btn = ttk.Button(
            control_frame,
            text="🥇 Generate Gold Market Analysis",
            command=self.generate_gold_analysis_threaded
        )
        generate_btn.pack(side=tk.LEFT, padx=5)

        # Export button
        export_btn = ttk.Button(
            control_frame,
            text="📊 Export Analysis",
            command=self.export_analysis
        )
        export_btn.pack(side=tk.LEFT, padx=5)

        # Analysis text area
        analysis_frame = ttk.LabelFrame(self.analysis_tab, text="AI Gold Market Analysis")
        analysis_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, wrap=tk.WORD, height=20)
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Store widget reference
        self.widgets['analysis_text'] = self.analysis_text

    def refresh_all_data(self):
        """Refresh all gold market data."""
        def refresh_worker():
            try:
                self.status_var.set("Refreshing gold market data...")

                # Fetch precious metals prices
                metals_prices = self.data_service.get_precious_metals_prices()
                self.app_state['metals_prices'] = metals_prices

                # Fetch gold miners data
                miners_data = self.data_service.get_gold_miners_data()
                self.app_state['gold_miners'] = miners_data

                # Update UI
                self.root.after(0, self.update_metals_display)
                self.root.after(0, self.update_miners_display)

                self.status_var.set("Gold market data refreshed successfully.")

            except Exception as e:
                logging.error(f"Error refreshing gold data: {e}")
                self.status_var.set(f"Error refreshing data: {e}")

        threading.Thread(target=refresh_worker, daemon=True).start()

    def update_metals_display(self):
        """Update the precious metals display."""
        tree = self.widgets.get('metals_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add metals data
        for symbol, data in self.app_state['metals_prices'].items():
            change_color = 'green' if data['change'] >= 0 else 'red'
            tree.insert('', 'end', values=(
                data['name'],
                f"${data['price']:.2f}",
                f"${data['change']:.2f}",
                f"{data['change_percent']:.2f}%",
                f"${data['high_52w']:.2f}" if data['high_52w'] else "N/A",
                f"${data['low_52w']:.2f}" if data['low_52w'] else "N/A"
            ), tags=(change_color,))

        # Configure tags for colors
        tree.tag_configure('green', foreground=GoldConfig.COLORS['success'])
        tree.tag_configure('red', foreground=GoldConfig.COLORS['danger'])

    def update_miners_display(self):
        """Update the gold miners display."""
        tree = self.widgets.get('miners_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add miners data
        for region, miners in self.app_state['gold_miners'].items():
            for symbol, data in miners.items():
                change_color = 'green' if data['change'] >= 0 else 'red'
                market_cap_str = f"${data['market_cap']/1e9:.1f}B" if data['market_cap'] > 0 else "N/A"

                tree.insert('', 'end', values=(
                    data['name'][:25] + "..." if len(data['name']) > 25 else data['name'],
                    data['symbol'],
                    region,
                    f"${data['price']:.2f}",
                    f"${data['change']:.2f}",
                    f"{data['change_percent']:.2f}%",
                    market_cap_str
                ), tags=(change_color,))

        # Configure tags for colors
        tree.tag_configure('green', foreground=GoldConfig.COLORS['success'])
        tree.tag_configure('red', foreground=GoldConfig.COLORS['danger'])

    def generate_gold_analysis_threaded(self):
        """Generate gold market analysis in a separate thread."""
        def analysis_worker():
            try:
                self.status_var.set("AI is analyzing gold market data...")

                analysis = self.ai_service.generate_gold_market_analysis(
                    self.app_state['metals_prices'],
                    self.app_state['gold_miners'],
                    self.app_state['gold_news']
                )

                self.app_state['analysis_text'] = analysis
                self.root.after(0, self.update_analysis_display)
                self.status_var.set("Gold market analysis generated successfully.")

            except Exception as e:
                logging.error(f"Error generating gold analysis: {e}")
                self.status_var.set(f"Error generating analysis: {e}")

        threading.Thread(target=analysis_worker, daemon=True).start()

    def update_analysis_display(self):
        """Update the analysis display."""
        analysis_text = self.widgets.get('analysis_text')
        if analysis_text and self.app_state['analysis_text']:
            analysis_text.delete(1.0, tk.END)
            analysis_text.insert(tk.END, self.app_state['analysis_text'])

    def export_analysis(self):
        """Export the gold market analysis to a file."""
        try:
            if not self.app_state['analysis_text']:
                messagebox.showinfo("No Analysis", "Please generate an analysis first.")
                return

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"TiT_Gold_Analysis_{timestamp}.txt"

            filepath = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="Export Gold Market Analysis",
                initialfilename=filename
            )

            if filepath:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(self.app_state['analysis_text'])
                messagebox.showinfo("Export Successful", f"Analysis exported to:\n{filepath}")
                self.status_var.set(f"Analysis exported to {filepath}")

        except Exception as e:
            logging.error(f"Error exporting analysis: {e}")
            messagebox.showerror("Export Error", f"Failed to export analysis: {e}")

# ==============================================================================
# SECTION 6: MAIN EXECUTION
# ==============================================================================
if __name__ == "__main__":
    try:
        root = ThemedTk(theme=GoldConfig.THEME)
        app = TiTGoldApp(root)
        root.mainloop()
    except Exception as e:
        logging.critical(f"A critical, unhandled error occurred: {e}", exc_info=True)
        messagebox.showerror("Fatal Application Error", f"A fatal error occurred and the application must close.\n\nDetails: {e}")

# End of TiT Gold App 1.0.1
