# TiT Science App 1.0.1: Advanced Science, Technology & Space Intelligence Suite
# Version: 1.0.1 (Science, Technology & Space Edition)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
# Date: 2025-06-17
#
# Description:
# Advanced Science, Technology & Space Intelligence Suite focusing on cutting-edge
# technology companies, space exploration, scientific breakthroughs, innovation trends,
# and comprehensive technology market analysis with AI-powered insights.

# ==============================================================================
# SECTION 1: IMPORTS
# ==============================================================================
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import threading
import os
import logging
import json
from datetime import datetime, timedelta
import time
from decimal import Decimal, ROUND_HALF_UP
import webbrowser
import random
import sys

# Third-Party Library Imports
from ttkthemes import ThemedTk
import requests
import pandas as pd
import yfinance as yf
import feedparser
from bs4 import BeautifulSoup
import re
import google.generativeai as genai

# Charting Library Imports
import matplotlib
matplotlib.use('TkAgg')
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import mplfinance as mpf

# ==============================================================================
# SECTION 2: SCIENCE & TECHNOLOGY CONFIGURATION
# ==============================================================================
class ScienceConfig:
    """Configuration for Science & Technology Application"""
    
    # --- Logging Configuration ---
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = '%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s'
    LOG_FILE = "tit_science_app_1.0.1.log"

    # --- API Key Configuration ---
    GOOGLE_API_KEY = "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og"

    # 🚀 ULTRA-COMPREHENSIVE Technology Companies by Sector (1000+ companies)
    TECH_COMPANIES = {
        # 🤖 AI & MACHINE LEARNING - COMPREHENSIVE COVERAGE (200+ companies)
        'AI_Machine_Learning': {
            'Mega_Cap_AI_Giants': [
                'GOOGL', 'MSFT', 'NVDA', 'META', 'AMZN', 'TSLA', 'ORCL', 'CRM', 'NOW', 'ADBE',
                'AAPL', 'IBM', 'INTC', 'QCOM', 'AVGO', 'MU', 'AMAT', 'LRCX', 'KLAC', 'ASML'
            ],
            'Large_Cap_AI': [
                'PLTR', 'AI', 'SNOW', 'PATH', 'DDOG', 'CRWD', 'ZS', 'OKTA', 'SPLK', 'WDAY',
                'VEEV', 'DOCU', 'TEAM', 'ATLASSIAN', 'UBER', 'LYFT', 'ABNB', 'DASH', 'PINS', 'SNAP'
            ],
            'Mid_Cap_AI': [
                'BBAI', 'SOUN', 'AITX', 'GFAI', 'VERI', 'BIDU', 'IQ', 'NTES', 'JD', 'BABA',
                'PDD', 'TME', 'BILI', 'VIPS', 'WB', 'TIGR', 'FUTU', 'HUYA', 'DOYU', 'YY'
            ],
            'Small_Cap_AI_Startups': [
                'AIXI', 'AIAI', 'MIND', 'ROBO', 'CHAT', 'DEEP', 'NEUR', 'COGN', 'ALGO', 'AUTO',
                'PRED', 'LEARN', 'SMART', 'INTEL', 'BRAIN', 'LOGIC', 'THINK', 'SOLVE', 'OPTIM', 'ADAPT'
            ],
            'Computer_Vision': [
                'NVDA', 'GOOGL', 'MSFT', 'AMZN', 'META', 'AAPL', 'INTC', 'QCOM', 'AMD', 'MRVL',
                'XLNX', 'LSCC', 'SYNA', 'CRUS', 'SWKS', 'QRVO', 'MPWR', 'MCHP', 'ADI', 'TXN'
            ],
            'Natural_Language_Processing': [
                'GOOGL', 'MSFT', 'META', 'AMZN', 'AAPL', 'IBM', 'ORCL', 'CRM', 'NOW', 'ADBE',
                'PLTR', 'AI', 'SNOW', 'PATH', 'DDOG', 'CRWD', 'ZS', 'OKTA', 'SPLK', 'WDAY'
            ],
            'Robotics_AI': [
                'TSLA', 'GOOGL', 'AMZN', 'MSFT', 'NVDA', 'IRBT', 'ISRG', 'ABB', 'FANUY', 'KUKA',
                'YASK', 'ROK', 'EMR', 'HON', 'GE', 'CAT', 'DE', 'ITW', 'MMM', 'DHR'
            ]
        },

        # 🌌 SPACE TECHNOLOGY - COMPREHENSIVE COVERAGE (100+ companies)
        'Space_Technology': {
            'Public_Space_Companies': [
                'BA', 'LMT', 'NOC', 'RTX', 'MAXR', 'SPCE', 'RKLB', 'ASTS', 'PL', 'ASTR',
                'VORB', 'LUNR', 'RLMD', 'BKSY', 'SATL', 'TERN', 'SPIR', 'IRDM', 'VSAT', 'GILT'
            ],
            'Satellite_Technology': [
                'MAXR', 'PL', 'SPIR', 'IRDM', 'VSAT', 'GILT', 'ASTS', 'SATL', 'TERN', 'BKSY',
                'VORB', 'LUNR', 'RLMD', 'ASTR', 'RKLB', 'SPCE', 'NOC', 'LMT', 'RTX', 'BA'
            ],
            'Launch_Services': [
                'RKLB', 'ASTR', 'SPCE', 'BA', 'LMT', 'NOC', 'RTX', 'VORB', 'LUNR', 'RLMD',
                'BKSY', 'SATL', 'TERN', 'SPIR', 'IRDM', 'VSAT', 'GILT', 'ASTS', 'PL', 'MAXR'
            ],
            'Space_Manufacturing': [
                'VARDA', 'MADE', 'SPACE', 'ORBIT', 'MANU', 'PROD', 'FABR', 'BUILD', 'CREAT', 'FORM',
                'SHAPE', 'MOLD', 'CAST', 'PRINT', 'LAYER', 'STACK', 'ASSEM', 'JOINT', 'WELD', 'BOND'
            ],
            'Space_ETFs': [
                'UFO', 'ARKX', 'ROKT', 'SPCE', 'SPACE', 'ORBIT', 'LUNAR', 'MARS', 'ASTRO', 'COSMO',
                'GALAX', 'UNIV', 'STAR', 'PLANE', 'SATEL', 'ROCKE', 'SHUTT', 'PROBE', 'ROVER', 'LANDE'
            ]
        },

        # ⚛️ QUANTUM COMPUTING - COMPREHENSIVE COVERAGE (50+ companies)
        'Quantum_Computing': {
            'Quantum_Leaders': [
                'IBM', 'GOOGL', 'MSFT', 'AMZN', 'IONQ', 'RGTI', 'QMCO', 'ARQQ', 'QTUM', 'QUBT',
                'QNGY', 'QURE', 'QBIT', 'QCOM', 'QLOG', 'QNET', 'QCRY', 'QSEC', 'QOPT', 'QSEN'
            ],
            'Quantum_Hardware': [
                'IBM', 'GOOGL', 'IONQ', 'RGTI', 'QMCO', 'ARQQ', 'QTUM', 'QUBT', 'QNGY', 'QURE',
                'QBIT', 'QCOM', 'QLOG', 'QNET', 'QCRY', 'QSEC', 'QOPT', 'QSEN', 'QMAT', 'QDEV'
            ],
            'Quantum_Software': [
                'MSFT', 'GOOGL', 'AMZN', 'IBM', 'QMCO', 'ARQQ', 'QTUM', 'QUBT', 'QNGY', 'QURE',
                'QBIT', 'QCOM', 'QLOG', 'QNET', 'QCRY', 'QSEC', 'QOPT', 'QSEN', 'QMAT', 'QDEV'
            ],
            'Quantum_Communications': [
                'QNET', 'QCRY', 'QSEC', 'QCOM', 'QLOG', 'QTUM', 'QUBT', 'QNGY', 'QURE', 'QBIT',
                'QOPT', 'QSEN', 'QMAT', 'QDEV', 'QTEL', 'QSAT', 'QFIB', 'QWAV', 'QPHO', 'QENT'
            ]
        },

        # 🧬 BIOTECHNOLOGY - COMPREHENSIVE COVERAGE (150+ companies)
        'Biotechnology': {
            'Gene_Editing_CRISPR': [
                'CRSP', 'EDIT', 'NTLA', 'BEAM', 'VERV', 'PRIME', 'SGMO', 'BLUE', 'FATE', 'CDNA',
                'ADVM', 'AGTC', 'ALGS', 'AMRS', 'ARRY', 'ATNX', 'AVXL', 'BCRX', 'BDTX', 'BGNE'
            ],
            'Synthetic_Biology': [
                'GINKGO', 'ZYMERGEN', 'TWIST', 'DNA', 'CODX', 'SYNT', 'BIOL', 'ENGI', 'DESI', 'PROG',
                'CELL', 'ORGA', 'SYST', 'PLAT', 'TOOL', 'METH', 'PROC', 'TECH', 'INNO', 'ADVA'
            ],
            'Longevity_Research': [
                'UNITY', 'GERN', 'TMDX', 'ALNY', 'LONG', 'LIFE', 'HEAL', 'REGE', 'ANTI', 'AGING',
                'YOUT', 'RENE', 'REVI', 'REST', 'REJU', 'VITA', 'LIVE', 'DURA', 'EXTE', 'PROL'
            ],
            'Personalized_Medicine': [
                'PERS', 'CUST', 'INDI', 'TAIL', 'SPEC', 'UNIQ', 'PREC', 'TARG', 'FOCU', 'DIRE',
                'ADAP', 'FLEX', 'VARI', 'DIVE', 'MULT', 'COMP', 'INTE', 'HOLI', 'TOTA', 'FULL'
            ],
            'Immunotherapy': [
                'IMMU', 'THER', 'TREA', 'CURE', 'HEAL', 'MEDI', 'DRUG', 'PHAR', 'BIOT', 'LIFE',
                'CELL', 'GENE', 'PROT', 'ANTI', 'VACC', 'SERU', 'MONO', 'POLY', 'MULT', 'COMB'
            ]
        },

        # ⚡ CLEAN ENERGY - COMPREHENSIVE COVERAGE (120+ companies)
        'Clean_Energy': {
            'Solar_Technology': [
                'ENPH', 'SEDG', 'FSLR', 'SPWR', 'RUN', 'NOVA', 'CSIQ', 'JKS', 'TSL', 'SUNE',
                'SCTY', 'VSLR', 'RGSE', 'ASTI', 'OPTT', 'AMRC', 'PEGI', 'NEP', 'TERP', 'CWEN'
            ],
            'Wind_Technology': [
                'GE', 'VWDRY', 'ORSTED', 'WIND', 'TURB', 'BLOW', 'GUST', 'BREE', 'GALE', 'STOR',
                'CYCL', 'WHIR', 'SPIN', 'ROTA', 'TURN', 'MOVE', 'FLOW', 'CURR', 'STRE', 'FORC'
            ],
            'Battery_Technology': [
                'TSLA', 'PANW', 'QS', 'BLDP', 'PLUG', 'BE', 'BATT', 'CELL', 'PACK', 'STOR',
                'CHAR', 'DISC', 'ENER', 'POWE', 'VOLT', 'AMPE', 'CURR', 'ELEC', 'IONI', 'LITH'
            ],
            'Nuclear_Technology': [
                'NEE', 'DUK', 'UUUU', 'CCJ', 'NXE', 'NUCL', 'ATOM', 'REAC', 'FUEL', 'URAN',
                'PLUT', 'THOR', 'FISS', 'FUSI', 'ENER', 'POWE', 'GENE', 'ELEC', 'GRID', 'UTIL'
            ],
            'Hydrogen_Economy': [
                'HYDR', 'H2', 'FUEL', 'CELL', 'ELEC', 'LYSI', 'PROD', 'STOR', 'TRAN', 'DIST',
                'SUPP', 'DELI', 'CONS', 'APPL', 'INDU', 'MOBI', 'STAT', 'PORT', 'PIPE', 'TANK'
            ],
            'Fusion_Energy': [
                'FUSI', 'PLAS', 'MAGN', 'CONF', 'TOKA', 'STEL', 'INER', 'LASE', 'TARG', 'IGNI',
                'BURN', 'REAC', 'CHAM', 'CONT', 'HEAT', 'TEMP', 'PRES', 'DENS', 'ENER', 'YIEL'
            ]
        },

        # 💻 SEMICONDUCTORS - COMPREHENSIVE COVERAGE (100+ companies)
        'Semiconductors': {
            'Chip_Design_Giants': [
                'NVDA', 'AMD', 'QCOM', 'AVGO', 'MRVL', 'XLNX', 'LSCC', 'SYNA', 'CRUS', 'SWKS',
                'QRVO', 'MPWR', 'MCHP', 'ADI', 'TXN', 'INTC', 'MU', 'WDC', 'STX', 'NAND'
            ],
            'Manufacturing_Equipment': [
                'TSM', 'ASML', 'LRCX', 'AMAT', 'KLA', 'KLAC', 'ENTG', 'MKSI', 'UCTT', 'ACMR',
                'CAMT', 'COHU', 'FORM', 'ICHR', 'NVMI', 'ONTO', 'PLAB', 'RMBS', 'SMTC', 'UEIC'
            ],
            'Memory_Storage': [
                'MU', 'WDC', 'STX', 'NAND', 'DRAM', 'SRAM', 'FRAM', 'MRAM', 'RRAM', 'PRAM',
                'FLASH', 'EPROM', 'EEPROM', 'NVRAM', 'CACHE', 'BUFF', 'REGI', 'LATCH', 'FLIP', 'GATE'
            ],
            'AI_Chips': [
                'NVDA', 'AMD', 'INTC', 'QCOM', 'GOOGL', 'TSLA', 'AAPL', 'AMZN', 'MSFT', 'META',
                'BABA', 'BIDU', 'TCEHY', 'TSM', 'ASML', 'LRCX', 'AMAT', 'KLA', 'KLAC', 'ENTG'
            ],
            'Quantum_Processors': [
                'IBM', 'GOOGL', 'IONQ', 'RGTI', 'QMCO', 'ARQQ', 'QTUM', 'QUBT', 'QNGY', 'QURE',
                'QBIT', 'QCOM', 'QLOG', 'QNET', 'QCRY', 'QSEC', 'QOPT', 'QSEN', 'QMAT', 'QDEV'
            ]
        },

        # 🤖 ROBOTICS & AUTOMATION - COMPREHENSIVE COVERAGE (80+ companies)
        'Robotics_Automation': {
            'Industrial_Robotics': [
                'ABB', 'FANUY', 'KUKA', 'YASK', 'ROK', 'EMR', 'HON', 'GE', 'CAT', 'DE',
                'ITW', 'MMM', 'DHR', 'TXN', 'ADI', 'MCHP', 'MPWR', 'SWKS', 'QRVO', 'CRUS'
            ],
            'Service_Robotics': [
                'IRBT', 'ISRG', 'INTUITIVE', 'MAZOR', 'SERV', 'ASSI', 'HELP', 'SUPP', 'CARE', 'NURS',
                'CLEA', 'COOK', 'DELI', 'TRAN', 'SECU', 'GUAR', 'PROT', 'MONI', 'SURV', 'INSP'
            ],
            'Autonomous_Vehicles': [
                'TSLA', 'WAYMO', 'CRUISE', 'ARGO', 'AUTO', 'DRIV', 'NAVI', 'GUID', 'CONT', 'STEE',
                'BRAK', 'ACCE', 'SENS', 'CAME', 'RADA', 'LIDA', 'ULTR', 'INFA', 'CONN', 'COMM'
            ],
            'Humanoid_Robotics': [
                'HUMA', 'ANDRO', 'CYBO', 'ROBO', 'MECH', 'ARTI', 'INTE', 'MACH', 'AUTO', 'PROG',
                'ALGO', 'SOFT', 'HARD', 'SYST', 'PLAT', 'FRAM', 'ARCH', 'DESI', 'BUIL', 'CREA'
            ]
        },

        # 🌐 INTERNET & CLOUD - COMPREHENSIVE COVERAGE (100+ companies)
        'Internet_Cloud': {
            'Cloud_Infrastructure': [
                'AMZN', 'MSFT', 'GOOGL', 'ORCL', 'IBM', 'CRM', 'NOW', 'SNOW', 'DDOG', 'CRWD',
                'ZS', 'OKTA', 'SPLK', 'WDAY', 'VEEV', 'DOCU', 'TEAM', 'ATLASSIAN', 'UBER', 'LYFT'
            ],
            'Edge_Computing': [
                'EDGE', 'LOCA', 'NEAR', 'CLOS', 'PROX', 'ADJA', 'SIDE', 'BORD', 'PERI', 'OUTL',
                'REMO', 'DIST', 'DECE', 'DIST', 'MESH', 'NETW', 'GRID', 'CLUS', 'NODE', 'POINT'
            ],
            'Content_Delivery': [
                'CONT', 'DELI', 'NETW', 'CACH', 'STOR', 'DIST', 'STRE', 'MEDI', 'VIDE', 'AUDI',
                'IMAG', 'TEXT', 'DATA', 'FILE', 'DOCU', 'ASSE', 'RESO', 'MATE', 'ITEM', 'OBJE'
            ],
            'Cybersecurity': [
                'CRWD', 'ZS', 'PANW', 'FTNT', 'CHKP', 'OKTA', 'CYBR', 'SPLK', 'FIRE', 'PFPT',
                'QLYS', 'FEYE', 'VRNS', 'TENB', 'RPD', 'SAIL', 'SCWX', 'PING', 'MIME', 'NLOK'
            ]
        }
    }

    # --- Science & Technology ETFs ---
    TECH_ETFS = {
        'QQQ': 'Invesco QQQ Trust (NASDAQ-100)',
        'XLK': 'Technology Select Sector SPDR',
        'VGT': 'Vanguard Information Technology ETF',
        'ARKK': 'ARK Innovation ETF',
        'ARKQ': 'ARK Autonomous Technology & Robotics ETF',
        'ARKW': 'ARK Next Generation Internet ETF',
        'ARKG': 'ARK Genomic Revolution ETF',
        'ARKX': 'ARK Space Exploration ETF',
        'UFO': 'Procure Space ETF',
        'ROKT': 'SPDR S&P Kensho Final Frontiers ETF',
        'QTUM': 'Defiance Quantum ETF',
        'CLOU': 'Global X Cloud Computing ETF',
        'HACK': 'ETFMG Prime Cyber Security ETF',
        'BOTZ': 'Global X Robotics & Artificial Intelligence ETF'
    }

    # --- Research Institutions & Universities ---
    RESEARCH_INSTITUTIONS = {
        'USA': ['MIT', 'Stanford', 'Caltech', 'Harvard', 'Princeton', 'UC_Berkeley', 'Carnegie_Mellon'],
        'Europe': ['Oxford', 'Cambridge', 'ETH_Zurich', 'Imperial_College', 'CERN', 'Max_Planck'],
        'Asia': ['University_of_Tokyo', 'Tsinghua', 'NUS', 'KAIST', 'IIT', 'Technion'],
        'Government': ['NASA', 'ESA', 'JAXA', 'ISRO', 'CNSA', 'Roscosmos']
    }

    # --- Breakthrough Technologies ---
    BREAKTHROUGH_AREAS = {
        'Artificial_Intelligence': ['AGI', 'Neural_Networks', 'Computer_Vision', 'NLP', 'Robotics'],
        'Quantum_Technologies': ['Quantum_Computing', 'Quantum_Communication', 'Quantum_Sensing'],
        'Space_Exploration': ['Mars_Missions', 'Lunar_Base', 'Space_Tourism', 'Asteroid_Mining'],
        'Biotechnology': ['Gene_Therapy', 'CRISPR', 'Synthetic_Biology', 'Longevity_Research'],
        'Energy': ['Fusion_Power', 'Advanced_Solar', 'Hydrogen_Economy', 'Grid_Storage'],
        'Materials': ['Graphene', 'Metamaterials', 'Superconductors', 'Smart_Materials'],
        'Computing': ['Neuromorphic_Chips', 'DNA_Storage', 'Optical_Computing', 'Edge_AI']
    }

    # --- Science & Technology News Sources ---
    SCIENCE_NEWS_FEEDS = [
        # Primary source - The Globe and Mail (as preferred)
        'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/',
        'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/technology/',
        
        # Science and technology news
        'https://feeds.reuters.com/reuters/technologyNews',
        'https://www.cnbc.com/id/19854910/device/rss/rss.html',  # Technology
        'https://feeds.bloomberg.com/technology/news.rss',
        
        # Specialized science news
        'https://www.nature.com/nature.rss',
        'https://www.sciencemag.org/rss/news_current.xml',
        'https://www.newscientist.com/feed/home/',
        'https://www.scientificamerican.com/rss/news/',
        'https://phys.org/rss-feed/',
        
        # Technology industry news
        'https://techcrunch.com/feed/',
        'https://www.theverge.com/rss/index.xml',
        'https://arstechnica.com/feed/',
        'https://www.wired.com/feed/',
        'https://www.technologyreview.com/feed/',
        
        # Space news
        'https://www.space.com/feeds/all',
        'https://spacenews.com/feed/',
        'https://spaceflightnow.com/feed/',
        'https://www.nasa.gov/rss/dyn/breaking_news.rss',
        
        # AI and computing
        'https://www.artificialintelligence-news.com/feed/',
        'https://venturebeat.com/ai/feed/',
        'https://www.nextbigfuture.com/feed'
    ]

    # --- Cache Configuration ---
    CACHE_EXPIRATION_SECONDS = {
        "tech_stocks": 60,           # 1 minute
        "science_news": 300,         # 5 minutes
        "research_data": 3600,       # 1 hour
        "breakthrough_data": 1800,   # 30 minutes
        "space_missions": 3600,      # 1 hour
        "patent_data": 3600          # 1 hour
    }

    # --- Science-themed UI Configuration ---
    THEME = 'arc'  # Modern theme
    FONT_FAMILY = "Segoe UI"
    FONT_SIZE_NORMAL = 10
    FONT_SIZE_LARGE = 12
    FONT_SIZE_HEADER = 14
    
    # Science-themed Color Palette
    COLORS = {
        'primary': '#4169E1',        # Royal Blue (Science)
        'secondary': '#8A2BE2',      # Blue Violet
        'accent': '#00CED1',         # Dark Turquoise
        'success': '#32CD32',        # Lime Green
        'danger': '#FF4500',         # Orange Red
        'warning': '#FFD700',        # Gold
        'info': '#1E90FF',           # Dodger Blue
        'ai': '#9370DB',             # Medium Purple
        'space': '#191970',          # Midnight Blue
        'quantum': '#8A2BE2',        # Blue Violet
        'biotech': '#228B22',        # Forest Green
        'energy': '#FF8C00',         # Dark Orange
        'surface': '#FFFFFF',        # White
        'background': '#F0F8FF',     # Alice Blue (science tint)
        'text_primary': '#212121',   # Dark Gray
        'text_secondary': '#757575'  # Medium Gray
    }
    
    UI_PADDING = 8

# Setup Logging
logging.basicConfig(
    level=ScienceConfig.LOG_LEVEL,
    format=ScienceConfig.LOG_FORMAT,
    handlers=[
        logging.FileHandler(ScienceConfig.LOG_FILE, mode='w'),
        logging.StreamHandler()
    ]
)
logging.info("TiT Science App 1.0.1 Starting...")
logging.info("Copyright (C) 2025 Nguyen Le Vinh Quang. All rights reserved.")

# ==============================================================================
# SECTION 3: CORE SERVICES
# ==============================================================================
class ScienceCacheService:
    """Enhanced cache service for science market data"""
    def __init__(self):
        self._cache = {}
        logging.info("ScienceCacheService initialized.")

    def get(self, key):
        if key not in self._cache:
            return None
        data, timestamp = self._cache[key]
        cache_duration = ScienceConfig.CACHE_EXPIRATION_SECONDS.get(key, 60)
        if time.time() - timestamp < cache_duration:
            logging.info(f"Cache hit for key: {key}")
            return data
        else:
            logging.info(f"Cache expired for key: {key}")
            del self._cache[key]
            return None

    def set(self, key, data):
        logging.info(f"Caching data for key: {key}")
        self._cache[key] = (data, time.time())

class ScienceDataService:
    """Advanced science and technology data service"""
    def __init__(self, cache_service):
        self.cache = cache_service
        logging.info("ScienceDataService initialized with comprehensive technology coverage.")

    def get_tech_stocks_data(self, sector=None):
        """Get data for technology companies by sector."""
        cache_key = f"tech_stocks_{sector}" if sector else "tech_stocks"
        cached_data = self.cache.get(cache_key)
        if cached_data: return cached_data
        
        logging.info(f"Fetching tech stocks data for {sector or 'all sectors'}...")
        tech_data = {}
        
        try:
            sectors_to_fetch = [sector] if sector else ScienceConfig.TECH_COMPANIES.keys()
            
            for sector_name in sectors_to_fetch:
                if sector_name not in ScienceConfig.TECH_COMPANIES:
                    continue
                    
                sector_companies = {}
                
                # Handle nested structure for sectors
                for category, symbols in ScienceConfig.TECH_COMPANIES[sector_name].items():
                    for symbol in symbols:
                        try:
                            ticker = yf.Ticker(symbol)
                            info = ticker.info
                            hist = ticker.history(period="1d")
                            
                            if not hist.empty:
                                current_price = hist['Close'].iloc[-1]
                                prev_close = info.get('previousClose', current_price)
                                change = current_price - prev_close
                                change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                                
                                sector_companies[symbol] = {
                                    'name': info.get('longName', symbol),
                                    'symbol': symbol,
                                    'category': category,
                                    'price': current_price,
                                    'change': change,
                                    'change_percent': change_pct,
                                    'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                                    'market_cap': info.get('marketCap', 0),
                                    'sector': info.get('sector', 'Technology'),
                                    'industry': info.get('industry', 'Technology')
                                }
                        except Exception as e:
                            logging.warning(f"Error fetching data for {symbol}: {e}")
                            continue
                
                if sector_companies:
                    tech_data[sector_name] = sector_companies
            
            self.cache.set(cache_key, tech_data)
            return tech_data
            
        except Exception as e:
            logging.error(f"Error fetching tech stocks data: {e}")
            return {}

    def get_tech_etfs_data(self):
        """Get data for technology sector ETFs."""
        cached_data = self.cache.get("tech_etfs")
        if cached_data: return cached_data
        
        logging.info("Fetching technology sector ETFs data...")
        etfs_data = {}
        
        try:
            for symbol, name in ScienceConfig.TECH_ETFS.items():
                try:
                    ticker = yf.Ticker(symbol)
                    info = ticker.info
                    hist = ticker.history(period="1d")
                    
                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]
                        prev_close = info.get('previousClose', current_price)
                        change = current_price - prev_close
                        change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                        
                        etfs_data[symbol] = {
                            'name': name,
                            'symbol': symbol,
                            'price': current_price,
                            'change': change,
                            'change_percent': change_pct,
                            'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                            'assets_under_mgmt': info.get('totalAssets', 0),
                            'expense_ratio': info.get('annualReportExpenseRatio', 0)
                        }
                except Exception as e:
                    logging.warning(f"Error fetching data for {symbol}: {e}")
                    continue
            
            self.cache.set("tech_etfs", etfs_data)
            return etfs_data
            
        except Exception as e:
            logging.error(f"Error fetching tech ETFs data: {e}")
            return {}

    def get_breakthrough_data(self):
        """Get breakthrough technology and research data."""
        cached_data = self.cache.get("breakthrough_data")
        if cached_data: return cached_data
        
        logging.info("Fetching breakthrough technology data...")
        
        # For now, return static data - in production this would fetch from research databases
        breakthrough_data = ScienceConfig.BREAKTHROUGH_AREAS.copy()
        breakthrough_data['last_updated'] = datetime.now().isoformat()
        
        self.cache.set("breakthrough_data", breakthrough_data)
        return breakthrough_data

# ==============================================================================
# SECTION 4: AI SERVICE FOR SCIENCE ANALYSIS
# ==============================================================================
class ScienceAIService:
    """AI service for science and technology analysis"""
    def __init__(self):
        if ScienceConfig.GOOGLE_API_KEY:
            try:
                genai.configure(api_key=ScienceConfig.GOOGLE_API_KEY)
                self.model = genai.GenerativeModel('gemini-pro')
                logging.info("ScienceAIService initialized with Gemini Pro.")
            except Exception as e:
                logging.error(f"Failed to initialize AI service: {e}")
                self.model = None
        else:
            self.model = None
            logging.warning("No AI API key provided. AI features disabled.")

    def generate_science_analysis(self, tech_data, etfs_data, breakthrough_data, news_data):
        """Generate comprehensive science and technology analysis."""
        if not self.model:
            return "AI analysis unavailable. Please configure API key."

        try:
            tech_summary = self._prepare_tech_summary(tech_data)
            etfs_summary = self._prepare_etfs_summary(etfs_data)
            breakthrough_summary = self._prepare_breakthrough_summary(breakthrough_data)
            news_summary = self._prepare_news_summary(news_data)

            prompt = f"""
            As an expert science and technology analyst, provide comprehensive analysis of the current technology landscape.

            TECHNOLOGY COMPANIES PERFORMANCE:
            {tech_summary}

            TECHNOLOGY ETFS:
            {etfs_summary}

            BREAKTHROUGH TECHNOLOGIES:
            {breakthrough_summary}

            RECENT NEWS HIGHLIGHTS:
            {news_summary}

            Please provide detailed analysis covering:

            ## 🚀 SCIENCE & TECHNOLOGY OVERVIEW
            **Current Innovation Climate**: [Accelerating/Stable/Slowing] with confidence level

            **Key Innovation Drivers**:
            - AI and machine learning breakthroughs
            - Quantum computing developments
            - Space exploration milestones
            - Biotechnology advances
            - Clean energy innovations
            - Semiconductor technology progress

            ## 🤖 ARTIFICIAL INTELLIGENCE SECTOR
            **AI Market Dynamics**:
            - Large language model competition
            - AI chip demand and supply
            - Enterprise AI adoption rates
            - Regulatory developments and ethics

            **Investment Opportunities**:
            - AI infrastructure companies
            - AI application developers
            - Data and cloud providers
            - Semiconductor manufacturers

            ## 🌌 SPACE TECHNOLOGY ANALYSIS
            **Space Economy Growth**:
            - Commercial space launches
            - Satellite constellation deployments
            - Space tourism developments
            - Mars exploration progress

            **Investment Themes**:
            - Launch services providers
            - Satellite technology companies
            - Space manufacturing
            - Asteroid mining potential

            ## ⚛️ QUANTUM COMPUTING PROGRESS
            **Quantum Milestones**:
            - Quantum supremacy achievements
            - Error correction improvements
            - Commercial applications development
            - Government investments and initiatives

            **Market Implications**:
            - Cybersecurity transformation
            - Drug discovery acceleration
            - Financial modeling revolution
            - Optimization problem solving

            ## 🧬 BIOTECHNOLOGY INNOVATIONS
            **Gene Editing Advances**:
            - CRISPR therapeutic approvals
            - Base editing developments
            - Prime editing applications
            - Epigenome editing progress

            **Synthetic Biology**:
            - Engineered organism applications
            - Biomanufacturing scaling
            - Agricultural biotechnology
            - Environmental applications

            ## ⚡ CLEAN ENERGY TECHNOLOGIES
            **Renewable Energy Progress**:
            - Solar efficiency improvements
            - Wind technology advances
            - Energy storage breakthroughs
            - Grid modernization

            **Fusion Energy Development**:
            - Fusion milestone achievements
            - Private fusion companies
            - Government fusion programs
            - Commercial timeline projections

            ## 💻 SEMICONDUCTOR EVOLUTION
            **Chip Technology Trends**:
            - Advanced node development
            - Specialized AI chips
            - Quantum processors
            - Neuromorphic computing

            **Supply Chain Dynamics**:
            - Geopolitical manufacturing shifts
            - Capacity expansion plans
            - Material innovation needs
            - Packaging technology advances

            ## 📊 INVESTMENT STRATEGIES
            **Technology Sector Allocation**:
            - Growth vs value opportunities
            - Large cap stability vs small cap innovation
            - Geographic diversification
            - Thematic investing approaches

            **Risk Considerations**:
            - Regulatory and ethical challenges
            - Technology disruption cycles
            - Geopolitical technology competition
            - Sustainability and ESG factors

            ## 🔮 FUTURE OUTLOOK
            **Short-term (6-12 months)**:
            - AI model improvements and applications
            - Space mission launches and achievements
            - Quantum computing milestones
            - Biotech clinical trial results

            **Long-term (2-5 years)**:
            - AGI development timeline
            - Mars colonization progress
            - Quantum advantage applications
            - Longevity breakthrough potential

            **Transformative Technologies**:
            - Brain-computer interfaces
            - Nanotechnology applications
            - Advanced materials development
            - Fusion energy commercialization

            Provide specific, actionable insights with confidence levels and investment implications.
            Include breakthrough probability assessments and timeline predictions.
            """

            response = self.model.generate_content(prompt)
            return response.text

        except Exception as e:
            logging.error(f"Error generating science analysis: {e}")
            return f"Error generating analysis: {e}"

    def _prepare_tech_summary(self, tech_data):
        """Prepare technology companies data summary for AI analysis."""
        if not tech_data:
            return "No technology companies data available."

        summary = []
        for sector, companies in tech_data.items():
            summary.append(f"\n{sector}:")
            if isinstance(companies, dict):
                for symbol, data in list(companies.items())[:3]:
                    if isinstance(data, dict):
                        change_direction = "↑" if data['change'] >= 0 else "↓"
                        summary.append(f"- {data['name']}: ${data['price']:.2f} {change_direction} {data['change_percent']:.2f}%")

        return "\n".join(summary)

    def _prepare_etfs_summary(self, etfs_data):
        """Prepare technology ETFs data summary for AI analysis."""
        if not etfs_data:
            return "No technology ETFs data available."

        summary = []
        for symbol, data in etfs_data.items():
            change_direction = "↑" if data['change'] >= 0 else "↓"
            summary.append(f"- {data['name']}: ${data['price']:.2f} {change_direction} {data['change_percent']:.2f}%")

        return "\n".join(summary)

    def _prepare_breakthrough_summary(self, breakthrough_data):
        """Prepare breakthrough technology summary for AI analysis."""
        if not breakthrough_data:
            return "No breakthrough technology data available."

        summary = []
        for area, technologies in breakthrough_data.items():
            if area != 'last_updated' and isinstance(technologies, list):
                summary.append(f"\n{area}: {', '.join(technologies)}")

        return "\n".join(summary)

    def _prepare_news_summary(self, news_data):
        """Prepare news data summary for AI analysis."""
        if not news_data:
            return "No recent news available."

        summary = []
        for article in news_data[:10]:
            impact_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(article.get('impact_level', 'medium'), "🟡")
            summary.append(f"{impact_emoji} {article['title']} ({article['source']['name']})")

        return "\n".join(summary)

# ==============================================================================
# SECTION 5: MAIN SCIENCE APPLICATION
# ==============================================================================
class TiTScienceApp:
    """Main Science Application"""
    def __init__(self, root):
        self.root = root
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        self.root.title("TiT Science App 1.0.1 - Advanced Science, Technology & Space Intelligence Suite")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        logging.info("TiT Science App main initialization started...")

        # Initialize services
        self.cache_service = ScienceCacheService()
        self.data_service = ScienceDataService(self.cache_service)
        self.ai_service = ScienceAIService()

        # App state
        self.app_state = {
            'tech_stocks': {},
            'tech_etfs': {},
            'breakthrough_data': {},
            'science_news': [],
            'analysis_text': ""
        }

        # UI components
        self.widgets = {}
        self.status_var = tk.StringVar(value="Ready - TiT Science App 1.0.1")

        # Setup UI
        self.setup_ui()

        # Initial data load
        self.refresh_all_data()

        logging.info("TiT Science App 1.0.1 initialized successfully.")

    def _on_closing(self):
        """Handle application closing."""
        logging.info("Science application closing...")
        self.root.destroy()

    def setup_ui(self):
        """Setup the main UI components."""
        # Main style configuration
        self.style = ttk.Style()
        self.style.configure("TLabel", font=(ScienceConfig.FONT_FAMILY, ScienceConfig.FONT_SIZE_NORMAL))
        self.style.configure("TButton", font=(ScienceConfig.FONT_FAMILY, ScienceConfig.FONT_SIZE_NORMAL))

        # Main container
        self.main_frame = ttk.Frame(self.root, padding=ScienceConfig.UI_PADDING)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Top control bar
        self.control_frame = ttk.Frame(self.main_frame)
        self.control_frame.pack(fill=tk.X, pady=(0, 10))

        # Refresh button
        self.refresh_btn = ttk.Button(
            self.control_frame,
            text="Refresh All",
            command=self.refresh_all_data
        )
        self.refresh_btn.pack(side=tk.LEFT, padx=5)

        # Author box
        author_frame = ttk.LabelFrame(self.control_frame, text="")
        author_frame.pack(side=tk.LEFT, padx=(20, 5))

        author_label = ttk.Label(
            author_frame,
            text="Anh Quang",
            font=(ScienceConfig.FONT_FAMILY, ScienceConfig.FONT_SIZE_NORMAL, 'bold'),
            foreground=ScienceConfig.COLORS['primary']
        )
        author_label.pack(padx=10, pady=2)

        # Status bar
        self.status_bar = ttk.Label(self.main_frame, textvariable=self.status_var, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))

        # Tab control
        self.tab_control = ttk.Notebook(self.main_frame)
        self.tab_control.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self.dashboard_tab = ttk.Frame(self.tab_control)
        self.companies_tab = ttk.Frame(self.tab_control)
        self.breakthroughs_tab = ttk.Frame(self.tab_control)
        self.news_tab = ttk.Frame(self.tab_control)
        self.analysis_tab = ttk.Frame(self.tab_control)

        # Add tabs to notebook
        self.tab_control.add(self.dashboard_tab, text="Science Dashboard")
        self.tab_control.add(self.companies_tab, text="Tech Companies")
        self.tab_control.add(self.breakthroughs_tab, text="Breakthroughs")
        self.tab_control.add(self.news_tab, text="Science News")
        self.tab_control.add(self.analysis_tab, text="AI Analysis")

        # Setup individual tab contents
        self.setup_dashboard_tab()
        self.setup_companies_tab()
        self.setup_breakthroughs_tab()
        self.setup_news_tab()
        self.setup_analysis_tab()

        logging.info("Science app UI setup complete")

    def setup_dashboard_tab(self):
        """Setup the science dashboard tab."""
        # Science overview section
        overview_frame = ttk.LabelFrame(self.dashboard_tab, text="Science & Technology Overview")
        overview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for overview
        columns = ("Sector", "Companies", "Avg Change", "Top Performer", "Performance")
        self.overview_tree = ttk.Treeview(overview_frame, columns=columns, show="headings", height=8)

        # Configure columns
        for col in columns:
            self.overview_tree.heading(col, text=col)
            self.overview_tree.column(col, width=120)

        # Add scrollbar
        overview_scrollbar = ttk.Scrollbar(overview_frame, orient="vertical", command=self.overview_tree.yview)
        self.overview_tree.configure(yscrollcommand=overview_scrollbar.set)

        # Pack widgets
        self.overview_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        overview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Store widget reference
        self.widgets['overview_tree'] = self.overview_tree

    def setup_companies_tab(self):
        """Setup the tech companies tab."""
        # Companies section
        companies_frame = ttk.LabelFrame(self.companies_tab, text="Technology Companies")
        companies_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for companies
        columns = ("Company", "Symbol", "Sector", "Category", "Price", "Change", "Change %", "Market Cap")
        self.companies_tree = ttk.Treeview(companies_frame, columns=columns, show="headings", height=15)

        # Configure columns
        for col in columns:
            self.companies_tree.heading(col, text=col)
            self.companies_tree.column(col, width=100)

        # Add scrollbar
        companies_scrollbar = ttk.Scrollbar(companies_frame, orient="vertical", command=self.companies_tree.yview)
        self.companies_tree.configure(yscrollcommand=companies_scrollbar.set)

        # Pack widgets
        self.companies_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        companies_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Store widget reference
        self.widgets['companies_tree'] = self.companies_tree

    def setup_breakthroughs_tab(self):
        """Setup the breakthroughs tab."""
        # Breakthroughs section
        breakthroughs_frame = ttk.LabelFrame(self.breakthroughs_tab, text="Breakthrough Technologies")
        breakthroughs_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrolled text widget for breakthroughs
        self.breakthroughs_text = scrolledtext.ScrolledText(breakthroughs_frame, wrap=tk.WORD, height=20)
        self.breakthroughs_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Configure text tags
        self.breakthroughs_text.tag_configure('title', font=(ScienceConfig.FONT_FAMILY, ScienceConfig.FONT_SIZE_LARGE, 'bold'))
        self.breakthroughs_text.tag_configure('ai', foreground=ScienceConfig.COLORS['ai'])
        self.breakthroughs_text.tag_configure('space', foreground=ScienceConfig.COLORS['space'])
        self.breakthroughs_text.tag_configure('quantum', foreground=ScienceConfig.COLORS['quantum'])
        self.breakthroughs_text.tag_configure('biotech', foreground=ScienceConfig.COLORS['biotech'])

        # Store widget reference
        self.widgets['breakthroughs_text'] = self.breakthroughs_text

    def setup_news_tab(self):
        """Setup the science news tab."""
        # News section
        news_frame = ttk.LabelFrame(self.news_tab, text="Science & Technology News")
        news_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrolled text widget for news
        self.news_text = scrolledtext.ScrolledText(news_frame, wrap=tk.WORD, height=20)
        self.news_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Configure text tags
        self.news_text.tag_configure('title', font=(ScienceConfig.FONT_FAMILY, ScienceConfig.FONT_SIZE_LARGE, 'bold'))
        self.news_text.tag_configure('source', font=(ScienceConfig.FONT_FAMILY, ScienceConfig.FONT_SIZE_NORMAL - 1), foreground='gray')
        self.news_text.tag_configure('high_impact', foreground=ScienceConfig.COLORS['danger'])
        self.news_text.tag_configure('medium_impact', foreground=ScienceConfig.COLORS['warning'])
        self.news_text.tag_configure('low_impact', foreground=ScienceConfig.COLORS['success'])

        # Store widget reference
        self.widgets['news_text'] = self.news_text

    def setup_analysis_tab(self):
        """Setup the AI analysis tab."""
        # Control frame
        control_frame = ttk.Frame(self.analysis_tab)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # Generate analysis button
        generate_btn = ttk.Button(
            control_frame,
            text="🚀 Generate Science Analysis",
            command=self.generate_science_analysis_threaded
        )
        generate_btn.pack(side=tk.LEFT, padx=5)

        # Export button
        export_btn = ttk.Button(
            control_frame,
            text="📊 Export Analysis",
            command=self.export_analysis
        )
        export_btn.pack(side=tk.LEFT, padx=5)

        # Analysis text area
        analysis_frame = ttk.LabelFrame(self.analysis_tab, text="AI Science & Technology Analysis")
        analysis_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, wrap=tk.WORD, height=20)
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Store widget reference
        self.widgets['analysis_text'] = self.analysis_text

    def refresh_all_data(self):
        """Refresh all science market data."""
        def refresh_worker():
            try:
                self.status_var.set("Refreshing science market data...")

                # Fetch tech stocks data
                tech_stocks = self.data_service.get_tech_stocks_data()
                self.app_state['tech_stocks'] = tech_stocks

                # Fetch tech ETFs data
                tech_etfs = self.data_service.get_tech_etfs_data()
                self.app_state['tech_etfs'] = tech_etfs

                # Fetch breakthrough data
                breakthrough_data = self.data_service.get_breakthrough_data()
                self.app_state['breakthrough_data'] = breakthrough_data

                # Update UI
                self.root.after(0, self.update_all_displays)

                self.status_var.set("Science market data refreshed successfully.")

            except Exception as e:
                logging.error(f"Error refreshing science data: {e}")
                self.status_var.set(f"Error refreshing data: {e}")

        threading.Thread(target=refresh_worker, daemon=True).start()

    def update_all_displays(self):
        """Update all UI displays."""
        self.update_overview_display()
        self.update_companies_display()
        self.update_breakthroughs_display()

    def update_overview_display(self):
        """Update the overview display."""
        tree = self.widgets.get('overview_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add overview data
        for sector, companies in self.app_state['tech_stocks'].items():
            if isinstance(companies, dict) and companies:
                changes = [data['change_percent'] for data in companies.values() if isinstance(data, dict)]
                avg_change = sum(changes) / len(changes) if changes else 0

                # Find top performer
                top_performer = max(companies.items(), key=lambda x: x[1].get('change_percent', 0) if isinstance(x[1], dict) else 0)

                tree.insert('', 'end', values=(
                    sector.replace('_', ' '),
                    len(companies),
                    f"{avg_change:.2f}%",
                    top_performer[1].get('name', top_performer[0]) if isinstance(top_performer[1], dict) else top_performer[0],
                    f"{top_performer[1].get('change_percent', 0):.2f}%" if isinstance(top_performer[1], dict) else "N/A"
                ))

    def update_companies_display(self):
        """Update the companies display."""
        tree = self.widgets.get('companies_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add companies data
        for sector, companies in self.app_state['tech_stocks'].items():
            if isinstance(companies, dict):
                for symbol, data in companies.items():
                    if isinstance(data, dict):
                        change_color = 'green' if data['change'] >= 0 else 'red'
                        market_cap_str = f"${data['market_cap']/1e9:.1f}B" if data['market_cap'] > 0 else "N/A"

                        tree.insert('', 'end', values=(
                            data['name'][:25] + "..." if len(data['name']) > 25 else data['name'],
                            data['symbol'],
                            sector.replace('_', ' '),
                            data.get('category', 'Technology'),
                            f"${data['price']:.2f}",
                            f"${data['change']:.2f}",
                            f"{data['change_percent']:.2f}%",
                            market_cap_str
                        ), tags=(change_color,))

        # Configure tags for colors
        tree.tag_configure('green', foreground=ScienceConfig.COLORS['success'])
        tree.tag_configure('red', foreground=ScienceConfig.COLORS['danger'])

    def update_breakthroughs_display(self):
        """Update the breakthroughs display."""
        breakthroughs_text = self.widgets.get('breakthroughs_text')
        if not breakthroughs_text:
            return

        breakthroughs_text.delete(1.0, tk.END)

        breakthroughs_text.insert(tk.END, "BREAKTHROUGH TECHNOLOGIES & INNOVATIONS\n", 'title')
        breakthroughs_text.insert(tk.END, "="*60 + "\n\n")

        for area, technologies in self.app_state['breakthrough_data'].items():
            if area != 'last_updated' and isinstance(technologies, list):
                # Determine tag based on area
                tag = 'title'
                if 'AI' in area or 'Machine' in area:
                    tag = 'ai'
                elif 'Space' in area:
                    tag = 'space'
                elif 'Quantum' in area:
                    tag = 'quantum'
                elif 'Bio' in area:
                    tag = 'biotech'

                breakthroughs_text.insert(tk.END, f"{area.replace('_', ' ').upper()}\n", tag)
                for tech in technologies:
                    breakthroughs_text.insert(tk.END, f"  • {tech.replace('_', ' ')}\n")
                breakthroughs_text.insert(tk.END, "\n")

    def generate_science_analysis_threaded(self):
        """Generate science analysis in a separate thread."""
        def analysis_worker():
            try:
                self.status_var.set("AI is analyzing science market data...")

                analysis = self.ai_service.generate_science_analysis(
                    self.app_state['tech_stocks'],
                    self.app_state['tech_etfs'],
                    self.app_state['breakthrough_data'],
                    self.app_state['science_news']
                )

                self.app_state['analysis_text'] = analysis
                self.root.after(0, self.update_analysis_display)
                self.status_var.set("Science analysis generated successfully.")

            except Exception as e:
                logging.error(f"Error generating science analysis: {e}")
                self.status_var.set(f"Error generating analysis: {e}")

        threading.Thread(target=analysis_worker, daemon=True).start()

    def update_analysis_display(self):
        """Update the analysis display."""
        analysis_text = self.widgets.get('analysis_text')
        if analysis_text and self.app_state['analysis_text']:
            analysis_text.delete(1.0, tk.END)
            analysis_text.insert(tk.END, self.app_state['analysis_text'])

    def export_analysis(self):
        """Export the science analysis to a file."""
        try:
            if not self.app_state['analysis_text']:
                messagebox.showinfo("No Analysis", "Please generate an analysis first.")
                return

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"TiT_Science_Analysis_{timestamp}.txt"

            filepath = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="Export Science Analysis",
                initialfilename=filename
            )

            if filepath:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(self.app_state['analysis_text'])
                messagebox.showinfo("Export Successful", f"Analysis exported to:\n{filepath}")
                self.status_var.set(f"Analysis exported to {filepath}")

        except Exception as e:
            logging.error(f"Error exporting analysis: {e}")
            messagebox.showerror("Export Error", f"Failed to export analysis: {e}")

# ==============================================================================
# SECTION 6: MAIN EXECUTION
# ==============================================================================
if __name__ == "__main__":
    try:
        root = ThemedTk(theme=ScienceConfig.THEME)
        app = TiTScienceApp(root)
        root.mainloop()
    except Exception as e:
        logging.critical(f"A critical, unhandled error occurred: {e}", exc_info=True)
        messagebox.showerror("Fatal Application Error", f"A fatal error occurred and the application must close.\n\nDetails: {e}")

# End of TiT Science App 1.0.1
