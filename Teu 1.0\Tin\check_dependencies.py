#!/usr/bin/env python3
"""
Check for missing dependencies in Tin apps
"""

import sys

# List of required modules for Tin apps
required_modules = [
    'tkinter',
    'ttkthemes', 
    'pandas',
    'numpy',
    'requests',
    'yfinance',
    'feedparser',
    'bs4',
    'matplotlib',
    'mplfinance',
    'google.generativeai',
    'pycoingecko',
    'tradingview_ta',
    'investpy'
]

missing_modules = []
available_modules = []

print("🔍 Checking Tin Apps Dependencies...")
print("=" * 50)

for module in required_modules:
    try:
        __import__(module)
        available_modules.append(module)
        print(f"✅ {module}")
    except ImportError as e:
        missing_modules.append(module)
        print(f"❌ {module} - {e}")

print("\n" + "=" * 50)
print(f"📊 SUMMARY:")
print(f"✅ Available: {len(available_modules)}")
print(f"❌ Missing: {len(missing_modules)}")

if missing_modules:
    print(f"\n🚨 MISSING DEPENDENCIES:")
    for module in missing_modules:
        print(f"   - {module}")
    
    print(f"\n💡 To install missing dependencies, run:")
    print(f"pip install {' '.join(missing_modules)}")
    sys.exit(1)
else:
    print(f"\n🎉 All dependencies are available!")
    sys.exit(0)
