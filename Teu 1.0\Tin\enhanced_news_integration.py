# Enhanced News Integration Engine - EXPAND and CONNECT approach
# Created to provide advanced news aggregation and analysis
# NO MOCK DATA - Real enhanced news processing

import requests
import feedparser
import logging
from datetime import datetime, timedelta
import threading
import time
from bs4 import BeautifulSoup
import re

class EnhancedNewsIntegrationEngine:
    """
    Enhanced news integration engine for comprehensive news coverage
    EXPAND and CONNECT philosophy - never remove, only add more news sources!
    """
    
    def __init__(self):
        self.news_cache = {}
        self.processing_active = True
        self.news_sources = {
            'financial': [
                'https://feeds.reuters.com/reuters/businessNews',
                'https://feeds.bloomberg.com/markets/news.rss',
                'https://www.cnbc.com/id/100003114/device/rss/rss.html',
                'https://feeds.finance.yahoo.com/rss/2.0/headline'
            ],
            'crypto': [
                'https://cointelegraph.com/rss',
                'https://decrypt.co/feed',
                'https://www.coindesk.com/arc/outboundfeeds/rss/'
            ],
            'technology': [
                'https://feeds.feedburner.com/TechCrunch',
                'https://www.wired.com/feed/rss',
                'https://feeds.arstechnica.com/arstechnica/index'
            ],
            'global': [
                'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/',
                'https://www.ft.com/rss/home/<USER>',
                'https://www.ft.com/rss/home/<USER>'
            ]
        }
        logging.info("🚀 EnhancedNewsIntegrationEngine initialized - EXPANDING news coverage!")
    
    def enhanced_news(self, category='all', limit=50):
        """
        Get enhanced news with advanced filtering and processing
        EXPAND approach - add more news sources and processing!
        """
        try:
            cache_key = f"enhanced_news_{category}_{limit}"
            
            # Check cache first
            if cache_key in self.news_cache:
                cached_data, timestamp = self.news_cache[cache_key]
                if time.time() - timestamp < 300:  # 5 minutes cache
                    logging.info(f"🚀 Enhanced news cache hit for {category}")
                    return cached_data
            
            all_articles = []
            
            # Determine which sources to use
            if category == 'all':
                sources_to_fetch = []
                for source_list in self.news_sources.values():
                    sources_to_fetch.extend(source_list)
            elif category in self.news_sources:
                sources_to_fetch = self.news_sources[category]
            else:
                sources_to_fetch = self.news_sources['financial']  # Default fallback
            
            # Fetch from multiple sources
            for feed_url in sources_to_fetch[:10]:  # Limit to prevent overload
                try:
                    articles = self._fetch_feed_articles(feed_url, limit // len(sources_to_fetch[:10]))
                    all_articles.extend(articles)
                except Exception as e:
                    logging.warning(f"Failed to fetch from {feed_url}: {e}")
                    continue
            
            # Enhanced processing
            processed_articles = self._process_articles(all_articles)
            
            # Sort by relevance and recency
            sorted_articles = sorted(processed_articles, 
                                   key=lambda x: (x.get('relevance_score', 0), x.get('timestamp', 0)), 
                                   reverse=True)
            
            # Limit results
            final_articles = sorted_articles[:limit]
            
            # Cache results
            self.news_cache[cache_key] = (final_articles, time.time())
            
            logging.info(f"🚀 Enhanced news fetched: {len(final_articles)} articles for {category}")
            return final_articles
            
        except Exception as e:
            logging.error(f"Enhanced news integration error: {e}")
            return self._fallback_news()
    
    def _fetch_feed_articles(self, feed_url, limit=10):
        """Fetch articles from a single RSS feed"""
        try:
            feed = feedparser.parse(feed_url)
            articles = []
            
            for entry in feed.entries[:limit]:
                article = {
                    'title': entry.get('title', 'No Title'),
                    'description': entry.get('summary', entry.get('description', 'No Description')),
                    'link': entry.get('link', ''),
                    'published': entry.get('published', ''),
                    'source': feed.feed.get('title', 'Unknown Source'),
                    'timestamp': time.mktime(entry.get('published_parsed', time.gmtime()))
                }
                articles.append(article)
            
            return articles
            
        except Exception as e:
            logging.warning(f"Failed to fetch feed {feed_url}: {e}")
            return []
    
    def _process_articles(self, articles):
        """Process articles with enhanced analysis"""
        processed = []
        
        for article in articles:
            try:
                # Calculate relevance score
                relevance_score = self._calculate_relevance(article)
                
                # Clean and enhance description
                cleaned_description = self._clean_text(article.get('description', ''))
                
                # Extract keywords
                keywords = self._extract_keywords(article.get('title', '') + ' ' + cleaned_description)
                
                # Determine category
                category = self._categorize_article(article)
                
                processed_article = {
                    **article,
                    'relevance_score': relevance_score,
                    'cleaned_description': cleaned_description,
                    'keywords': keywords,
                    'category': category,
                    'processed_timestamp': datetime.now().isoformat()
                }
                
                processed.append(processed_article)
                
            except Exception as e:
                logging.warning(f"Failed to process article: {e}")
                processed.append(article)  # Keep original if processing fails
        
        return processed
    
    def _calculate_relevance(self, article):
        """Calculate relevance score for article"""
        score = 0.5  # Base score
        
        title = article.get('title', '').lower()
        description = article.get('description', '').lower()
        text = f"{title} {description}"
        
        # High-value keywords
        high_value_keywords = ['breaking', 'urgent', 'major', 'significant', 'bitcoin', 'ethereum', 'stock', 'market']
        for keyword in high_value_keywords:
            if keyword in text:
                score += 0.1
        
        # Recency bonus
        timestamp = article.get('timestamp', 0)
        hours_old = (time.time() - timestamp) / 3600
        if hours_old < 1:
            score += 0.3
        elif hours_old < 6:
            score += 0.2
        elif hours_old < 24:
            score += 0.1
        
        return min(1.0, score)
    
    def _clean_text(self, text):
        """Clean HTML and unwanted characters from text"""
        try:
            # Remove HTML tags
            soup = BeautifulSoup(text, 'html.parser')
            cleaned = soup.get_text()
            
            # Remove extra whitespace
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()
            
            # Limit length
            if len(cleaned) > 500:
                cleaned = cleaned[:497] + "..."
            
            return cleaned
            
        except Exception:
            return text[:500]  # Fallback to simple truncation
    
    def _extract_keywords(self, text):
        """Extract relevant keywords from text"""
        try:
            # Simple keyword extraction
            words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
            
            # Filter common words
            stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'will', 'with'}
            
            keywords = [word for word in words if word not in stop_words]
            
            # Return top keywords
            return list(set(keywords))[:10]
            
        except Exception:
            return []
    
    def _categorize_article(self, article):
        """Categorize article based on content"""
        title = article.get('title', '').lower()
        description = article.get('description', '').lower()
        text = f"{title} {description}"
        
        categories = {
            'crypto': ['bitcoin', 'ethereum', 'cryptocurrency', 'blockchain', 'defi'],
            'stocks': ['stock', 'shares', 'nasdaq', 'dow', 'market'],
            'technology': ['tech', 'ai', 'software', 'innovation', 'digital'],
            'economy': ['economy', 'gdp', 'inflation', 'federal', 'bank']
        }
        
        for category, keywords in categories.items():
            if any(keyword in text for keyword in keywords):
                return category
        
        return 'general'
    
    def _fallback_news(self):
        """Fallback news when enhanced processing fails"""
        return [
            {
                'title': 'Enhanced News Service Temporarily Unavailable',
                'description': 'The enhanced news integration service is currently unavailable. Please try again later.',
                'link': '',
                'source': 'Enhanced News Engine',
                'category': 'system',
                'timestamp': time.time()
            }
        ]
    
    def get_sector_news(self, sector, limit=20):
        """Get news specific to a sector"""
        try:
            if sector.lower() in ['crypto', 'cryptocurrency']:
                return self.enhanced_news('crypto', limit)
            elif sector.lower() in ['tech', 'technology']:
                return self.enhanced_news('technology', limit)
            else:
                return self.enhanced_news('financial', limit)
        except Exception as e:
            logging.error(f"Sector news error: {e}")
            return self._fallback_news()

# Global instance for immediate use
enhanced_news_engine = EnhancedNewsIntegrationEngine()

def enhanced_news(category='all', limit=50):
    """Quick access function for enhanced news"""
    return enhanced_news_engine.enhanced_news(category, limit)

def get_sector_news(sector, limit=20):
    """Quick access function for sector news"""
    return enhanced_news_engine.get_sector_news(sector, limit)

if __name__ == "__main__":
    print("🚀 Enhanced News Integration Engine - EXPAND and CONNECT approach!")
    print("✅ Ready to provide enhanced news coverage!")
