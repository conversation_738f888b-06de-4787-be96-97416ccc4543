#!/usr/bin/env python3
"""
Script to fix Vietnamese translations in all Tin apps
"""

import os
import re

# List of Tin app files to fix
TIN_APPS = [
    "Tin_Stock_App_1.0.1.py",
    "Tin_Gold_App_1.0.1.py", 
    "Tin_Oil_App_1.0.1.py",
    "Tin_Health_App_1.0.1.py",
    "Tin_Defense_App_1.0.1.py",
    "Tin_Science_App_1.0.1.py"
]

# Common English text patterns to replace with Vietnamese
REPLACEMENTS = {
    # Tab names
    r'text="Dashboard"': 'text=vn("Dashboard")',
    r'text="Portfolio"': 'text=vn("Portfolio")',
    r'text="Charts"': 'text=vn("Charts")',
    r'text="News"': 'text=vn("News")',
    r'text="Analysis"': 'text=vn("Analysis")',
    r'text="Settings"': 'text=vn("Settings")',
    r'text="Tools"': 'text=vn("Tools")',
    r'text="Chat"': 'text=vn("Chat")',
    
    # Common UI elements
    r'text="Refresh All"': 'text=vn("Refresh All")',
    r'text="Export Analysis"': 'text=vn("Export Analysis")',
    r'text="Generate Analysis"': 'text=vn("Generate Analysis")',
    r'text="Search"': 'text=vn("Search")',
    r'text="Filter"': 'text=vn("Filter")',
    r'text="Clear"': 'text=vn("Clear")',
    r'text="Apply"': 'text=vn("Apply")',
    r'text="Loading\.\.\."': 'text=vn("Loading...")',
    r'text="Ready"': 'text=vn("Ready")',
    
    # Frame labels
    r'text="Market Overview"': 'text=vn("Market Overview")',
    r'text="Global Markets"': 'text=vn("Global Markets")',
    r'text="Stock Search"': 'text=vn("Stock Search")',
    r'text="Search Results"': 'text=vn("Search Results")',
    r'text="Market News"': 'text=vn("Market News")',
    r'text="Market Analysis"': 'text=vn("Market Analysis")',
    r'text="News Controls"': 'text=vn("News Controls")',
    r'text="High Impact News"': 'text=vn("High Impact News")',
    r'text="News Details"': 'text=vn("News Details")',
    r'text="Commodities"': 'text=vn("Commodities")',
    r'text="Oil & Gold Prices"': 'text=vn("Oil & Gold Prices")',
    
    # Form labels
    r'text="Select Country:"': 'text=vn("Select Country:")',
    r'text="Country:"': 'text=vn("Country:")',
    r'text="Sector:"': 'text=vn("Sector:")',
    r'text="Category:"': 'text=vn("Category:")',
    r'text="Impact:"': 'text=vn("Impact:")',
    r'text="Source:"': 'text=vn("Source:")',
    r'text="Filter by:"': 'text=vn("Filter by:")',
    r'text="Search:"': 'text=vn("Search:")',
    
    # Button text
    r'text="🔍 Search"': 'text=f"🔍 {vn(\'Search\')}"',
    r'text="🔄 Refresh News"': 'text=f"🔄 {vn(\'Refresh News\')}"',
    r'text="🤖 Generate Market Analysis"': 'text=f"🤖 {vn(\'Generate Market Analysis\')}"',
    r'text="📄 Export Analysis"': 'text=f"📄 {vn(\'Export Analysis\')}"',
    
    # Professional Developer
    r'text="Professional Developer"': 'text=vn("Professional Developer")',
    r'text="👨‍💻 Created By"': 'text=f"👨‍💻 {vn(\'Created By\')}"',
}

def add_translation_import(file_path):
    """Add Vietnamese translation import to a file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if translation import already exists
    if 'from vietnamese_translations import' in content:
        print(f"✅ Translation import already exists in {file_path}")
        return
    
    # Find the import section and add translation import
    import_pattern = r'(import google\.generativeai as genai\n)'
    translation_import = '''
# Vietnamese Translation Module
try:
    from vietnamese_translations import translate_to_vietnamese, vn
    TRANSLATION_AVAILABLE = True
    print("✅ Vietnamese translation module loaded!")
except ImportError:
    TRANSLATION_AVAILABLE = False
    print("⚠️ Vietnamese translation module not found, using English")
    def vn(text): return text
    def translate_to_vietnamese(text): return text
'''
    
    if re.search(import_pattern, content):
        content = re.sub(import_pattern, r'\1' + translation_import, content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Added translation import to {file_path}")
    else:
        print(f"⚠️ Could not find import section in {file_path}")

def fix_translations_in_file(file_path):
    """Fix Vietnamese translations in a single file"""
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    print(f"🔧 Fixing translations in {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    changes_made = 0
    
    # Apply all replacements
    for pattern, replacement in REPLACEMENTS.items():
        matches = re.findall(pattern, content)
        if matches:
            content = re.sub(pattern, replacement, content)
            changes_made += len(matches)
            print(f"  ✅ Replaced {len(matches)} instances of {pattern}")
    
    # Write back if changes were made
    if changes_made > 0:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Made {changes_made} translation fixes in {file_path}")
    else:
        print(f"ℹ️ No translation fixes needed in {file_path}")

def main():
    """Main function to fix all Tin apps"""
    print("🚀 Starting Vietnamese translation fixes for all Tin apps...")
    print("=" * 60)
    
    total_fixes = 0
    
    for app_file in TIN_APPS:
        if os.path.exists(app_file):
            print(f"\n📱 Processing {app_file}...")
            
            # Add translation import
            add_translation_import(app_file)
            
            # Fix translations
            fix_translations_in_file(app_file)
            
        else:
            print(f"⚠️ File not found: {app_file}")
    
    print("\n" + "=" * 60)
    print("🎉 Vietnamese translation fixes completed!")
    print("📝 All Tin apps should now display Vietnamese text properly.")
    print("🔄 You may need to restart the apps to see the changes.")

if __name__ == "__main__":
    main()
