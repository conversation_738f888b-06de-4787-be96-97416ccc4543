2025-06-18 03:09:19,962 - INFO - [MainThread] - TiT Defense App 1.0.1 Starting...
2025-06-18 03:09:19,965 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-18 03:09:23,431 - INFO - [MainThread] - TiT Defense App main initialization started...
2025-06-18 03:09:23,432 - INFO - [MainThread] - DefenseCacheService initialized.
2025-06-18 03:09:23,433 - INFO - [MainThread] - DefenseDataService initialized with comprehensive defense coverage.
2025-06-18 03:09:23,434 - INFO - [MainThread] - DefenseAIService initialized with Gemini Pro.
2025-06-18 03:09:25,539 - INFO - [MainThread] - Defense app UI setup complete
2025-06-18 03:09:25,541 - INFO - [MainThread] - TiT Defense App 1.0.1 initialized successfully.
2025-06-18 03:09:25,657 - INFO - [Thread-1 (refresh_worker)] - Fetching defense stocks data for all regions...
2025-06-18 03:09:32,488 - ERROR - [Thread-1 (refresh_worker)] - $AJRD: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-18 03:09:32,995 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:33,442 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:36,042 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:37,417 - ERROR - [Thread-1 (refresh_worker)] - $AMSWA: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-18 03:09:37,765 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:38,275 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:38,794 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:39,277 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:40,962 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:41,272 - ERROR - [Thread-1 (refresh_worker)] - $BAE.L: possibly delisted; no price data found  (period=1d)
2025-06-18 03:09:41,934 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:42,901 - ERROR - [Thread-1 (refresh_worker)] - $MEGG.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-18 03:09:45,168 - ERROR - [Thread-1 (refresh_worker)] - $THA.PA: possibly delisted; no price data found  (period=1d)
2025-06-18 03:09:48,368 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:49,203 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:49,494 - ERROR - [Thread-1 (refresh_worker)] - $FNC.MI: possibly delisted; no price data found  (period=1d)
2025-06-18 03:09:53,640 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:55,923 - ERROR - [Thread-1 (refresh_worker)] - $UNAC.ME: possibly delisted; no price data found  (period=1d)
2025-06-18 03:09:56,432 - ERROR - [Thread-1 (refresh_worker)] - $AFKS.ME: possibly delisted; no price data found  (period=1d)
2025-06-18 03:09:57,165 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:57,897 - ERROR - [Thread-1 (refresh_worker)] - $600893.SZ: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-18 03:09:59,197 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:59,981 - ERROR - [Thread-1 (refresh_worker)] - $ELBT.TA: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-18 03:09:59,983 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: defense_stocks
2025-06-18 03:09:59,984 - INFO - [Thread-1 (refresh_worker)] - Fetching defense sector ETFs data...
2025-06-18 03:10:01,973 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:10:02,674 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:10:03,626 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:10:04,446 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: defense_etfs
2025-06-18 03:10:04,451 - INFO - [Thread-1 (refresh_worker)] - Fetching conflict status and geopolitical data...
2025-06-18 03:10:04,501 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: conflict_data
2025-06-18 03:10:04,507 - INFO - [Thread-1 (refresh_worker)] - Fetching arms trade data...
2025-06-18 03:10:04,521 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: arms_trade
