2025-06-18 03:08:27,770 - INFO - [MainThread] - TiT Health App 1.0.1 Starting...
2025-06-18 03:08:27,774 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-18 03:08:34,184 - INFO - [MainThread] - TiT Health App main initialization started...
2025-06-18 03:08:34,185 - INFO - [MainThread] - HealthCacheService initialized.
2025-06-18 03:08:34,186 - INFO - [MainThread] - HealthDataService initialized with comprehensive healthcare coverage.
2025-06-18 03:08:34,187 - INFO - [MainThread] - HealthAIService initialized with Gemini Pro.
2025-06-18 03:08:37,347 - INFO - [MainThread] - Health app UI setup complete
2025-06-18 03:08:37,352 - INFO - [MainThread] - TiT Health App 1.0.1 initialized successfully.
2025-06-18 03:08:37,467 - INFO - [Thread-1 (refresh_worker)] - Fetching health stocks data for all categories...
2025-06-18 03:08:41,256 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:08:43,171 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:08:43,997 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:08:44,810 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:08:45,612 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:08:46,311 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:08:47,060 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:08:47,909 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:08:49,224 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:08:52,472 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:08:58,436 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:00,547 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:01,123 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:03,810 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:04,231 - ERROR - [Thread-1 (refresh_worker)] - $BLUE: possibly delisted; no price data found  (period=1d)
2025-06-18 03:09:04,550 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:06,709 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:07,553 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:10,841 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:12,687 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:13,211 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:13,706 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:14,342 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:14,912 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:15,615 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:16,652 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:17,362 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:18,982 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:19,683 - ERROR - [Thread-1 (refresh_worker)] - $ONEM: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-18 03:09:19,985 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:20,344 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: health_stocks
2025-06-18 03:09:20,345 - INFO - [Thread-1 (refresh_worker)] - Fetching health sector ETFs data...
2025-06-18 03:09:21,246 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:21,930 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:22,655 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:23,369 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:24,013 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-18 03:09:26,475 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: health_etfs
