#!/usr/bin/env python3
"""
Vietnamese Translation Module for Tin Apps
Provides comprehensive Vietnamese translations for all UI elements
"""

# Comprehensive Vietnamese translations
VIETNAMESE_TRANSLATIONS = {
    # Common UI Elements
    "Refresh All": "Làm mới tất cả",
    "Export Analysis": "<PERSON><PERSON>t phân tích", 
    "Generate Analysis": "Tạo phân tích",
    "Loading...": "Đang tải...",
    "Ready": "Sẵn sàng",
    "Error": "Lỗi",
    "Success": "Thành công",
    "Warning": "Cảnh báo",
    "Information": "Thông tin",
    "Close": "Đóng",
    "Cancel": "Hủy",
    "OK": "Đồng ý",
    "Yes": "Có",
    "No": "Không",
    "Save": "Lưu",
    "Open": "Mở",
    "Search": "Tìm kiếm",
    "Filter": "Lọc",
    "Sort": "<PERSON>ắp xếp",
    "Clear": "<PERSON>ó<PERSON>",
    "Apply": "Á<PERSON> dụng",
    "Reset": "Đặt lại",
    
    # Tab Names
    "Dashboard": "Tổng quan",
    "Portfolio": "<PERSON><PERSON> mục",
    "Charts": "Biểu đồ", 
    "News": "Tin tức",
    "Calendar": "Lịch",
    "Analysis": "Phân tích",
    "Settings": "Cài đặt",
    "Tools": "Công cụ",
    "Chat": "Trò chuyện",
    
    # Stock App Specific
    "Gold Dashboard": "Tổng quan Vàng",
    "Gold Miners": "Công ty Khai thác Vàng",
    "Precious Metals News": "Tin tức Kim loại Quý",
    "AI Analysis": "Phân tích AI",
    "Live Precious Metals Prices": "Giá Kim loại Quý Trực tiếp",
    "Major Gold Mining Companies": "Các Công ty Khai thác Vàng Lớn",
    "Precious Metals Market News": "Tin tức Thị trường Kim loại Quý",
    "AI Gold Market Analysis": "Phân tích Thị trường Vàng AI",
    "Export Gold Market Analysis": "Xuất Phân tích Thị trường Vàng",
    "Generate Gold Market Analysis": "Tạo Phân tích Thị trường Vàng",
    
    # Stock Market Terms
    "Market Overview": "Tổng quan Thị trường",
    "Global Markets": "Thị trường Toàn cầu", 
    "Stock Search": "Tìm kiếm Cổ phiếu",
    "Search Results": "Kết quả Tìm kiếm",
    "Stock Details & News Impact": "Chi tiết Cổ phiếu & Tác động Tin tức",
    "Stock Information": "Thông tin Cổ phiếu",
    "News Impact Analysis": "Phân tích Tác động Tin tức",
    "Commodities": "Hàng hóa",
    "Oil & Gold Prices": "Giá Dầu & Vàng",
    "Commodities Analysis": "Phân tích Hàng hóa",
    "Market News": "Tin tức Thị trường",
    "News Controls": "Điều khiển Tin tức",
    "High Impact News": "Tin tức Tác động Cao",
    "News Details": "Chi tiết Tin tức",
    "Market Analysis": "Phân tích Thị trường",
    "Market Analysis Results": "Kết quả Phân tích Thị trường",
    "Generate Market Analysis": "Tạo Phân tích Thị trường",
    "Export Analysis": "Xuất Phân tích",
    "Refresh News": "Làm mới Tin tức",
    
    # Form Labels
    "Select Country:": "Chọn Quốc gia:",
    "Country:": "Quốc gia:",
    "Sector:": "Ngành:",
    "Category:": "Danh mục:",
    "Impact:": "Tác động:",
    "Source:": "Nguồn:",
    "Filter by:": "Lọc theo:",
    "Search:": "Tìm kiếm:",
    
    # Stock Market Indices
    "Top Stock Indices": "Chỉ số Chứng khoán Hàng đầu",
    "Global Stock Market Indices": "Chỉ số Chứng khoán Toàn cầu",
    "Major Stocks": "Cổ phiếu Chính",
    
    # Professional Developer
    "Professional Developer": "Nhà phát triển Chuyên nghiệp",
    "Created By": "Được tạo bởi",
    
    # Defense App Terms
    "Defense Overview": "Tổng quan Quốc phòng",
    "Defense Contractors": "Nhà thầu Quốc phòng", 
    "Geopolitical News": "Tin tức Địa chính trị",
    "Conflict Monitor": "Giám sát Xung đột",
    "Arms Trade": "Buôn bán Vũ khí",
    "Defense Analysis": "Phân tích Quốc phòng",
    
    # Health/Biotech App Terms
    "Health Overview": "Tổng quan Sức khỏe",
    "Biotech Stocks": "Cổ phiếu Công nghệ Sinh học",
    "Medical News": "Tin tức Y tế",
    "Drug Pipeline": "Đường ống Thuốc",
    "Health Analysis": "Phân tích Sức khỏe",
    
    # Oil App Terms
    "Oil Overview": "Tổng quan Dầu mỏ",
    "Oil Prices": "Giá Dầu",
    "Energy News": "Tin tức Năng lượng",
    "Oil Companies": "Công ty Dầu khí",
    "Energy Analysis": "Phân tích Năng lượng",
    
    # Science/Space App Terms
    "Science Overview": "Tổng quan Khoa học",
    "Space Technology": "Công nghệ Vũ trụ",
    "Tech News": "Tin tức Công nghệ",
    "Space Companies": "Công ty Vũ trụ",
    "Technology Analysis": "Phân tích Công nghệ",
    
    # Common Status Messages
    "Refreshing data...": "Đang làm mới dữ liệu...",
    "Generating analysis...": "Đang tạo phân tích...",
    "Loading news...": "Đang tải tin tức...",
    "Fetching market data...": "Đang lấy dữ liệu thị trường...",
    "Connecting to API...": "Đang kết nối API...",
    "Processing...": "Đang xử lý...",
    "Completed": "Hoàn thành",
    "Failed": "Thất bại",
    
    # File Operations
    "Export Report": "Xuất Báo cáo",
    "Save Report": "Lưu Báo cáo", 
    "Load Data": "Tải Dữ liệu",
    "Import Settings": "Nhập Cài đặt",
    "Export Settings": "Xuất Cài đặt",
    
    # Time and Date
    "Today": "Hôm nay",
    "Yesterday": "Hôm qua",
    "This Week": "Tuần này",
    "This Month": "Tháng này",
    "Last Update": "Cập nhật cuối",
    "Real-time": "Thời gian thực",
    
    # Numbers and Metrics
    "Price": "Giá",
    "Volume": "Khối lượng",
    "Change": "Thay đổi",
    "Market Cap": "Vốn hóa",
    "High": "Cao",
    "Low": "Thấp",
    "Open": "Mở cửa",
    "Close": "Đóng cửa",
    "Percentage": "Phần trăm",
    "Value": "Giá trị",
    "Total": "Tổng",
    "Average": "Trung bình",
    
    # AI and Analysis
    "AI Assistant": "Trợ lý AI",
    "Generate Report": "Tạo Báo cáo",
    "Market Prediction": "Dự đoán Thị trường",
    "Technical Analysis": "Phân tích Kỹ thuật",
    "Fundamental Analysis": "Phân tích Cơ bản",
    "Risk Assessment": "Đánh giá Rủi ro",
    "Investment Recommendation": "Khuyến nghị Đầu tư",
    
    # Error Messages
    "Connection Error": "Lỗi Kết nối",
    "Data Not Available": "Dữ liệu Không Có sẵn",
    "Invalid Input": "Đầu vào Không hợp lệ",
    "API Error": "Lỗi API",
    "Network Error": "Lỗi Mạng",
    "Timeout Error": "Lỗi Hết thời gian",
    
    # Success Messages
    "Data Updated Successfully": "Dữ liệu Đã cập nhật Thành công",
    "Report Exported Successfully": "Báo cáo Đã xuất Thành công",
    "Analysis Generated Successfully": "Phân tích Đã tạo Thành công",
    "Settings Saved Successfully": "Cài đặt Đã lưu Thành công"
}

def translate_to_vietnamese(text):
    """
    Translate English text to Vietnamese using the translation dictionary
    """
    if not text or not isinstance(text, str):
        return text
    
    # Direct translation lookup
    if text in VIETNAMESE_TRANSLATIONS:
        return VIETNAMESE_TRANSLATIONS[text]
    
    # Try to find partial matches for compound phrases
    for english, vietnamese in VIETNAMESE_TRANSLATIONS.items():
        if english.lower() in text.lower():
            return text.replace(english, vietnamese)
    
    # Return original text if no translation found
    return text

def get_vietnamese_text(key, default=None):
    """
    Get Vietnamese text by key, with optional default
    """
    return VIETNAMESE_TRANSLATIONS.get(key, default or key)

# Quick access function
def vn(text):
    """Quick Vietnamese translation function"""
    return translate_to_vietnamese(text)

def translate_news_to_vietnamese(news_text):
    """
    Translate news content to Vietnamese using simple keyword replacement
    This is a basic translation for common financial terms
    """
    if not news_text or not isinstance(news_text, str):
        return news_text

    # Financial terms translation
    financial_terms = {
        "stock market": "thị trường chứng khoán",
        "cryptocurrency": "tiền điện tử",
        "bitcoin": "bitcoin",
        "ethereum": "ethereum",
        "price": "giá",
        "trading": "giao dịch",
        "investment": "đầu tư",
        "investor": "nhà đầu tư",
        "market": "thị trường",
        "analysis": "phân tích",
        "forecast": "dự báo",
        "bullish": "tăng giá",
        "bearish": "giảm giá",
        "volatility": "biến động",
        "volume": "khối lượng",
        "portfolio": "danh mục đầu tư",
        "profit": "lợi nhuận",
        "loss": "lỗ",
        "gain": "tăng",
        "decline": "giảm",
        "rally": "tăng mạnh",
        "crash": "sụp đổ",
        "bubble": "bong bóng",
        "correction": "điều chỉnh",
        "resistance": "kháng cự",
        "support": "hỗ trợ",
        "breakout": "đột phá",
        "trend": "xu hướng",
        "bull market": "thị trường tăng",
        "bear market": "thị trường giảm",
        "market cap": "vốn hóa thị trường",
        "dividend": "cổ tức",
        "earnings": "thu nhập",
        "revenue": "doanh thu",
        "quarterly": "hàng quý",
        "annual": "hàng năm",
        "financial": "tài chính",
        "economic": "kinh tế",
        "inflation": "lạm phát",
        "interest rate": "lãi suất",
        "federal reserve": "cục dự trữ liên bang",
        "central bank": "ngân hàng trung ương",
        "GDP": "GDP",
        "recession": "suy thoái",
        "recovery": "phục hồi",
        "growth": "tăng trưởng",
        "regulation": "quy định",
        "compliance": "tuân thủ",
        "SEC": "SEC",
        "IPO": "IPO",
        "merger": "sáp nhập",
        "acquisition": "mua lại",
        "partnership": "đối tác",
        "technology": "công nghệ",
        "innovation": "đổi mới",
        "blockchain": "blockchain",
        "AI": "AI",
        "artificial intelligence": "trí tuệ nhân tạo",
        "machine learning": "học máy",
        "data": "dữ liệu",
        "digital": "số hóa",
        "online": "trực tuyến",
        "platform": "nền tảng",
        "software": "phần mềm",
        "hardware": "phần cứng",
        "semiconductor": "bán dẫn",
        "chip": "chip",
        "processor": "bộ xử lý",
        "cloud": "đám mây",
        "cybersecurity": "an ninh mạng",
        "energy": "năng lượng",
        "oil": "dầu",
        "gas": "khí đốt",
        "renewable": "tái tạo",
        "solar": "năng lượng mặt trời",
        "wind": "gió",
        "electric": "điện",
        "battery": "pin",
        "automotive": "ô tô",
        "healthcare": "chăm sóc sức khỏe",
        "pharmaceutical": "dược phẩm",
        "biotech": "công nghệ sinh học",
        "medical": "y tế",
        "drug": "thuốc",
        "vaccine": "vắc xin",
        "clinical trial": "thử nghiệm lâm sàng",
        "FDA": "FDA",
        "approval": "phê duyệt",
        "real estate": "bất động sản",
        "property": "tài sản",
        "housing": "nhà ở",
        "mortgage": "thế chấp",
        "retail": "bán lẻ",
        "consumer": "người tiêu dùng",
        "spending": "chi tiêu",
        "sales": "bán hàng",
        "manufacturing": "sản xuất",
        "industrial": "công nghiệp",
        "supply chain": "chuỗi cung ứng",
        "logistics": "hậu cần",
        "transportation": "vận tải",
        "airline": "hàng không",
        "shipping": "vận chuyển",
        "commodity": "hàng hóa",
        "gold": "vàng",
        "silver": "bạc",
        "copper": "đồng",
        "agriculture": "nông nghiệp",
        "food": "thực phẩm",
        "beverage": "đồ uống",
        "restaurant": "nhà hàng",
        "hospitality": "khách sạn",
        "tourism": "du lịch",
        "entertainment": "giải trí",
        "media": "truyền thông",
        "streaming": "phát trực tuyến",
        "gaming": "game",
        "sports": "thể thao",
        "fashion": "thời trang",
        "luxury": "xa xỉ",
        "bank": "ngân hàng",
        "insurance": "bảo hiểm",
        "credit": "tín dụng",
        "loan": "vay",
        "debt": "nợ",
        "bond": "trái phiếu",
        "treasury": "kho bạc",
        "yield": "lợi suất",
        "currency": "tiền tệ",
        "dollar": "đô la",
        "euro": "euro",
        "yen": "yen",
        "yuan": "nhân dân tệ",
        "exchange rate": "tỷ giá",
        "forex": "ngoại hối",
        "trade": "thương mại",
        "export": "xuất khẩu",
        "import": "nhập khẩu",
        "tariff": "thuế quan",
        "sanctions": "trừng phạt",
        "geopolitical": "địa chính trị",
        "war": "chiến tranh",
        "conflict": "xung đột",
        "peace": "hòa bình",
        "election": "bầu cử",
        "government": "chính phủ",
        "policy": "chính sách",
        "law": "luật",
        "legal": "pháp lý",
        "court": "tòa án",
        "lawsuit": "kiện tụng",
        "settlement": "giải quyết",
        "fine": "phạt",
        "penalty": "hình phạt"
    }

    # Apply translations (case insensitive)
    translated_text = news_text
    for english, vietnamese in financial_terms.items():
        # Use word boundaries to avoid partial matches
        pattern = r'\b' + re.escape(english) + r'\b'
        translated_text = re.sub(pattern, vietnamese, translated_text, flags=re.IGNORECASE)

    return translated_text

def enhance_vietnamese_ui_speed():
    """
    Enhance UI speed for Vietnamese text rendering
    """
    import tkinter as tk

    # Configure Vietnamese font settings for better performance
    vietnamese_fonts = [
        ("Segoe UI", 10),
        ("Arial Unicode MS", 10),
        ("Tahoma", 10),
        ("Times New Roman", 10)
    ]

    return vietnamese_fonts[0]  # Return best font for Vietnamese
