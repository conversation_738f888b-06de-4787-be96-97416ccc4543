#!/usr/bin/env python3
# EMERGENCY TiT LAUNCHER - GUARANTEED TO WORK!
# NO DEPENDENCIES, NO FANCY THEMES, JUST PURE FUNCTIONALITY!

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os

class EmergencyLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 EMERGENCY TiT LAUNCHER - GUARANTEED TO WORK! 🚀")
        self.root.geometry("800x600")
        self.root.configure(bg='#2C3E50')
        
        # Center window
        self.center_window()
        
        # Create UI
        self.create_ui()
        
    def center_window(self):
        self.root.update_idletasks()
        width = 800
        height = 600
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_ui(self):
        # Main frame
        main_frame = tk.Frame(self.root, bg='#2C3E50', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="🚀 EMERGENCY TiT LAUNCHER 🚀",
            font=("Arial", 24, "bold"),
            fg='#FFFFFF',
            bg='#2C3E50'
        )
        title_label.pack(pady=(0, 20))
        
        subtitle_label = tk.Label(
            main_frame,
            text="GUARANTEED TO WORK - NO SLEEP LOSS!",
            font=("Arial", 16, "bold"),
            fg='#E74C3C',
            bg='#2C3E50'
        )
        subtitle_label.pack(pady=(0, 30))
        
        # Apps list
        apps = [
            ("🚀 CRYPTO APP", "Teu 1.0.1.py"),
            ("📈 STOCK APP", "TiT_Stock_App_1.0.1.py"),
            ("🛢️ OIL APP", "TiT_Oil_App_1.0.1.py"),
            ("🥇 GOLD APP", "TiT_Gold_App_1.0.1.py"),
            ("🧬 HEALTH APP", "TiT_Health_App_1.0.1.py"),
            ("⚔️ DEFENSE APP", "TiT_Defense_App_1.0.1.py"),
            ("🚀 SCIENCE APP", "TiT_Science_App_1.0.1.py")
        ]
        
        # Create buttons for each app
        for app_name, app_file in apps:
            self.create_app_button(main_frame, app_name, app_file)
        
        # Launch All button
        launch_all_btn = tk.Button(
            main_frame,
            text="🚀 LAUNCH ALL APPS NOW! 🚀",
            font=("Arial", 14, "bold"),
            fg='#FFFFFF',
            bg='#E74C3C',
            activebackground='#C0392B',
            activeforeground='#FFFFFF',
            command=self.launch_all,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        launch_all_btn.pack(fill=tk.X, pady=20)
        
        # Status
        status_label = tk.Label(
            main_frame,
            text="© 2025 Anh Quang - EMERGENCY LAUNCHER v1.0",
            font=("Arial", 10),
            fg='#BDC3C7',
            bg='#2C3E50'
        )
        status_label.pack(side=tk.BOTTOM, pady=10)
    
    def create_app_button(self, parent, app_name, app_file):
        # Check if file exists
        exists = os.path.exists(app_file)
        status = "✅ READY" if exists else "❌ MISSING"
        color = "#27AE60" if exists else "#E74C3C"
        
        # Button frame
        btn_frame = tk.Frame(parent, bg='#2C3E50')
        btn_frame.pack(fill=tk.X, pady=5)
        
        # Launch button
        btn = tk.Button(
            btn_frame,
            text=f"{app_name} - {status}",
            font=("Arial", 12, "bold"),
            fg='#FFFFFF',
            bg=color,
            activebackground='#2ECC71' if exists else '#C0392B',
            activeforeground='#FFFFFF',
            command=lambda: self.launch_app(app_name, app_file),
            height=2,
            relief=tk.RAISED,
            bd=2,
            state=tk.NORMAL if exists else tk.DISABLED
        )
        btn.pack(fill=tk.X)
    
    def launch_app(self, app_name, app_file):
        try:
            if not os.path.exists(app_file):
                messagebox.showerror(
                    "❌ FILE NOT FOUND",
                    f"Cannot find {app_file}\n\nPlease check if the file exists!"
                )
                return
            
            # Launch the app
            process = subprocess.Popen([sys.executable, app_file])
            
            messagebox.showinfo(
                "🚀 SUCCESS!",
                f"{app_name} launched successfully!\n\nProcess ID: {process.pid}\n\nThe app should appear in a few seconds!"
            )
            
        except Exception as e:
            messagebox.showerror(
                "❌ LAUNCH ERROR",
                f"Failed to launch {app_name}:\n\n{str(e)}"
            )
    
    def launch_all(self):
        try:
            result = messagebox.askyesno(
                "🚀 LAUNCH ALL APPS",
                "This will launch ALL TiT apps!\n\nAre you sure?"
            )
            
            if not result:
                return
            
            apps = [
                "Teu 1.0.1.py",
                "TiT_Stock_App_1.0.1.py",
                "TiT_Oil_App_1.0.1.py",
                "TiT_Gold_App_1.0.1.py",
                "TiT_Health_App_1.0.1.py",
                "TiT_Defense_App_1.0.1.py",
                "TiT_Science_App_1.0.1.py"
            ]
            
            launched = 0
            failed = []
            
            for app_file in apps:
                try:
                    if os.path.exists(app_file):
                        subprocess.Popen([sys.executable, app_file])
                        launched += 1
                    else:
                        failed.append(app_file)
                except Exception as e:
                    failed.append(f"{app_file} (Error: {e})")
            
            message = f"✅ Successfully launched {launched} apps!"
            if failed:
                message += f"\n\n❌ Failed to launch:\n" + "\n".join(failed)
            
            messagebox.showinfo("🚀 LAUNCH RESULTS", message)
            
        except Exception as e:
            messagebox.showerror("❌ ERROR", f"Launch all failed: {str(e)}")
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    print("🚀 STARTING EMERGENCY LAUNCHER...")
    print("✅ NO DEPENDENCIES REQUIRED!")
    print("✅ GUARANTEED TO WORK!")
    
    launcher = EmergencyLauncher()
    launcher.run()
