# EMERGENCY SIMPLE LAUNCHER - 7 SEPARATE APPS
# NO DEPENDENCIES - WORKS 100% OF THE TIME
# Created by <PERSON><PERSON>

import tkinter as tk
import subprocess
import sys
import os

def launch_app(app_name, file_name):
    """Launch an app"""
    try:
        if os.path.exists(file_name):
            subprocess.Popen([sys.executable, file_name])
            print(f"✅ Launched {app_name}")
        else:
            print(f"❌ File not found: {file_name}")
    except Exception as e:
        print(f"❌ Error launching {app_name}: {e}")

def create_launcher():
    """Create the emergency launcher"""
    root = tk.Tk()
    root.title("🚀 TiT Suite - EMERGENCY LAUNCHER")
    root.geometry("600x500")
    root.configure(bg='#2C3E50')
    
    # Title
    title = tk.Label(root, text="🚀 TiT SUITE - 7 SEPARATE APPS 🚀", 
                    font=("Arial", 16, "bold"), fg='white', bg='#2C3E50')
    title.pack(pady=20)
    
    # Author
    author = tk.Label(root, text="👨‍💻 Created by <PERSON><PERSON> 👨‍💻", 
                     font=("Arial", 12), fg='#E74C3C', bg='#2C3E50')
    author.pack(pady=10)
    
    # Apps
    apps = [
        ("₿ TiT Crypto App", "Teu 1.0.1.py"),
        ("📈 TiT Stock App", "TiT_Stock_App_1.0.1.py"),
        ("🛢️ TiT Oil App", "TiT_Oil_App_1.0.1.py"),
        ("🥇 TiT Gold App", "TiT_Gold_App_1.0.1.py"),
        ("🧬 TiT Health App", "TiT_Health_App_1.0.1.py"),
        ("⚔️ TiT Defense App", "TiT_Defense_App_1.0.1.py"),
        ("🚀 TiT Science App", "TiT_Science_App_1.0.1.py")
    ]
    
    # Create buttons for each app
    for app_name, file_name in apps:
        btn = tk.Button(root, text=f"🚀 {app_name}", 
                       font=("Arial", 11, "bold"),
                       bg='#3498DB', fg='white', width=40, pady=5,
                       command=lambda n=app_name, f=file_name: launch_app(n, f))
        btn.pack(pady=5)
    
    # Launch All button
    launch_all_btn = tk.Button(root, text="🚀 LAUNCH ALL 7 APPS", 
                              font=("Arial", 12, "bold"),
                              bg='#27AE60', fg='white', width=40, pady=8,
                              command=lambda: [launch_app(n, f) for n, f in apps])
    launch_all_btn.pack(pady=15)
    
    # Exit button
    exit_btn = tk.Button(root, text="❌ EXIT", 
                        font=("Arial", 10),
                        bg='#E74C3C', fg='white', width=20, pady=5,
                        command=root.quit)
    exit_btn.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    print("🚀 EMERGENCY SIMPLE LAUNCHER")
    print("📱 7 SEPARATE APPS MENU")
    print("👨‍💻 Created by Anh Quang")
    print("✅ NO DEPENDENCIES - WORKS 100%")
    create_launcher()
