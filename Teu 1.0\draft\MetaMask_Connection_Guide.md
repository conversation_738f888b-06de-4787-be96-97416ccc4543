# MetaMask Wallet Connection Guide for Teu 1.0.1

## What Changed?

The wallet connection dialog has been significantly improved to provide better guidance and user experience when connecting your MetaMask wallet.

## New Features:

### 1. **Enhanced Dialog Layout**
- Larger, more organized dialog window (600x400)
- Clear step-by-step instructions
- Better visual organization with labeled sections

### 2. **Detailed Instructions**
The dialog now includes comprehensive instructions on how to get your wallet address from MetaMask:
1. Open MetaMask extension in your browser
2. Make sure you're on the correct network (Ethereum or BSC)
3. Click on your account name at the top
4. Click "Copy address to clipboard" or copy the address manually
5. Paste the address in the field below

### 3. **Smart Paste Button**
- **Paste Button**: Automatically detects valid wallet addresses from clipboard
- **Validation**: Checks if clipboard contains a valid 42-character address starting with "0x"
- **User Feedback**: Shows success/error messages for paste operations

### 4. **Address Validation**
- **Format Check**: Validates that addresses start with "0x" and are exactly 42 characters
- **Real-time Feedback**: Shows clear error messages for invalid addresses
- **Example Display**: Shows a sample address format for reference

### 5. **Help System**
- **Help Button**: Provides detailed connection help
- **Security Information**: Explains that the app only reads wallet data
- **Network Instructions**: Guidance for both Ethereum and BSC networks

### 6. **Improved Network Selection**
- Clear labels: "Ethereum Mainnet" and "Binance Smart Chain"
- Better visual layout for network options

## How to Use:

### Step 1: Open MetaMask
1. Open your browser and click the MetaMask extension
2. Make sure you're logged in to your MetaMask wallet
3. Select the correct network:
   - For Ethereum tokens: Select "Ethereum Mainnet"
   - For BSC tokens: Add BSC network and select it

### Step 2: Copy Your Address
1. In MetaMask, click on your account name at the top
2. Click "Copy address to clipboard" 
3. Your address will be copied (it starts with "0x" and is 42 characters long)

### Step 3: Connect in Teu
1. In Teu 1.0.1, go to Portfolio tab
2. Click "Connect Wallet" button
3. The improved dialog will open
4. Either:
   - **Paste directly**: Click the "Paste" button to auto-paste from clipboard
   - **Manual entry**: Type or paste your address in the text field
5. Select your blockchain network (Ethereum or BSC)
6. Choose if you want auto-connect on startup
7. Click "Connect Wallet"

### Step 4: Verify Connection
- The app will validate your address format
- It will connect to the blockchain and fetch your wallet data
- You'll see a success message with your balance and token count
- Your portfolio will update with your wallet assets

## Security Notes:

- ✅ **Safe**: This app only reads your wallet data
- ✅ **No Private Keys**: Your private keys never leave MetaMask
- ✅ **No Transactions**: The app cannot make transactions
- ✅ **Read-Only**: Only views balances and token holdings

## Troubleshooting:

### "Invalid Address" Error
- Make sure your address starts with "0x"
- Check that it's exactly 42 characters long
- Copy directly from MetaMask to avoid typos

### "Clipboard Empty" Error
- Copy your address from MetaMask first
- Try the paste button again

### Connection Failed
- Check your internet connection
- Verify you're on the correct network in MetaMask
- Make sure the address is correct

### No Tokens Showing
- Ensure you're connected to the right network
- Some tokens might not be detected by the API
- Try refreshing the wallet data

## Benefits of the New System:

1. **Better Security**: Manual address entry prevents automatic wallet access
2. **User Control**: You choose exactly which wallet to connect
3. **Clear Instructions**: No more guessing how to get your address
4. **Validation**: Prevents connection errors from invalid addresses
5. **Multi-Network**: Easy switching between Ethereum and BSC
6. **Convenience**: Paste button for quick address entry

The improved wallet connection system provides a much better user experience while maintaining security best practices!
