# SIMPLE TiT LAUNCHER - 7 SEPARATE APPS MENU
# Version: 1.0.1 (ULTRA-SIMPLE & RELIABLE)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON>
# Date: 2025-06-17
#
# SIMPLE, RELIABLE launcher for 7 SEPARATE TiT Apps - NO COMPLEX DEPENDENCIES!

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os

class SimpleTiTLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 TiT Suite - 7 SEPARATE APPS MENU 🚀")
        self.root.geometry("800x600")
        self.root.configure(bg='#2C3E50')
        
        # Center window
        self.center_window()
        
        # Create UI
        self.create_ui()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_ui(self):
        """Create the simple, reliable UI"""
        # Main frame
        main_frame = tk.Frame(self.root, bg='#2C3E50', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="🚀 TiT FINANCIAL SUITE 🚀",
            font=("Arial", 20, "bold"),
            fg='#ECF0F1',
            bg='#2C3E50'
        )
        title_label.pack(pady=(0, 10))
        
        # Subtitle
        subtitle_label = tk.Label(
            main_frame,
            text="💎 7 SEPARATE INDEPENDENT APPS 💎",
            font=("Arial", 14, "italic"),
            fg='#3498DB',
            bg='#2C3E50'
        )
        subtitle_label.pack(pady=(0, 20))
        
        # Author
        author_label = tk.Label(
            main_frame,
            text="👨‍💻 Created by Anh Quang 👨‍💻",
            font=("Arial", 12, "bold"),
            fg='#E74C3C',
            bg='#2C3E50'
        )
        author_label.pack(pady=(0, 30))
        
        # Apps frame
        apps_frame = tk.Frame(main_frame, bg='#2C3E50')
        apps_frame.pack(fill=tk.BOTH, expand=True)
        
        # Define the 7 separate apps
        self.apps = [
            {"name": "₿ TiT Crypto App", "file": "Teu 1.0.1.py", "desc": "Cryptocurrency Intelligence"},
            {"name": "📈 TiT Stock App", "file": "TiT_Stock_App_1.0.1.py", "desc": "Global Stock Markets"},
            {"name": "🛢️ TiT Oil App", "file": "TiT_Oil_App_1.0.1.py", "desc": "Oil Market Intelligence"},
            {"name": "🥇 TiT Gold App", "file": "TiT_Gold_App_1.0.1.py", "desc": "Precious Metals"},
            {"name": "🧬 TiT Health App", "file": "TiT_Health_App_1.0.1.py", "desc": "Health & Biotech"},
            {"name": "⚔️ TiT Defense App", "file": "TiT_Defense_App_1.0.1.py", "desc": "Defense & Geopolitics"},
            {"name": "🚀 TiT Science App", "file": "TiT_Science_App_1.0.1.py", "desc": "Science & Technology"}
        ]
        
        # Create app buttons
        for i, app in enumerate(self.apps):
            self.create_app_button(apps_frame, app, i)
        
        # Launch All button
        launch_all_btn = tk.Button(
            main_frame,
            text="🚀 LAUNCH ALL 7 APPS",
            font=("Arial", 12, "bold"),
            bg='#27AE60',
            fg='white',
            padx=20,
            pady=10,
            command=self.launch_all_apps
        )
        launch_all_btn.pack(pady=20)
        
        # Exit button
        exit_btn = tk.Button(
            main_frame,
            text="❌ EXIT",
            font=("Arial", 10),
            bg='#E74C3C',
            fg='white',
            padx=15,
            pady=5,
            command=self.root.quit
        )
        exit_btn.pack()
    
    def create_app_button(self, parent, app, index):
        """Create a button for each app"""
        # App frame
        app_frame = tk.Frame(parent, bg='#34495E', relief=tk.RAISED, bd=2)
        app_frame.pack(fill=tk.X, pady=5, padx=10)
        
        # App info frame
        info_frame = tk.Frame(app_frame, bg='#34495E')
        info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # App name
        name_label = tk.Label(
            info_frame,
            text=app["name"],
            font=("Arial", 12, "bold"),
            fg='#ECF0F1',
            bg='#34495E'
        )
        name_label.pack(anchor=tk.W)
        
        # App description
        desc_label = tk.Label(
            info_frame,
            text=app["desc"],
            font=("Arial", 9),
            fg='#BDC3C7',
            bg='#34495E'
        )
        desc_label.pack(anchor=tk.W)
        
        # File name
        file_label = tk.Label(
            info_frame,
            text=f"📁 {app['file']}",
            font=("Arial", 8),
            fg='#95A5A6',
            bg='#34495E'
        )
        file_label.pack(anchor=tk.W)
        
        # Launch button
        launch_btn = tk.Button(
            app_frame,
            text="🚀 LAUNCH",
            font=("Arial", 10, "bold"),
            bg='#3498DB',
            fg='white',
            padx=15,
            pady=20,
            command=lambda: self.launch_app(app)
        )
        launch_btn.pack(side=tk.RIGHT, padx=10, pady=10)
    
    def launch_app(self, app):
        """Launch a specific app"""
        try:
            app_file = app["file"]
            
            # Check if file exists
            if not os.path.exists(app_file):
                messagebox.showerror(
                    "❌ File Not Found",
                    f"Application file '{app_file}' not found!\n\n"
                    f"Please ensure the file exists in the current directory."
                )
                return
            
            # Launch the app
            print(f"🚀 Launching {app['name']}...")
            process = subprocess.Popen([sys.executable, app_file])
            
            # Show success message
            messagebox.showinfo(
                "🚀 Launch Successful!",
                f"✅ {app['name']} is starting!\n\n"
                f"🖥️ Application window will appear shortly\n"
                f"🎯 Process ID: {process.pid}"
            )
            
        except Exception as e:
            messagebox.showerror(
                "❌ Launch Error",
                f"Failed to launch {app['name']}:\n\n{str(e)}"
            )
    
    def launch_all_apps(self):
        """Launch all 7 apps"""
        try:
            result = messagebox.askyesno(
                "Launch All Apps",
                "This will launch all 7 TiT applications.\n\n"
                "This may use significant system resources.\n\n"
                "Continue?"
            )
            
            if not result:
                return
            
            launched = 0
            failed = []
            
            for app in self.apps:
                try:
                    if os.path.exists(app["file"]):
                        subprocess.Popen([sys.executable, app["file"]])
                        launched += 1
                        print(f"✅ Launched {app['name']}")
                    else:
                        failed.append(f"{app['name']} (file not found)")
                except Exception as e:
                    failed.append(f"{app['name']} (error: {str(e)})")
            
            # Show results
            message = f"✅ Successfully launched {launched} applications!"
            if failed:
                message += f"\n\n❌ Failed to launch:\n" + "\n".join(failed)
            
            messagebox.showinfo("Launch Results", message)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch applications: {str(e)}")

def main():
    """Main function"""
    print("🚀 Starting SIMPLE TiT Launcher...")
    print("📱 7 SEPARATE APPS MENU")
    print("👨‍💻 Created by Anh Quang")
    
    root = tk.Tk()
    app = SimpleTiTLauncher(root)
    
    print("✅ Launcher ready!")
    root.mainloop()

if __name__ == "__main__":
    main()
