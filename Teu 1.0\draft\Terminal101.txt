PS C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package> python "TiT_Oil_App_1.0.1.py"
2025-06-20 00:39:12,981 - INFO - [MainThread] - TiT Oil App 1.0.1 Starting...
2025-06-20 00:39:12,982 - INFO - [MainThread] - Copyright (C) 2025 Nguy<PERSON>. All rights reserved.
2025-06-20 00:39:15,320 - INFO - [MainThread] - TiT Oil App main initialization started...
2025-06-20 00:39:15,320 - INFO - [MainThread] - OilCacheService initialized.
2025-06-20 00:39:15,320 - INFO - [MainThread] - OilDataService initialized with comprehensive oil market coverage and speed enhancement engines.
2025-06-20 00:39:15,321 - INFO - [MainThread] - OilAIService initialized with Gemini Pro.
2025-06-20 00:39:16,636 - INFO - [MainThread] - Oil app UI setup complete
2025-06-20 00:39:16,637 - INFO - [MainThread] - TiT Oil App 1.0.1 initialized successfully.
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 70: character maps to <undefined>   
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 942, in _load_oil_instruments
    logging.info("🚀 Loading oil instruments...")
Message: '🚀 Loading oil instruments...'
Arguments: ()
2025-06-20 00:39:16,692 - INFO - [Thread-2 (_load_oil_instruments)] - 🚀 Loading oil instruments...
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 70: character maps to <undefined>   
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1200, in populate_initial_oil_instruments
    logging.info("🚀 Populating ALL oil instruments in dashboard - NO LIMITS...")
Message: '🚀 Populating ALL oil instruments in dashboard - NO LIMITS...'
Arguments: ()
2025-06-20 00:39:17,037 - INFO - [Thread-2 (_load_oil_instruments)] - 🚀 Populating ALL oil instruments in dashboard - NO LIMITS...
2025-06-20 00:39:17,037 - INFO - [Thread-1 (refresh_worker)] - Fetching real-time oil prices...
2025-06-20 00:39:17,646 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:18,368 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:18,914 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:19,513 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 70-71: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1240, in populate_initial_oil_instruments
    logging.info(f"🛢️ Loaded {count} oil instruments so far...")
Message: '🛢️ Loaded 50 oil instruments so far...'
Arguments: ()
2025-06-20 00:39:19,991 - INFO - [Thread-2 (_load_oil_instruments)] - 🛢️ Loaded 50 oil instruments  so far...
2025-06-20 00:39:20,124 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:21,412 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:22,026 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 70-71: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1240, in populate_initial_oil_instruments
    logging.info(f"🛢️ Loaded {count} oil instruments so far...")
Message: '🛢️ Loaded 100 oil instruments so far...'
Arguments: ()
2025-06-20 00:39:24,634 - INFO - [Thread-2 (_load_oil_instruments)] - 🛢️ Loaded 100 oil instruments  so far...
2025-06-20 00:39:24,678 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:25,329 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:25,635 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: oil_prices
2025-06-20 00:39:25,636 - INFO - [Thread-1 (refresh_worker)] - Fetching oil companies data for all regions...
2025-06-20 00:39:26,957 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:27,629 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 70-71: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1240, in populate_initial_oil_instruments
    logging.info(f"🛢️ Loaded {count} oil instruments so far...")
Message: '🛢️ Loaded 150 oil instruments so far...'
Arguments: ()
2025-06-20 00:39:29,313 - INFO - [Thread-2 (_load_oil_instruments)] - 🛢️ Loaded 150 oil instruments  so far...
2025-06-20 00:39:30,045 - ERROR - [Thread-1 (refresh_worker)] - $PXD: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:30,329 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:31,062 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:31,721 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:32,371 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:33,042 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:33,726 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:34,647 - ERROR - [Thread-1 (refresh_worker)] - $MRO: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 70-71: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1240, in populate_initial_oil_instruments
    logging.info(f"🛢️ Loaded {count} oil instruments so far...")
Message: '🛢️ Loaded 200 oil instruments so far...'
Arguments: ()
2025-06-20 00:39:34,937 - INFO - [Thread-2 (_load_oil_instruments)] - 🛢️ Loaded 200 oil instruments  so far...
2025-06-20 00:39:35,093 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:35,867 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:36,553 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:37,356 - ERROR - [Thread-1 (refresh_worker)] - $NBL: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:38,159 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 70-71: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1240, in populate_initial_oil_instruments
    logging.info(f"🛢️ Loaded {count} oil instruments so far...")
Message: '🛢️ Loaded 250 oil instruments so far...'
Arguments: ()
2025-06-20 00:39:39,955 - INFO - [Thread-2 (_load_oil_instruments)] - 🛢️ Loaded 250 oil instruments  so far...
2025-06-20 00:39:40,039 - ERROR - [Thread-1 (refresh_worker)] - $CLR: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:40,325 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:40,940 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 70: character maps to <undefined>       
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1246, in populate_initial_oil_instruments
    logging.info(f"✅ Dashboard populated with ALL {count} oil instruments - NO LIMITS!")
Message: '✅ Dashboard populated with ALL 268 oil instruments - NO LIMITS!'
Arguments: ()
2025-06-20 00:39:41,244 - INFO - [Thread-2 (_load_oil_instruments)] - ✅ Dashboard populated with ALL 268 oil instruments
 - NO LIMITS!
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 67: character maps to <undefined>       
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 959, in _load_market_stats
    self.update_oil_market_stats()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1539, in update_oil_market_stats
    logging.info(f"✅ Oil market stats updated: {total_futures} futures, {total_companies} companies, {total_news} news")
Message: '✅ Oil market stats updated: 13 futures, 350 companies, 0 news'
Arguments: ()
2025-06-20 00:39:41,502 - INFO - [Thread-3 (_load_market_stats)] - ✅ Oil market stats updated: 13 futures, 350 companies
, 0 news
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f916' in position 66: character maps to <undefined>   
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 972, in _load_ai_insights
    self.generate_oil_ai_insights()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1418, in generate_oil_ai_insights
    logging.info("🤖 Generating AI oil market insights...")
Message: '🤖 Generating AI oil market insights...'
Arguments: ()
2025-06-20 00:39:41,510 - INFO - [Thread-4 (_load_ai_insights)] - 🤖 Generating AI oil market insights...
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 67: character maps to <undefined>       
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1501, in _generate_insights
    logging.info("✅ Oil AI insights generated successfully!")
Message: '✅ Oil AI insights generated successfully!'
Arguments: ()
2025-06-20 00:39:41,525 - INFO - [Thread-5 (_generate_insights)] - ✅ Oil AI insights generated successfully!
2025-06-20 00:39:41,652 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:42,240 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:43,424 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:44,007 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:45,586 - ERROR - [Thread-1 (refresh_worker)] - $ENLC: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:47,129 - ERROR - [Thread-1 (refresh_worker)] - $EQM: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:47,516 - ERROR - [Thread-1 (refresh_worker)] - $TSO: possibly delisted; no price data found  (period=1d)
2025-06-20 00:39:48,668 - ERROR - [Thread-1 (refresh_worker)] - $ANDV: possibly delisted; no price data found  (period=1d)
2025-06-20 00:39:49,026 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:49,811 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:50,463 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:51,407 - ERROR - [Thread-1 (refresh_worker)] - $CVRR: possibly delisted; no price data found  (period=1d)
2025-06-20 00:39:51,730 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:52,578 - ERROR - [Thread-1 (refresh_worker)] - $HFC: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:53,404 - ERROR - [Thread-1 (refresh_worker)] - $PBFX: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:54,430 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:55,189 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:56,952 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:57,745 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:00,512 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:02,581 - ERROR - [Thread-1 (refresh_worker)] - $ENI: possibly delisted; no price data found  (period=1d)
2025-06-20 00:40:02,844 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:06,399 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:07,200 - ERROR - [Thread-1 (refresh_worker)] - $RDSA.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:40:08,661 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 404: 
2025-06-20 00:40:09,295 - ERROR - [Thread-1 (refresh_worker)] - $TULW.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:40:10,066 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:11,870 - ERROR - [Thread-1 (refresh_worker)] - $JKX.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:40:12,145 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:13,012 - ERROR - [Thread-1 (refresh_worker)] - $GENEL.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:40:13,255 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:13,918 - ERROR - [Thread-1 (refresh_worker)] - $ENQUEST.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:40:15,608 - ERROR - [Thread-1 (refresh_worker)] - $CGG.PA: p






PS C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package> python "TiT_Oil_App_1.0.1.py"
2025-06-20 00:39:12,981 - INFO - [MainThread] - TiT Oil App 1.0.1 Starting...
2025-06-20 00:39:12,982 - INFO - [MainThread] - Copyright (C) 2025 Nguyen Le Vinh Quang. All rights reserved.
2025-06-20 00:39:15,320 - INFO - [MainThread] - TiT Oil App main initialization started...
2025-06-20 00:39:15,320 - INFO - [MainThread] - OilCacheService initialized.
2025-06-20 00:39:15,320 - INFO - [MainThread] - OilDataService initialized with comprehensive oil market coverage and speed enhancement engines.
2025-06-20 00:39:15,321 - INFO - [MainThread] - OilAIService initialized with Gemini Pro.
2025-06-20 00:39:16,636 - INFO - [MainThread] - Oil app UI setup complete
2025-06-20 00:39:16,637 - INFO - [MainThread] - TiT Oil App 1.0.1 initialized successfully.
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 70: character maps to <undefined>   
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 942, in _load_oil_instruments
    logging.info("🚀 Loading oil instruments...")
Message: '🚀 Loading oil instruments...'
Arguments: ()
2025-06-20 00:39:16,692 - INFO - [Thread-2 (_load_oil_instruments)] - 🚀 Loading oil instruments...
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 70: character maps to <undefined>   
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1200, in populate_initial_oil_instruments
    logging.info("🚀 Populating ALL oil instruments in dashboard - NO LIMITS...")
Message: '🚀 Populating ALL oil instruments in dashboard - NO LIMITS...'
Arguments: ()
2025-06-20 00:39:17,037 - INFO - [Thread-2 (_load_oil_instruments)] - 🚀 Populating ALL oil instruments in dashboard - NO LIMITS...
2025-06-20 00:39:17,037 - INFO - [Thread-1 (refresh_worker)] - Fetching real-time oil prices...
2025-06-20 00:39:17,646 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:18,368 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:18,914 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:19,513 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 70-71: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1240, in populate_initial_oil_instruments
    logging.info(f"🛢️ Loaded {count} oil instruments so far...")
Message: '🛢️ Loaded 50 oil instruments so far...'
Arguments: ()
2025-06-20 00:39:19,991 - INFO - [Thread-2 (_load_oil_instruments)] - 🛢️ Loaded 50 oil instruments  so far...
2025-06-20 00:39:20,124 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:21,412 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:22,026 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 70-71: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1240, in populate_initial_oil_instruments
    logging.info(f"🛢️ Loaded {count} oil instruments so far...")
Message: '🛢️ Loaded 100 oil instruments so far...'
Arguments: ()
2025-06-20 00:39:24,634 - INFO - [Thread-2 (_load_oil_instruments)] - 🛢️ Loaded 100 oil instruments  so far...
2025-06-20 00:39:24,678 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:25,329 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:25,635 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: oil_prices
2025-06-20 00:39:25,636 - INFO - [Thread-1 (refresh_worker)] - Fetching oil companies data for all regions...
2025-06-20 00:39:26,957 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:27,629 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 70-71: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1240, in populate_initial_oil_instruments
    logging.info(f"🛢️ Loaded {count} oil instruments so far...")
Message: '🛢️ Loaded 150 oil instruments so far...'
Arguments: ()
2025-06-20 00:39:29,313 - INFO - [Thread-2 (_load_oil_instruments)] - 🛢️ Loaded 150 oil instruments  so far...
2025-06-20 00:39:30,045 - ERROR - [Thread-1 (refresh_worker)] - $PXD: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:30,329 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:31,062 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:31,721 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:32,371 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:33,042 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:33,726 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:34,647 - ERROR - [Thread-1 (refresh_worker)] - $MRO: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 70-71: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1240, in populate_initial_oil_instruments
    logging.info(f"🛢️ Loaded {count} oil instruments so far...")
Message: '🛢️ Loaded 200 oil instruments so far...'
Arguments: ()
2025-06-20 00:39:34,937 - INFO - [Thread-2 (_load_oil_instruments)] - 🛢️ Loaded 200 oil instruments  so far...
2025-06-20 00:39:35,093 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:35,867 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:36,553 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:37,356 - ERROR - [Thread-1 (refresh_worker)] - $NBL: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:38,159 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 70-71: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1240, in populate_initial_oil_instruments
    logging.info(f"🛢️ Loaded {count} oil instruments so far...")
Message: '🛢️ Loaded 250 oil instruments so far...'
Arguments: ()
2025-06-20 00:39:39,955 - INFO - [Thread-2 (_load_oil_instruments)] - 🛢️ Loaded 250 oil instruments  so far...
2025-06-20 00:39:40,039 - ERROR - [Thread-1 (refresh_worker)] - $CLR: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:40,325 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:40,940 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 70: character maps to <undefined>       
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 946, in _load_oil_instruments
    self.populate_initial_oil_instruments()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1246, in populate_initial_oil_instruments
    logging.info(f"✅ Dashboard populated with ALL {count} oil instruments - NO LIMITS!")
Message: '✅ Dashboard populated with ALL 268 oil instruments - NO LIMITS!'
Arguments: ()
2025-06-20 00:39:41,244 - INFO - [Thread-2 (_load_oil_instruments)] - ✅ Dashboard populated with ALL 268 oil instruments
 - NO LIMITS!
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 67: character maps to <undefined>       
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 959, in _load_market_stats
    self.update_oil_market_stats()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1539, in update_oil_market_stats
    logging.info(f"✅ Oil market stats updated: {total_futures} futures, {total_companies} companies, {total_news} news")
Message: '✅ Oil market stats updated: 13 futures, 350 companies, 0 news'
Arguments: ()
2025-06-20 00:39:41,502 - INFO - [Thread-3 (_load_market_stats)] - ✅ Oil market stats updated: 13 futures, 350 companies
, 0 news
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f916' in position 66: character maps to <undefined>   
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 972, in _load_ai_insights
    self.generate_oil_ai_insights()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1418, in generate_oil_ai_insights
    logging.info("🤖 Generating AI oil market insights...")
Message: '🤖 Generating AI oil market insights...'
Arguments: ()
2025-06-20 00:39:41,510 - INFO - [Thread-4 (_load_ai_insights)] - 🤖 Generating AI oil market insights...
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 67: character maps to <undefined>       
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1009, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1052, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 989, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Oil_App_1.0.1.py", line 1501, in _generate_insights
    logging.info("✅ Oil AI insights generated successfully!")
Message: '✅ Oil AI insights generated successfully!'
Arguments: ()
2025-06-20 00:39:41,525 - INFO - [Thread-5 (_generate_insights)] - ✅ Oil AI insights generated successfully!
2025-06-20 00:39:41,652 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:42,240 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:43,424 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:44,007 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:45,586 - ERROR - [Thread-1 (refresh_worker)] - $ENLC: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:47,129 - ERROR - [Thread-1 (refresh_worker)] - $EQM: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:47,516 - ERROR - [Thread-1 (refresh_worker)] - $TSO: possibly delisted; no price data found  (period=1d)
2025-06-20 00:39:48,668 - ERROR - [Thread-1 (refresh_worker)] - $ANDV: possibly delisted; no price data found  (period=1d)
2025-06-20 00:39:49,026 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:49,811 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:50,463 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:51,407 - ERROR - [Thread-1 (refresh_worker)] - $CVRR: possibly delisted; no price data found  (period=1d)
2025-06-20 00:39:51,730 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:52,578 - ERROR - [Thread-1 (refresh_worker)] - $HFC: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:53,404 - ERROR - [Thread-1 (refresh_worker)] - $PBFX: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:39:54,430 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:55,189 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:56,952 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:39:57,745 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:00,512 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:02,581 - ERROR - [Thread-1 (refresh_worker)] - $ENI: possibly delisted; no price data found  (period=1d)
2025-06-20 00:40:02,844 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:06,399 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:07,200 - ERROR - [Thread-1 (refresh_worker)] - $RDSA.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:40:08,661 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 404: 
2025-06-20 00:40:09,295 - ERROR - [Thread-1 (refresh_worker)] - $TULW.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:40:10,066 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:11,870 - ERROR - [Thread-1 (refresh_worker)] - $JKX.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:40:12,145 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:13,012 - ERROR - [Thread-1 (refresh_worker)] - $GENEL.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:40:13,255 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-20 00:40:13,918 - ERROR - [Thread-1 (refresh_worker)] - $ENQUEST.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 00:40:15,608 - ERROR - [Thread-1 (refresh_worker)] - $CGG.PA: p