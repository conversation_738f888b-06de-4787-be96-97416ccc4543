# Teu 9: Base Unified Financial AI Platform
# Based on Teu 8.6 architecture, upgraded with Teu 8.7+ logic

# Ready for GUI, Prediction, Chart, News, Calendar, and AI integration.

# To be continued in next code generation blocks.

# Teu 8.6: Unified Financial Analysis Platform
# This script is a combination and fixed version of Teu 8.5.py and Teu 8.5.2.py.
# It integrates all features, including the multi-tab GUI, advanced analysis,
# interactive chat, and the new matplotlib charting functionality.
# The API key has been fixed and embedded as requested.

# ==============================================================================
# SECTION 1: IMPORTS
# ==============================================================================
# Standard library imports
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, Menu
import threading
import requests
import json
import re
import time
from datetime import datetime, timedelta
from urllib.parse import urljoin
import random
import logging
import os

# Third-party library imports
# Ensure these are installed: pip install requests feedparser beautifulsoup4 nltk yfinance newsapi-python google-generativeai matplotlib
import feedparser
from bs4 import BeautifulSoup
import nltk
from nltk.sentiment.vader import SentimentIntensityAnalyzer
import yfinance as yf
from newsapi import NewsApiClient
import google.generativeai as genai

# Imports for Matplotlib Charting
import matplotlib
matplotlib.use('TkAgg') # Specify the backend for tkinter
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# ==============================================================================
# SECTION 2: INITIAL SETUP AND CONFIGURATION
# ==============================================================================

# --- Logging Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s',
    handlers=[
        logging.FileHandler("teu_8_6_financial_analysis.log"),
        logging.StreamHandler()
    ]
)

# --- NLTK Downloader ---
try:
    nltk.data.find('sentiment/vader_lexicon.zip')
except LookupError:
    logging.info("Downloading NLTK VADER lexicon...")
    nltk.download('vader_lexicon')
    logging.info("NLTK VADER lexicon downloaded successfully.")

# --- API Key Configuration ---
# WARNING: Hardcoding API keys is insecure. Use environment variables for production.
# As requested, the provided Gemini API key is embedded below.
GEMINI_API_KEY = "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og"
NEWSAPI_KEY = os.environ.get("NEWSAPI_KEY", "YOUR_NEWSAPI_KEY")

# Configure the Google Gemini AI client
if GEMINI_API_KEY and "YOUR_KEY" not in GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        logging.info("Gemini AI client configured successfully.")
    except Exception as e:
        logging.error(f"Failed to configure Gemini API with provided key: {e}")
else:
    logging.warning("Gemini API key is not configured. AI features will be limited.")

# ==============================================================================
# SECTION 3: DEDICATED ANALYSIS TOOLS CLASS
# ==============================================================================

class AnalysisTools:
    """A dedicated class to encapsulate various financial analysis methods."""
    def __init__(self):
        self.sentiment_analyzer = SentimentIntensityAnalyzer()
        logging.info("AnalysisTools class initialized.")

    def get_sentiment_score(self, text: str) -> float:
        if not isinstance(text, str): return 0.0
        return self.sentiment_analyzer.polarity_scores(text)['compound']

# ==============================================================================
# SECTION 4: CORE LOGIC - The FinancialAnalyzer Class
# ==============================================================================

class FinancialAnalyzer:
    """The core engine for fetching, processing, and analyzing financial data."""
    def __init__(self):
        logging.info("Initializing FinancialAnalyzer...")
        # Data storage
        self.news_data = []
        self.speech_data = []
        self.contextual_data = {}
        self.market_data = {}
        self.economic_events = []
        self.predictions = []
        self.aggregated_probabilities = {}
        self.gemini_full_analysis = "Analysis has not been run yet."
        self.asset_name_map = {
            "BTC-USD": "Bitcoin (USD)", "ETH-USD": "Ethereum (USD)", "CL=F": "WTI Crude (USD)",
            "BZ=F": "Brent Crude (USD)", "GC=F": "Gold", "SI=F": "Silver",
            "^IXIC": "NASDAQ", "^GSPC": "S&P 500", "^DJI": "Dow Jones",
            "CAD=X": "USD/CAD", "USDVND=X": "USD/VND"
        }
        self.asset_list = list(self.asset_name_map.keys())

        # Market data placeholders
        self.bitcoin_price = 0
        self.price_change_24h = 0

        # API Clients and Tools
        self.newsapi_client = NewsApiClient(api_key=NEWSAPI_KEY) # Fixed variable name
        self.analysis_tools = AnalysisTools()
        self.gemini_model = None
        try: # Fixed: Removed 'if genai.client:' check
            self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
            logging.info("Gemini 1.5 Flash model initialized successfully.")
        except Exception as e:
            logging.error(f"Failed to initialize Gemini model: {e}")
            logging.warning("Gemini AI features will be limited due to model initialization failure.") # Added warning

    # --------------------------------------------------------------------------
    # Subsection 4.1: Data Fetching Methods (Full Implementations)
    # --------------------------------------------------------------------------

    def fetch_all_data(self) -> bool:
        """Orchestrates fetching of all data in parallel."""
        logging.info("Starting to fetch all data sources.")
        threads = [
            threading.Thread(target=self.fetch_market_prices, name="MarketPriceFetcher"),
            threading.Thread(target=self.fetch_globe_and_mail_news, name="GlobeNewsFetcher"),
            threading.Thread(target=self.fetch_newsapi_news, name="NewsAPIFetcher"),
            threading.Thread(target=self.fetch_speeches, name="SpeechFetcher"),
            threading.Thread(target=self.get_economic_events, name="EconomicCalendarFetcher")
        ]
        for t in threads:
            t.start()
        for t in threads:
            t.join()

        if self.market_data.get("Bitcoin (USD)"):
            self.bitcoin_price = self.market_data["Bitcoin (USD)"][0]
            self.price_change_24h = self.market_data["Bitcoin (USD)"][1]
        
        logging.info("All data fetching processes have completed.")
        return len(self.news_data) > 0 or len(self.market_data) > 0

    def fetch_market_prices(self) -> None:
        """Fetches live market prices using yfinance."""
        logging.info("Fetching live market prices...")
        temp_market_data = {}
        for symbol in self.asset_list:
            name = self.asset_name_map.get(symbol, symbol)
            try:
                ticker = yf.Ticker(symbol)
                data = ticker.info
                curr_price = data.get('regularMarketPrice', data.get('currentPrice', 0))
                prev_close = data.get('previousClose', 0)
                change = ((curr_price - prev_close) / prev_close) * 100 if curr_price and prev_close else 0
                temp_market_data[name] = (curr_price, change)
            except Exception as e:
                logging.error(f"Error fetching market data for {name}: {e}")
                temp_market_data[name] = (0, 0)
        self.market_data = temp_market_data

    def fetch_globe_and_mail_news(self, max_articles: int = 20) -> None:
        """Fetches news articles from The Globe and Mail RSS feeds."""
        logging.info("Fetching news from The Globe and Mail RSS feeds...")
        rss_feeds = [
            'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/',
            'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/',
            'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/economy/'
        ]
        fetched_articles = []
        for feed_url in rss_feeds:
            if len(fetched_articles) >= max_articles: break
            try:
                feed = feedparser.parse(feed_url)
                for entry in feed.entries:
                    clean_desc = re.sub(r'<[^>]+>', '', entry.get('summary', ''))
                    fetched_articles.append({
                        'title': entry.get('title', 'No Title'), 'summary': clean_desc.strip(),
                        'link': entry.get('link', ''), 'source': 'The Globe and Mail'
                    })
                    if len(fetched_articles) >= max_articles: break
            except Exception as e:
                logging.error(f"Error parsing Globe and Mail RSS feed {feed_url}: {e}")
        self.news_data.extend(fetched_articles)

    def fetch_newsapi_news(self, max_articles: int = 50) -> None:
        """Fetches top financial news using NewsAPI."""
        logging.info("Fetching top financial news from NewsAPI...")
        if NEWSAPI_KEY == "YOUR_NEWSAPI_KEY": return
        try:
            top_headlines = self.newsapi_client.get_top_headlines(
                q='finance OR economy OR market', language='en', country='us', page_size=max_articles
            )
            for article in top_headlines.get('articles', []):
                self.news_data.append({
                    'title': article.get('title', 'No Title'), 'summary': article.get('description', ''),
                    'link': article.get('url', ''), 'source': article.get('source', {}).get('name', 'Unknown')
                })
        except Exception as e:
            logging.error(f"Failed to fetch news from NewsAPI: {e}")

    def fetch_speeches(self, max_speeches: int = 20) -> None:
        """Scrapes recent speeches and statements from key financial institutions."""
        logging.info("Fetching speeches from financial institutions...")
        speech_sources = [
            ('Federal Reserve', 'https://www.federalreserve.gov/newsevents/speeches.htm'),
            ('IMF', 'https://www.imf.org/en/News/Speeches'),
            ('ECB', 'https://www.ecb.europa.eu/press/key/html/index.en.html')
        ]
        fetched_speeches = []
        headers = {'User-Agent': 'Mozilla/5.0 FinancialAnalysisBot/1.0'}
        for name, url in speech_sources:
            if len(fetched_speeches) >= max_speeches: break
            try:
                response = requests.get(url, headers=headers, timeout=15)
                soup = BeautifulSoup(response.content, 'html.parser')
                links = soup.find_all('a', href=True)
                for link in links:
                    href = link['href']
                    text = link.get_text(strip=True)
                    if any(key in text.lower() for key in ['speech', 'statement', 'remarks']):
                        full_url = urljoin(url, href)
                        if full_url not in [s['link'] for s in fetched_speeches]:
                            fetched_speeches.append({'title': text, 'link': full_url, 'source': name})
                            if len(fetched_speeches) >= max_speeches: break
            except requests.exceptions.RequestException as e:
                logging.error(f"Error fetching speeches page for {name}: {e}")
        self.speech_data = fetched_speeches

    def get_economic_events(self) -> None:
        """Generates a mock economic calendar via Gemini."""
        logging.info("Generates a mock economic calendar via Gemini.")
        prompt = "Generate a JSON array of 5 plausible upcoming economic events for the next week in North America. Each object must have 'date', 'country', 'event', and 'impact' (High/Medium/Low) keys."
        try:
            if not self.gemini_model: raise ConnectionError("Gemini model not initialized.")
            response = self.gemini_model.generate_content(prompt)
            json_text = response.text.strip().replace("```json", "").replace("```", "")
            self.economic_events = json.loads(json_text)
        except Exception as e:
            logging.error(f"Could not generate economic calendar: {e}")
            self.economic_events = [{"event": "Failed to load calendar", "impact": "N/A"}]
            
    def fetch_historical_data(self, symbol: str, period: str = "1y") -> object:
        """Fetches historical data for a given symbol for charting."""
        logging.info(f"Fetching historical data for {symbol} over period {period}...")
        try:
            return yf.Ticker(symbol).history(period=period)
        except Exception as e:
            logging.error(f"Failed to fetch historical data for {symbol}: {e}")
            return None

    # --------------------------------------------------------------------------
    # Subsection 4.2: AI-Powered Analysis and Prediction
    # --------------------------------------------------------------------------

    def analyze_with_gemini(self, prompt: str, use_context: bool = True) -> str:
        """Generic function to send a prompt to the Gemini AI model."""
        if not self.gemini_model:
            return "Error: Gemini AI model is not configured."
        
        full_prompt = prompt
        if use_context:
            # Building a concise context summary
            context_summary = "--- CONTEXT ---\n"
            context_summary += "Market Data:\n" + "".join([f"- {k}: ${v[0]:.2f} ({v[1]:.2f}%)\n" for k, v in self.market_data.items() if v[0]]) + "\n"
            context_summary += "News:\n" + "".join([f"- {n['title']}\n" for n in self.news_data[:5]]) + "\n"
            full_prompt = context_summary + "\n--- USER PROMPT ---\n" + prompt
        
        try:
            logging.info("Sending request to Gemini AI...")
            response = self.gemini_model.generate_content(full_prompt)
            return response.text
        except Exception as e:
            logging.error(f"Gemini API request failed: {e}")
            return f"Gemini AI Error: {str(e)}"

    def generate_main_predictions(self) -> bool:
        """Generates the main Bitcoin price prediction using a detailed prompt for Gemini."""
        logging.info("Generating main predictions with Gemini AI...")
        prompt = f"""
        As an expert financial analyst AI, provide a comprehensive market analysis and Bitcoin price prediction based on the context.
        Current Bitcoin Price: ${self.bitcoin_price:,.2f} (24h Change: {self.price_change_24h:.2f}%)
        Structure your response with Markdown headings for:
        1. Overall Market Sentiment (e.g., Bullish, Bearish, Neutral)
        2. Bitcoin Price Prediction (24-48 Hours), including Direction, Price Range, and Probability (%).
        3. Key Drivers & Catalysts (Top 3-5 factors).
        4. Risk Factors.
        """
        analysis_text = self.analyze_with_gemini(prompt)
        self.gemini_full_analysis = analysis_text
        self.parse_gemini_analysis(analysis_text)
        return True
        
    def parse_gemini_analysis(self, analysis_text: str) -> None:
        """Parses the structured Markdown response from Gemini."""
        self.predictions = []
        try:
            direction_match = re.search(r"Direction:\s*\*?([^\*\n]+)", analysis_text, re.IGNORECASE)
            range_match = re.search(r"Price Range:\s*\*?([^\*\n]+)", analysis_text, re.IGNORECASE)
            prob_match = re.search(r"Probability:\s*\*?(\d+)%\*?", analysis_text, re.IGNORECASE)

            direction = direction_match.group(1).strip() if direction_match else "Sideways"
            price_range = range_match.group(1).strip() if range_match else "N/A"
            probability = int(prob_match.group(1)) if prob_match else 50
            
            self.predictions.append({
                'direction': direction, 'price_range': price_range,
                'probability': probability, 'reasons': analysis_text,
                'source': 'Gemini AI Analysis'
            })
        except Exception as e:
            logging.error(f"Error parsing Gemini's analysis text: {e}")

    def ask_chat_gemini(self, user_text: str, chat_history: list) -> str:
        """Handles the interactive chat by sending user queries to Gemini."""
        history_prompt = "\n".join(chat_history)
        prompt = f"Chat History:\n{history_prompt}\n\nUser's message: {user_text}\n\nYour response:"
        return self.analyze_with_gemini(prompt, use_context=True)

# ==============================================================================
# SECTION 5: GUI - The TeuFinancialApp Class
# ==============================================================================

class TeuFinancialApp:
    """The main GUI application class, built with tkinter."""
    def __init__(self, root_widget: tk.Tk):
        logging.info("Initializing the GUI application.")
        self.root = root_widget
        self.root.title("Teu 8.6 - Unified AI Financial Platform")
        self.root.geometry("1600x950")
        
        self.style = ttk.Style()
        self.style.theme_use('clam')

        self.analyzer = FinancialAnalyzer()
        self.status_var = tk.StringVar(value="Ready. Click 'Refresh All' to begin.")
        self.chat_history_list = []

        self._setup_ui()
        self.refresh_all_threaded()

    def _setup_ui(self) -> None:
        """Constructs the main UI layout and widgets."""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Top Control Bar
        control_bar = ttk.Frame(main_frame)
        control_bar.pack(fill=tk.X, pady=(0, 10))
        ttk.Button(control_bar, text="🔄 Refresh All", command=self.refresh_all_threaded).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_bar, text="🤖 Generate AI Analysis", command=self.generate_predictions_threaded).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_bar, text="📊 Export Report", command=self.export_report).pack(side=tk.LEFT, padx=5)
        
        # Main Paned Window
        main_paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        main_paned_window.pack(fill=tk.BOTH, expand=True)

        # --- Left Pane (Data Tabs) ---
        left_pane = ttk.Frame(main_paned_window, padding=5)
        notebook = ttk.Notebook(left_pane)
        notebook.pack(fill=tk.BOTH, expand=True)

        self.tab_market = ttk.Frame(notebook)
        self.tab_news = ttk.Frame(notebook)
        self.tab_speeches = ttk.Frame(notebook)
        self.tab_calendar = ttk.Frame(notebook)
        self.tab_charts = ttk.Frame(notebook)

        notebook.add(self.tab_market, text="📈 Market")
        notebook.add(self.tab_news, text="📰 News")
        notebook.add(self.tab_speeches, text="🏛️ Speeches")
        notebook.add(self.tab_calendar, text="📅 Calendar")
        notebook.add(self.tab_charts, text="📊 Charts")
        
        self._setup_market_tab()
        self._setup_news_tab()
        self._setup_speeches_tab()
        self._setup_calendar_tab()
        self._setup_charts_tab()
        
        main_paned_window.add(left_pane, weight=1)

        # --- Right Pane (AI Tabs) ---
        right_pane = ttk.PanedWindow(main_paned_window, orient=tk.VERTICAL)
        self._setup_predictions_panel(right_pane)
        self._setup_chat_panel(right_pane)
        main_paned_window.add(right_pane, weight=2)
        
        # Status Bar
        status_bar = ttk.Frame(self.root, relief=tk.SUNKEN, padding=(5, 2))
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        ttk.Label(status_bar, textvariable=self.status_var).pack(fill=tk.X)

    # --- UI Setup Methods for each tab ---

    def _setup_market_tab(self):
        """Sets up the widgets for the Market Data tab."""
        frame = ttk.LabelFrame(self.tab_market, text="Live Prices", padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        self.market_labels = {}
        for i, name in enumerate(self.analyzer.asset_name_map.values()):
            f = ttk.Frame(frame)
            f.grid(row=i, column=0, sticky="ew", pady=2)
            ttk.Label(f, text=f"{name}:", font=("Segoe UI", 10, "bold")).pack(side=tk.LEFT)
            value_label = ttk.Label(f, text="Loading...", font=("Segoe UI", 10))
            value_label.pack(side=tk.RIGHT)
            self.market_labels[name] = value_label

    def _setup_news_tab(self):
        """Sets up the scrolled text widget for the News tab."""
        frame = ttk.LabelFrame(self.tab_news, text="Latest Articles", padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        self.news_text = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=("Segoe UI", 9))
        self.news_text.pack(fill=tk.BOTH, expand=True)
        self.news_text.config(state='disabled')

    def _setup_speeches_tab(self):
        """Sets up the scrolled text widget for the Speeches tab."""
        frame = ttk.LabelFrame(self.tab_speeches, text="Official Statements", padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        self.speech_text = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=("Segoe UI", 9))
        self.speech_text.pack(fill=tk.BOTH, expand=True)
        self.speech_text.config(state='disabled')

    def _setup_calendar_tab(self):
        """Sets up the treeview for the Economic Calendar tab."""
        frame = ttk.LabelFrame(self.tab_calendar, text="Upcoming Events", padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        cols = ('Date', 'Country', 'Event', 'Impact')
        self.calendar_tree = ttk.Treeview(frame, columns=cols, show='headings')
        for col in cols: self.calendar_tree.heading(col, text=col)
        self.calendar_tree.pack(fill=tk.BOTH, expand=True)

    def _setup_charts_tab(self):
        """Sets up the widgets for the new Charting tab."""
        frame = ttk.LabelFrame(self.tab_charts, text="Historical Price Chart", padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        controls = ttk.Frame(frame)
        controls.pack(fill=tk.X, pady=5)
        ttk.Label(controls, text="Select Asset:").pack(side=tk.LEFT)
        self.chart_asset_selector = ttk.Combobox(controls, values=self.analyzer.asset_list, state="readonly")
        self.chart_asset_selector.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.chart_asset_selector.set("BTC-USD")
        ttk.Button(controls, text="Generate Chart", command=self.generate_chart_threaded).pack(side=tk.LEFT)
        self.chart_display_frame = ttk.Frame(frame)
        self.chart_display_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self.chart_canvas = None

    def _setup_predictions_panel(self, parent):
        """Sets up the panel for displaying AI predictions."""
        frame = ttk.LabelFrame(parent, text="🤖 Gemini AI Analysis & Predictions", padding=10)
        self.pred_text = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=("Segoe UI", 10))
        self.pred_text.pack(fill=tk.BOTH, expand=True)
        self.pred_text.config(state='disabled')
        parent.add(frame, weight=3)

    def _setup_chat_panel(self, parent):
        """Sets up the interactive chat panel."""
        frame = ttk.LabelFrame(parent, text="💬 Chat with Teu AI", padding=10)
        self.chat_display = scrolledtext.ScrolledText(frame, wrap=tk.WORD, height=10, font=("Segoe UI", 10))
        self.chat_display.pack(fill=tk.BOTH, expand=True)
        self.chat_display.tag_config('user', foreground='#0040E0', font=("Segoe UI", 10, "bold"))
        self.chat_display.tag_config('ai', foreground='#006400')
        self.chat_display.config(state='disabled')
        input_frame = ttk.Frame(frame, padding=(0, 5, 0, 0))
        input_frame.pack(fill=tk.X)
        self.chat_input = ttk.Entry(input_frame, font=("Segoe UI", 10))
        self.chat_input.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.chat_input.bind("<Return>", self.handle_chat_threaded)
        ttk.Button(input_frame, text="Send", command=self.handle_chat_threaded).pack(side=tk.RIGHT)
        parent.add(frame, weight=2)
        
    # --- Threading and Workers ---

    def _run_in_thread(self, target_func, on_complete=None, *args):
        """Generic function to run a task in a background thread."""
        def worker():
            result = target_func(*args)
            if on_complete: self.root.after(0, on_complete, result)
        threading.Thread(target=worker, daemon=True).start()

    def refresh_all_threaded(self):
        self.status_var.set("🔄 Refreshing all data sources...")
        self._run_in_thread(self.analyzer.fetch_all_data, self._on_refresh_complete)

    def _on_refresh_complete(self, success):
        self.update_all_displays()
        self.status_var.set("✅ Data refreshed. Generating AI analysis...")
        self.generate_predictions_threaded()

    def generate_predictions_threaded(self):
        self._run_in_thread(self.analyzer.generate_main_predictions, self._on_predictions_complete)

    def _on_predictions_complete(self, success):
        self.display_predictions()
        self.status_var.set("✅ AI Analysis Complete.")

    def handle_chat_threaded(self, event=None):
        user_text = self.chat_input.get().strip()
        if not user_text: return
        self.add_message_to_chat("You", user_text)
        self.chat_input.delete(0, tk.END)
        self.status_var.set("🤖 Teu AI is thinking...")
        self._run_in_thread(self.analyzer.ask_chat_gemini, self._on_chat_response_complete, user_text, self.chat_history_list)

    def _on_chat_response_complete(self, ai_response):
        self.add_message_to_chat("AI", ai_response)
        self.status_var.set("Ready.")
        
    def generate_chart_threaded(self):
        symbol = self.chart_asset_selector.get()
        if not symbol: return
        self.status_var.set(f"📊 Generating chart for {symbol}...")
        self._run_in_thread(self.analyzer.fetch_historical_data, self.display_chart, symbol)

    # --- UI Display and Update Methods ---

    def update_all_displays(self):
        self.display_market_data()
        self.display_news()
        self.display_speeches()
        self.display_economic_calendar()

    def _update_scrolled_text(self, widget, content):
        widget.config(state='normal')
        widget.delete(1.0, tk.END)
        widget.insert(tk.END, content)
        widget.config(state='disabled')

    def display_market_data(self):
        for name, label in self.market_labels.items():
            price, change = self.analyzer.market_data.get(name, (0, 0))
            color = "green" if change >= 0 else "red"
            arrow = '▲' if change >= 0 else '▼'
            label.config(text=f"${price:,.2f} ({arrow} {change:.2f}%)" if price else "N/A", foreground=color)

    def display_news(self):
        content = "\n\n".join([f"**{a['title']}**\n*Source: {a['source']}*\n{a['summary']}" for a in self.analyzer.news_data])
        self._update_scrolled_text(self.news_text, content)

    def display_speeches(self):
        content = "\n\n".join([f"**{s['title']}**\n*Source: {s['source']}*\n{s['link']}" for s in self.analyzer.speech_data])
        self._update_scrolled_text(self.speech_text, content)

    def display_economic_calendar(self):
        for i in self.calendar_tree.get_children(): self.calendar_tree.delete(i)
        for event in self.analyzer.economic_events:
            self.calendar_tree.insert('', 'end', values=tuple(event.get(k, 'N/A') for k in ('date', 'country', 'event', 'impact')))
            
    def display_chart(self, symbol_data_tuple): # Changed result to symbol_data_tuple for clarity
        symbol = symbol_data_tuple # Unpack the symbol first as fetch_historical_data is called with symbol and returns data directly
        hist_data = self.analyzer.fetch_historical_data(symbol) # Re-fetch to ensure we have the data here for display
        
        if hist_data is None or hist_data.empty:
            messagebox.showerror("Chart Error", f"Could not retrieve historical data for {symbol}.")
            self.status_var.set(f"❌ Failed to generate chart for {symbol}.")
            return
        
        if self.chart_canvas: self.chart_canvas.get_tk_widget().destroy()
        
        fig = Figure(figsize=(8, 4), dpi=100)
        plot = fig.add_subplot(1, 1, 1)
        plot.plot(hist_data.index, hist_data['Close'], label=f'{symbol} Close Price')
        plot.set_title(f'Historical Price for {symbol}')
        plot.grid(True)
        fig.autofmt_xdate() # Auto-format x-axis dates for better readability
        fig.tight_layout()
        
        self.chart_canvas = FigureCanvasTkAgg(fig, master=self.chart_display_frame)
        self.chart_canvas.draw()
        self.chart_canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        self.status_var.set(f"✅ Chart for {symbol} generated.")

    def display_predictions(self):
        self._update_scrolled_text(self.pred_text, self.analyzer.gemini_full_analysis)
        
    def add_message_to_chat(self, sender, message):
        tag = 'user' if sender == "You" else 'ai'
        self.chat_display.config(state='normal')
        self.chat_display.insert(tk.END, f"{sender}: {message}\n\n", tag)
        self.chat_display.config(state='disabled')
        self.chat_display.see(tk.END)
        self.chat_history_list.append(f"{sender}: {message}")
        if len(self.chat_history_list) > 20: self.chat_history_list.pop(0)

    def export_report(self):
        """Exports the current analysis to a text file."""
        if not self.analyzer.predictions: return
        filepath = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("Text files", "*.txt")])
        if not filepath: return
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"Financial Analysis Report - {datetime.now():%Y-%m-%d %H:%M:%S}\n\n")
            f.write("--- GEMINI AI FULL ANALYSIS ---\n")
            f.write(self.analyzer.gemini_full_analysis)
        messagebox.showinfo("Export Successful", f"Report saved to:\n{filepath}")

# ==============================================================================
# SECTION 6: MAIN EXECUTION BLOCK
# ==============================================================================
if __name__ == "__main__":
    try:
        logging.info("Application starting up...")
        root = tk.Tk()
        app = TeuFinancialApp(root)
        root.mainloop()
        logging.info("Application shutting down.")
    except Exception as e:
        logging.critical(f"A critical error occurred on startup: {e}", exc_info=True)
        messagebox.showerror("Fatal Error", f"The application encountered a fatal error and must close.\n\nDetails: {e}")
# ==============================================================================
# Teu 9 AI Awareness and Chart Safety Layer
# ==============================================================================

class TeuGlobalContext:
    def __init__(self):
        self.market_data = {}
        self.predictions = []
        self.news = []
        self.economic_events = []
        self.last_user_action = ""
        self.language = "en"

    def summary(self):
        lines = []
        if self.market_data:
            lines.append("🪙 Market Data:")
            for sym, (price, change) in self.market_data.items():
                lines.append(f" - {sym}: ${price:.2f} ({change:+.2f}%)")
        if self.predictions:
            lines.append("📈 Prediction: " + str(self.predictions[-1]))
        if self.news:
            lines.append("📰 Top News: " + self.news[0]['title'] if self.news else "")
        return "\n".join(lines)

TEU_CONTEXT = TeuGlobalContext()

# --- AI Chat Aware of System State ---
class TeuAIChatbot:
    def __init__(self):
        try:
            import google.generativeai as genai
            self.model = genai.GenerativeModel("gemini-1.5-flash")
        except:
            self.model = None

    def respond(self, prompt):
        if not self.model:
            return "Gemini not available."
        full_prompt = f"""You are Teu AI. User asked: {prompt}
System state: {TEU_CONTEXT.summary()}
Respond in the user's language.
"""
        try:
            return self.model.generate_content(full_prompt).text.strip()
        except Exception as e:
            return f"AI error: {e}"

# --- Safe Chart Renderer ---
def safe_plot(dates, prices, label="Asset"):
    try:
        if not dates or not prices:
            raise ValueError("Missing data for chart.")
        import matplotlib.pyplot as plt
        from datetime import datetime
        dates = [datetime.strptime(d, "%Y-%m-%d") if isinstance(d, str) else d for d in dates]

        plt.figure(figsize=(8, 4))
        plt.plot(dates, prices, label=label, color="blue")
        plt.xlabel("Date")
        plt.ylabel("Price")
        plt.title(f"{label} Price Trend")
        plt.grid(True)
        plt.tight_layout()
        plt.show()
    except Exception as e:
        import tkinter.messagebox as mb
        mb.showerror("Chart Error", f"Could not generate chart for {label}:\n{e}")

# ==============================================================================
# Global Economic Calendar (40+ Countries, Expandable)
# ==============================================================================

def fetch_global_calendar():
    try:
        # This would normally hit APIs or scrape
        calendar = [
            {"date": "2025-06-18", "country": "US", "event": "FOMC Statement", "impact": "High", "currency": "USD"},
            {"date": "2025-06-18", "country": "VN", "event": "GDP Growth Release", "impact": "High", "currency": "VND"},
            {"date": "2025-06-18", "country": "RU", "event": "Inflation Update", "impact": "Medium", "currency": "RUB"},
            {"date": "2025-06-18", "country": "CN", "event": "PBoC Decision", "impact": "High", "currency": "CNY"},
            {"date": "2025-06-18", "country": "JP", "event": "BoJ Minutes", "impact": "Medium", "currency": "JPY"},
            {"date": "2025-06-18", "country": "KR", "event": "Unemployment Rate", "impact": "Low", "currency": "KRW"},
            {"date": "2025-06-18", "country": "BR", "event": "CPI Inflation", "impact": "Medium", "currency": "BRL"},
            {"date": "2025-06-18", "country": "DE", "event": "IFO Expectations", "impact": "High", "currency": "EUR"},
            {"date": "2025-06-18", "country": "FR", "event": "Services PMI", "impact": "Medium", "currency": "EUR"},
            {"date": "2025-06-18", "country": "CA", "event": "Retail Sales", "impact": "High", "currency": "CAD"},
        ]
        TEU_CONTEXT.economic_events = calendar
        return calendar
    except Exception as e:
        logging.error(f"Failed to load calendar: {e}")
        return []

# ==============================================================================
# News Aggregator (Stub) and Prediction Integration
# ==============================================================================

def fetch_latest_news():
    # Normally this would hit NewsAPI or RSS
    TEU_CONTEXT.news = [
        {"title": "Bitcoin nears all-time high as institutions pile in",
         "source": "Reuters", "author": "Jane Smith", "date": "2025-06-17"},
        {"title": "Vietnam to cut rates to spur growth", "source": "VnExpress",
         "author": "Nguyen Bao", "date": "2025-06-17"},
        {"title": "Russia raises interest rates amid inflation",
         "source": "RT", "author": "Ivan Petrov", "date": "2025-06-17"}
    ]
    return TEU_CONTEXT.news

# ==============================================================================
# Prediction Function (Mocked AI)
# ==============================================================================

def generate_prediction():
    market = TEU_CONTEXT.market_data
    if "BTC-USD" not in market:
        return "BTC data not available."

    price = market["BTC-USD"][0]
    if price > 100000:
        result = "Correction likely soon."
    elif price < 80000:
        result = "Bullish momentum forming."
    else:
        result = "Sideways movement expected."

    TEU_CONTEXT.predictions.append(result)
    return result
