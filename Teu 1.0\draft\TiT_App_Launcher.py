# TiT App Launcher - Main Menu Board
# Version: 1.0.1
# 
# Copyright (C) 2025 Ng<PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
# Date: 2025-06-17
#
# This software is the proprietary work of <PERSON><PERSON><PERSON>.
# Unauthorized copying, distribution, or modification of this software,
# via any medium, is strictly prohibited without explicit written permission.

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("tit_launcher.log", mode='w'),
        logging.StreamHandler()
    ]
)

class TiTAppLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("TiT App Suite - Main Launcher")
        self.root.geometry("700x500")
        self.root.minsize(600, 400)
        self.root.configure(bg='#1e1e1e')

        # Position window in top-left corner to avoid overlap
        self.position_launcher_window()
        
        # Store running processes
        self.running_processes = {}
        
        logging.info("TiT App Launcher Starting...")
        logging.info("Copyright (C) 2025 Nguyen Le Vinh Quang. All rights reserved.")
        
        self.setup_ui()
        
    def center_window(self):
        """Center the window on screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def position_launcher_window(self):
        """Position launcher window to avoid overlap with launched apps."""
        self.root.update_idletasks()
        # Position in top-left corner with some margin
        x = 50
        y = 50
        self.root.geometry(f'700x500+{x}+{y}')
        
    def setup_ui(self):
        """Setup the main launcher UI."""
        # Main container
        main_frame = tk.Frame(self.root, bg='#1e1e1e')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Header
        header_frame = tk.Frame(main_frame, bg='#1e1e1e')
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="TiT App Suite",
            font=('Arial', 28, 'bold'),
            fg='#00d4aa',
            bg='#1e1e1e'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            header_frame,
            text="Advanced Financial Intelligence Suite",
            font=('Arial', 14),
            fg='#888888',
            bg='#1e1e1e'
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Author credit
        author_label = tk.Label(
            header_frame,
            text="by Nguyen Le Vinh Quang (Anh Quang)",
            font=('Arial', 10),
            fg='#666666',
            bg='#1e1e1e'
        )
        author_label.pack(pady=(10, 0))
        
        # Apps grid
        apps_frame = tk.Frame(main_frame, bg='#1e1e1e')
        apps_frame.pack(fill=tk.BOTH, expand=True)
        
        # Configure grid
        apps_frame.grid_columnconfigure(0, weight=1)
        apps_frame.grid_columnconfigure(1, weight=1)
        apps_frame.grid_rowconfigure(0, weight=1)
        apps_frame.grid_rowconfigure(1, weight=1)
        apps_frame.grid_rowconfigure(2, weight=1)
        
        # App buttons
        self.create_app_button(
            apps_frame, 
            "🪙 Crypto App", 
            "Advanced Cryptocurrency\nMarket Analysis\n\n• 60+ Cryptocurrencies\n• Fear & Greed Index\n• Global Market Metrics\n• Enhanced News Feed",
            self.launch_crypto_app,
            0, 0
        )
        
        self.create_app_button(
            apps_frame, 
            "📈 Stock App", 
            "Global Stock Market\nIntelligence\n\n• 20 Major Countries\n• The Globe and Mail Data\n• Oil & Gold Prices\n• Real-time Market Data",
            self.launch_stock_app,
            0, 1
        )
        
        self.create_app_button(
            apps_frame, 
            "🛢️ Oil & Commodities", 
            "Commodities Market\nTracking (Coming Soon)\n\n• Oil Prices (WTI, Brent)\n• Gold & Silver\n• Natural Gas\n• Agricultural Commodities",
            self.launch_commodities_app,
            1, 0
        )
        
        self.create_app_button(
            apps_frame, 
            "🌍 Global Markets", 
            "Worldwide Market\nOverview (Coming Soon)\n\n• All Major Indices\n• Currency Exchange\n• Economic Indicators\n• Regional Analysis",
            self.launch_global_app,
            1, 1
        )
        
        # Control buttons
        control_frame = tk.Frame(main_frame, bg='#1e1e1e')
        control_frame.pack(fill=tk.X, pady=(30, 0))

        # Status label
        self.status_var = tk.StringVar(value="Ready - Select an app to launch")
        status_label = tk.Label(
            control_frame,
            textvariable=self.status_var,
            font=('Arial', 10),
            fg='#888888',
            bg='#1e1e1e'
        )
        status_label.pack(side=tk.LEFT)

        # Minimize launcher button
        minimize_btn = tk.Button(
            control_frame,
            text="Minimize Launcher",
            command=self.minimize_launcher,
            bg='#4444ff',
            fg='white',
            font=('Arial', 10),
            relief=tk.FLAT,
            padx=20,
            pady=5
        )
        minimize_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # Close all button
        close_all_btn = tk.Button(
            control_frame,
            text="Close All Apps",
            command=self.close_all_apps,
            bg='#ff4444',
            fg='white',
            font=('Arial', 10, 'bold'),
            relief=tk.FLAT,
            padx=20,
            pady=5
        )
        close_all_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # Exit button
        exit_btn = tk.Button(
            control_frame,
            text="Exit Launcher",
            command=self.exit_launcher,
            bg='#666666',
            fg='white',
            font=('Arial', 10),
            relief=tk.FLAT,
            padx=20,
            pady=5
        )
        exit_btn.pack(side=tk.RIGHT)
        
    def create_app_button(self, parent, title, description, command, row, col):
        """Create an app launch button."""
        # Button frame
        btn_frame = tk.Frame(parent, bg='#2d2d2d', relief=tk.RAISED, bd=2)
        btn_frame.grid(row=row, column=col, padx=15, pady=15, sticky='nsew')
        
        # Make button clickable
        btn_frame.bind("<Button-1>", lambda e: command())
        btn_frame.bind("<Enter>", lambda e: btn_frame.configure(bg='#3d3d3d'))
        btn_frame.bind("<Leave>", lambda e: btn_frame.configure(bg='#2d2d2d'))
        
        # Title
        title_label = tk.Label(
            btn_frame,
            text=title,
            font=('Arial', 16, 'bold'),
            fg='#00d4aa',
            bg='#2d2d2d'
        )
        title_label.pack(pady=(20, 10))
        title_label.bind("<Button-1>", lambda e: command())
        title_label.bind("<Enter>", lambda e: btn_frame.configure(bg='#3d3d3d'))
        title_label.bind("<Leave>", lambda e: btn_frame.configure(bg='#2d2d2d'))
        
        # Description
        desc_label = tk.Label(
            btn_frame,
            text=description,
            font=('Arial', 10),
            fg='#cccccc',
            bg='#2d2d2d',
            justify=tk.LEFT
        )
        desc_label.pack(pady=(0, 20), padx=20)
        desc_label.bind("<Button-1>", lambda e: command())
        desc_label.bind("<Enter>", lambda e: btn_frame.configure(bg='#3d3d3d'))
        desc_label.bind("<Leave>", lambda e: btn_frame.configure(bg='#2d2d2d'))
        
    def launch_crypto_app(self):
        """Launch the crypto app."""
        try:
            if 'crypto' in self.running_processes:
                # Check if process is still running
                if self.running_processes['crypto'].poll() is None:
                    messagebox.showinfo("App Running", "Crypto App is already running!")
                    return
                else:
                    # Process ended, remove from tracking
                    del self.running_processes['crypto']

            self.status_var.set("Launching Crypto App...")
            logging.info("Launching Crypto App...")

            # Check if file exists
            if not os.path.exists("Teu 1.0.1.py"):
                messagebox.showerror("File Not Found", "Crypto App file 'Teu 1.0.1.py' not found!")
                self.status_var.set("Error: Crypto App file not found")
                return

            # Minimize launcher window
            self.root.iconify()

            # Launch the crypto app
            process = subprocess.Popen([sys.executable, "Teu 1.0.1.py"])
            self.running_processes['crypto'] = process

            # Wait a moment then restore launcher
            self.root.after(2000, self.restore_launcher)

            self.status_var.set("Crypto App launched successfully")
            logging.info("Crypto App launched successfully")

        except Exception as e:
            error_msg = f"Error launching Crypto App: {e}"
            logging.error(error_msg)
            messagebox.showerror("Launch Error", error_msg)
            self.status_var.set("Error launching Crypto App")
    
    def launch_stock_app(self):
        """Launch the stock app."""
        try:
            if 'stock' in self.running_processes:
                # Check if process is still running
                if self.running_processes['stock'].poll() is None:
                    messagebox.showinfo("App Running", "Stock App is already running!")
                    return
                else:
                    # Process ended, remove from tracking
                    del self.running_processes['stock']

            self.status_var.set("Launching Stock App...")
            logging.info("Launching Stock App...")

            # Check if file exists
            if not os.path.exists("TiT_Stock_App_1.0.1.py"):
                messagebox.showerror("File Not Found", "Stock App file 'TiT_Stock_App_1.0.1.py' not found!")
                self.status_var.set("Error: Stock App file not found")
                return

            # Minimize launcher window
            self.root.iconify()

            # Launch the stock app
            process = subprocess.Popen([sys.executable, "TiT_Stock_App_1.0.1.py"])
            self.running_processes['stock'] = process

            # Wait a moment then restore launcher
            self.root.after(2000, self.restore_launcher)

            self.status_var.set("Stock App launched successfully")
            logging.info("Stock App launched successfully")

        except Exception as e:
            error_msg = f"Error launching Stock App: {e}"
            logging.error(error_msg)
            messagebox.showerror("Launch Error", error_msg)
            self.status_var.set("Error launching Stock App")
    
    def launch_commodities_app(self):
        """Launch commodities app (placeholder)."""
        messagebox.showinfo(
            "Coming Soon", 
            "🛢️ Oil & Commodities App\n\nThis feature is coming soon!\n\n"
            "Will include:\n"
            "• Real-time oil prices (WTI, Brent)\n"
            "• Gold, Silver, Copper tracking\n"
            "• Natural Gas futures\n"
            "• Agricultural commodities\n"
            "• Commodities news & analysis"
        )
        
    def launch_global_app(self):
        """Launch global markets app (placeholder)."""
        messagebox.showinfo(
            "Coming Soon", 
            "🌍 Global Markets App\n\nThis feature is coming soon!\n\n"
            "Will include:\n"
            "• All major world indices\n"
            "• Currency exchange rates\n"
            "• Economic indicators\n"
            "• Regional market analysis\n"
            "• Cross-market correlations"
        )
    
    def close_all_apps(self):
        """Close all running apps."""
        try:
            closed_count = 0
            for app_name, process in list(self.running_processes.items()):
                try:
                    if process.poll() is None:  # Process is still running
                        process.terminate()
                        closed_count += 1
                        logging.info(f"Closed {app_name} app")
                except Exception as e:
                    logging.error(f"Error closing {app_name} app: {e}")
                    
            self.running_processes.clear()
            
            if closed_count > 0:
                self.status_var.set(f"Closed {closed_count} running app(s)")
                messagebox.showinfo("Apps Closed", f"Successfully closed {closed_count} running app(s)")
            else:
                self.status_var.set("No apps were running")
                messagebox.showinfo("No Apps Running", "No apps were currently running")
                
        except Exception as e:
            error_msg = f"Error closing apps: {e}"
            logging.error(error_msg)
            messagebox.showerror("Close Error", error_msg)
    
    def exit_launcher(self):
        """Exit the launcher."""
        try:
            # Close all running apps first
            for app_name, process in self.running_processes.items():
                try:
                    if process.poll() is None:
                        process.terminate()
                        logging.info(f"Terminated {app_name} app before exit")
                except:
                    pass
            
            logging.info("TiT App Launcher closing...")
            self.root.destroy()
            
        except Exception as e:
            logging.error(f"Error during exit: {e}")
            self.root.destroy()

    def restore_launcher(self):
        """Restore the launcher window after app launch."""
        try:
            self.root.deiconify()
            self.root.lift()
            self.root.focus_force()
        except Exception as e:
            logging.error(f"Error restoring launcher: {e}")

    def minimize_launcher(self):
        """Minimize the launcher window manually."""
        try:
            self.root.iconify()
            self.status_var.set("Launcher minimized - Click taskbar to restore")
            logging.info("Launcher minimized manually")
        except Exception as e:
            logging.error(f"Error minimizing launcher: {e}")

if __name__ == "__main__":
    try:
        root = tk.Tk()
        app = TiTAppLauncher(root)
        root.mainloop()
    except Exception as e:
        logging.critical(f"Critical error in launcher: {e}")
        print(f"Critical error: {e}")
