# TiT Enhanced Integration: Quality & Intelligence Integration Module
# Version: 1.0.1 (Integration Enhancement Module)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
# Date: 2025-06-17
#
# Description:
# Integration module that enhances all TiT apps with advanced quality assessment,
# news intelligence, and cross-market correlation analysis. This module provides
# the enhanced functionality to be integrated into existing apps.

import logging
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
from TiT_Quality_Engine import DataQualityEngine, NewsIntelligenceEngine
from TiT_Intelligence_Engine import CrossMarketIntelligenceEngine

# ==============================================================================
# SECTION 1: ENHANCED APP INTEGRATION MIXIN
# ==============================================================================
class EnhancedAppMixin:
    """Mixin class to add quality and intelligence features to existing apps"""
    
    def __init__(self, api_key: str = "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og"):
        # Initialize quality and intelligence engines
        self.quality_engine = DataQualityEngine()
        self.news_intelligence = NewsIntelligenceEngine()
        self.cross_market_intelligence = CrossMarketIntelligenceEngine(api_key)
        
        # Enhanced state tracking
        self.enhanced_state = {
            'data_quality_scores': {},
            'news_intelligence_analysis': {},
            'cross_market_analysis': {},
            'quality_alerts': [],
            'intelligence_insights': [],
            'last_quality_check': None,
            'last_intelligence_update': None
        }
        
        logging.info("Enhanced App Integration initialized with quality and intelligence engines.")

    def enhance_data_with_quality_assessment(self, data: Dict, data_type: str) -> Dict:
        """Enhance data with comprehensive quality assessment"""
        try:
            # Perform quality assessment
            quality_assessment = self.quality_engine.assess_data_quality(data, data_type)
            
            # Store quality score
            self.enhanced_state['data_quality_scores'][data_type] = quality_assessment
            
            # Generate quality alerts if needed
            if quality_assessment['overall_score'] < 0.6:
                alert = {
                    'type': 'data_quality',
                    'severity': 'warning' if quality_assessment['overall_score'] > 0.4 else 'critical',
                    'message': f"Low data quality detected for {data_type}: {quality_assessment['quality_level']}",
                    'recommendations': quality_assessment['recommendations'],
                    'timestamp': datetime.now().isoformat()
                }
                self.enhanced_state['quality_alerts'].append(alert)
            
            # Enhance original data with quality metadata
            enhanced_data = data.copy() if isinstance(data, dict) else data
            if isinstance(enhanced_data, dict):
                enhanced_data['_quality_metadata'] = quality_assessment
            
            return enhanced_data
            
        except Exception as e:
            logging.error(f"Error enhancing data with quality assessment: {e}")
            return data

    def enhance_news_with_intelligence(self, news_articles: List[Dict]) -> Dict:
        """Enhance news with advanced intelligence analysis"""
        try:
            # Perform news intelligence analysis
            news_analysis = self.news_intelligence.analyze_news_quality(news_articles)
            
            # Store analysis
            self.enhanced_state['news_intelligence_analysis'] = news_analysis
            self.enhanced_state['last_intelligence_update'] = datetime.now().isoformat()
            
            # Generate intelligence insights
            insights = self._generate_news_insights(news_analysis)
            self.enhanced_state['intelligence_insights'].extend(insights)
            
            # Enhance news articles with intelligence metadata
            enhanced_articles = []
            for article in news_articles:
                enhanced_article = article.copy()
                
                # Add sentiment and impact analysis
                sentiment = self.news_intelligence._analyze_sentiment_advanced(article)
                impact = self.news_intelligence._analyze_impact_advanced(article)
                quality = self.news_intelligence._analyze_single_article(article)
                
                enhanced_article['_intelligence_metadata'] = {
                    'sentiment': sentiment,
                    'impact': impact,
                    'quality': quality,
                    'analysis_timestamp': datetime.now().isoformat()
                }
                
                enhanced_articles.append(enhanced_article)
            
            return {
                'enhanced_articles': enhanced_articles,
                'intelligence_analysis': news_analysis,
                'insights': insights
            }
            
        except Exception as e:
            logging.error(f"Error enhancing news with intelligence: {e}")
            return {'enhanced_articles': news_articles, 'error': str(e)}

    def perform_cross_market_analysis(self, all_market_data: Dict) -> Dict:
        """Perform comprehensive cross-market intelligence analysis"""
        try:
            # Perform cross-market analysis
            cross_analysis = self.cross_market_intelligence.analyze_market_relationships(all_market_data)
            
            # Store analysis
            self.enhanced_state['cross_market_analysis'] = cross_analysis
            
            # Generate strategic insights
            strategic_insights = self._generate_strategic_insights(cross_analysis)
            self.enhanced_state['intelligence_insights'].extend(strategic_insights)
            
            return {
                'cross_market_analysis': cross_analysis,
                'strategic_insights': strategic_insights,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error performing cross-market analysis: {e}")
            return {'error': str(e)}

    def get_enhanced_dashboard_data(self) -> Dict:
        """Get comprehensive dashboard data with quality and intelligence metrics"""
        try:
            dashboard_data = {
                'quality_overview': self._get_quality_overview(),
                'intelligence_summary': self._get_intelligence_summary(),
                'alerts_and_insights': self._get_alerts_and_insights(),
                'performance_metrics': self._get_performance_metrics(),
                'recommendations': self._get_recommendations()
            }
            
            return dashboard_data
            
        except Exception as e:
            logging.error(f"Error generating enhanced dashboard data: {e}")
            return {'error': str(e)}

    def _generate_news_insights(self, news_analysis: Dict) -> List[Dict]:
        """Generate actionable insights from news analysis"""
        insights = []
        
        try:
            # Sentiment insights
            sentiment_analysis = news_analysis.get('sentiment_analysis', {})
            if sentiment_analysis:
                dominant_sentiment = sentiment_analysis.get('dominant_sentiment', 'neutral')
                sentiment_score = sentiment_analysis.get('sentiment_score', 0)
                
                if abs(sentiment_score) > 0.5:
                    insights.append({
                        'type': 'sentiment',
                        'priority': 'high' if abs(sentiment_score) > 0.7 else 'medium',
                        'message': f"Strong {dominant_sentiment} sentiment detected in news (score: {sentiment_score:.2f})",
                        'implication': f"Market may trend {'positively' if sentiment_score > 0 else 'negatively'} based on news sentiment",
                        'timestamp': datetime.now().isoformat()
                    })
            
            # Impact insights
            impact_analysis = news_analysis.get('impact_analysis', {})
            if impact_analysis:
                dominant_impact = impact_analysis.get('dominant_impact', 'low')
                if dominant_impact in ['high', 'critical']:
                    insights.append({
                        'type': 'impact',
                        'priority': 'high',
                        'message': f"High-impact news events detected",
                        'implication': "Increased market volatility expected",
                        'timestamp': datetime.now().isoformat()
                    })
            
            # Source quality insights
            source_analysis = news_analysis.get('source_analysis', {})
            if source_analysis:
                weighted_credibility = source_analysis.get('weighted_credibility', 0)
                if weighted_credibility > 0.9:
                    insights.append({
                        'type': 'source_quality',
                        'priority': 'medium',
                        'message': f"High-quality news sources (credibility: {weighted_credibility:.2f})",
                        'implication': "News analysis reliability is high",
                        'timestamp': datetime.now().isoformat()
                    })
            
            return insights
            
        except Exception as e:
            logging.error(f"Error generating news insights: {e}")
            return []

    def _generate_strategic_insights(self, cross_analysis: Dict) -> List[Dict]:
        """Generate strategic insights from cross-market analysis"""
        insights = []
        
        try:
            # Correlation insights
            correlation_analysis = cross_analysis.get('correlation_analysis', {})
            if correlation_analysis:
                strongest_correlation = correlation_analysis.get('strongest_correlation')
                if strongest_correlation:
                    correlation_pair, correlation_data = strongest_correlation
                    insights.append({
                        'type': 'correlation',
                        'priority': 'high',
                        'message': f"Strong correlation detected: {correlation_pair}",
                        'implication': f"Markets moving in {correlation_data['direction']} correlation",
                        'timestamp': datetime.now().isoformat()
                    })
            
            # Pattern insights
            pattern_recognition = cross_analysis.get('pattern_recognition', {})
            if pattern_recognition:
                dominant_pattern = pattern_recognition.get('dominant_pattern')
                confidence_score = pattern_recognition.get('confidence_score', 0)
                
                if dominant_pattern and confidence_score > 70:
                    insights.append({
                        'type': 'pattern',
                        'priority': 'high',
                        'message': f"Market pattern identified: {dominant_pattern}",
                        'implication': f"Pattern confidence: {confidence_score:.1f}%",
                        'timestamp': datetime.now().isoformat()
                    })
            
            # Market regime insights
            market_regime = cross_analysis.get('market_regime', {})
            if market_regime:
                regime = market_regime.get('regime', 'unknown')
                risk_sentiment = market_regime.get('risk_sentiment', 'neutral')
                
                insights.append({
                    'type': 'market_regime',
                    'priority': 'medium',
                    'message': f"Market regime: {regime}, Risk sentiment: {risk_sentiment}",
                    'implication': f"Adjust strategy for {regime} environment",
                    'timestamp': datetime.now().isoformat()
                })
            
            return insights
            
        except Exception as e:
            logging.error(f"Error generating strategic insights: {e}")
            return []

    def _get_quality_overview(self) -> Dict:
        """Get overview of data quality across all data types"""
        quality_scores = self.enhanced_state.get('data_quality_scores', {})
        
        if not quality_scores:
            return {'overall_quality': 'unknown', 'message': 'No quality data available'}
        
        # Calculate overall quality
        scores = [score['overall_score'] for score in quality_scores.values()]
        overall_quality = sum(scores) / len(scores) if scores else 0
        
        # Categorize quality
        if overall_quality >= 0.9:
            quality_level = 'excellent'
        elif overall_quality >= 0.75:
            quality_level = 'good'
        elif overall_quality >= 0.6:
            quality_level = 'acceptable'
        else:
            quality_level = 'needs_improvement'
        
        return {
            'overall_quality': quality_level,
            'overall_score': overall_quality,
            'data_type_scores': {dt: score['overall_score'] for dt, score in quality_scores.items()},
            'lowest_quality': min(quality_scores.items(), key=lambda x: x[1]['overall_score']) if quality_scores else None
        }

    def _get_intelligence_summary(self) -> Dict:
        """Get summary of intelligence analysis"""
        news_analysis = self.enhanced_state.get('news_intelligence_analysis', {})
        cross_market = self.enhanced_state.get('cross_market_analysis', {})
        
        summary = {
            'news_intelligence': {
                'total_articles': news_analysis.get('total_articles', 0),
                'overall_quality': news_analysis.get('overall_quality_score', 0),
                'dominant_sentiment': news_analysis.get('sentiment_analysis', {}).get('dominant_sentiment', 'neutral'),
                'dominant_impact': news_analysis.get('impact_analysis', {}).get('dominant_impact', 'low')
            },
            'cross_market': {
                'correlations_detected': len(cross_market.get('correlation_analysis', {}).get('correlations', {})),
                'dominant_pattern': cross_market.get('pattern_recognition', {}).get('dominant_pattern', 'none'),
                'market_regime': cross_market.get('market_regime', {}).get('regime', 'unknown'),
                'signal_count': cross_market.get('cross_asset_signals', {}).get('signal_count', 0)
            }
        }
        
        return summary

    def _get_alerts_and_insights(self) -> Dict:
        """Get current alerts and insights"""
        alerts = self.enhanced_state.get('quality_alerts', [])
        insights = self.enhanced_state.get('intelligence_insights', [])
        
        # Sort by priority and timestamp
        high_priority_alerts = [a for a in alerts if a.get('severity') in ['critical', 'high']]
        high_priority_insights = [i for i in insights if i.get('priority') == 'high']
        
        return {
            'critical_alerts': high_priority_alerts[-5:],  # Last 5 critical alerts
            'key_insights': high_priority_insights[-5:],   # Last 5 key insights
            'total_alerts': len(alerts),
            'total_insights': len(insights)
        }

    def _get_performance_metrics(self) -> Dict:
        """Get performance metrics for the enhanced system"""
        return {
            'last_quality_check': self.enhanced_state.get('last_quality_check'),
            'last_intelligence_update': self.enhanced_state.get('last_intelligence_update'),
            'quality_engine_status': 'active',
            'intelligence_engine_status': 'active',
            'cross_market_engine_status': 'active' if self.cross_market_intelligence.model else 'limited'
        }

    def _get_recommendations(self) -> List[Dict]:
        """Get actionable recommendations based on analysis"""
        recommendations = []
        
        # Quality-based recommendations
        quality_overview = self._get_quality_overview()
        if quality_overview['overall_score'] < 0.7:
            recommendations.append({
                'type': 'quality_improvement',
                'priority': 'high',
                'action': 'Improve data quality',
                'details': 'Consider adding more reliable data sources or improving data validation'
            })
        
        # Intelligence-based recommendations
        intelligence_summary = self._get_intelligence_summary()
        cross_market = intelligence_summary.get('cross_market', {})
        
        if cross_market.get('signal_count', 0) > 3:
            recommendations.append({
                'type': 'trading_opportunity',
                'priority': 'medium',
                'action': 'Review cross-asset signals',
                'details': 'Multiple trading signals detected across markets'
            })
        
        # News-based recommendations
        news_intel = intelligence_summary.get('news_intelligence', {})
        if news_intel.get('dominant_impact') in ['high', 'critical']:
            recommendations.append({
                'type': 'risk_management',
                'priority': 'high',
                'action': 'Monitor high-impact news',
                'details': 'High-impact news events may increase market volatility'
            })
        
        return recommendations

# ==============================================================================
# SECTION 2: ENHANCED UI COMPONENTS
# ==============================================================================
class EnhancedUIComponents:
    """Enhanced UI components for quality and intelligence display"""
    
    @staticmethod
    def create_quality_indicator(parent, quality_score: float, quality_level: str):
        """Create a visual quality indicator widget"""
        import tkinter as tk
        from tkinter import ttk
        
        frame = ttk.Frame(parent)
        
        # Quality score label
        score_label = ttk.Label(frame, text=f"Quality: {quality_score:.2f}")
        score_label.pack(side=tk.LEFT, padx=5)
        
        # Quality level indicator
        color_map = {
            'excellent': 'green',
            'good': 'blue',
            'acceptable': 'orange',
            'poor': 'red',
            'critical': 'darkred'
        }
        
        level_label = ttk.Label(
            frame, 
            text=quality_level.upper(),
            foreground=color_map.get(quality_level, 'black')
        )
        level_label.pack(side=tk.LEFT, padx=5)
        
        return frame

    @staticmethod
    def create_intelligence_summary(parent, intelligence_data: Dict):
        """Create an intelligence summary widget"""
        import tkinter as tk
        from tkinter import ttk
        
        frame = ttk.LabelFrame(parent, text="Market Intelligence")
        
        # News intelligence
        news_frame = ttk.Frame(frame)
        news_frame.pack(fill=tk.X, padx=5, pady=2)
        
        news_intel = intelligence_data.get('news_intelligence', {})
        ttk.Label(news_frame, text=f"News: {news_intel.get('total_articles', 0)} articles").pack(side=tk.LEFT)
        ttk.Label(news_frame, text=f"Sentiment: {news_intel.get('dominant_sentiment', 'neutral')}").pack(side=tk.RIGHT)
        
        # Cross-market intelligence
        market_frame = ttk.Frame(frame)
        market_frame.pack(fill=tk.X, padx=5, pady=2)
        
        cross_market = intelligence_data.get('cross_market', {})
        ttk.Label(market_frame, text=f"Correlations: {cross_market.get('correlations_detected', 0)}").pack(side=tk.LEFT)
        ttk.Label(market_frame, text=f"Regime: {cross_market.get('market_regime', 'unknown')}").pack(side=tk.RIGHT)
        
        return frame

    @staticmethod
    def create_alerts_panel(parent, alerts_data: Dict):
        """Create an alerts and insights panel"""
        import tkinter as tk
        from tkinter import ttk, scrolledtext
        
        frame = ttk.LabelFrame(parent, text="Alerts & Insights")
        
        # Alerts section
        alerts_text = scrolledtext.ScrolledText(frame, height=6, wrap=tk.WORD)
        alerts_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add critical alerts
        critical_alerts = alerts_data.get('critical_alerts', [])
        for alert in critical_alerts[-3:]:  # Show last 3
            alerts_text.insert(tk.END, f"🚨 {alert.get('message', '')}\n")
        
        # Add key insights
        key_insights = alerts_data.get('key_insights', [])
        for insight in key_insights[-3:]:  # Show last 3
            alerts_text.insert(tk.END, f"💡 {insight.get('message', '')}\n")
        
        alerts_text.config(state=tk.DISABLED)
        
        return frame
