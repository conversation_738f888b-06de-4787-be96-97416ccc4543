# TiT Health App 1.0.1: Advanced Health & Biotech Intelligence Suite
# Version: 1.0.1 (Health & Biotech Edition)
#
# Copyright (C) 2025 Ng<PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
# Date: 2025-06-17
#
# This software is the proprietary work of <PERSON><PERSON><PERSON>.
# Unauthorized copying, distribution, or modification of this software,
# via any medium, is strictly prohibited without explicit written permission.
#
# Description:
# Advanced Health & Biotech Intelligence Suite focusing on pharmaceutical companies,
# biotech stocks, medical devices, drug development pipeline, FDA approvals,
# clinical trials, and AI-powered healthcare market insights.

# ==============================================================================
# SECTION 1: IMPORTS
# ==============================================================================
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import threading
import os
import logging
import json
from datetime import datetime, timedelta
import time
from decimal import Decimal, ROUND_HALF_UP
import webbrowser
import random
import sys

# Third-Party Library Imports
from ttkthemes import ThemedTk
import requests
import pandas as pd
import yfinance as yf
import feedparser
from bs4 import BeautifulSoup
import re
import google.generativeai as genai

# Charting Library Imports
import matplotlib
matplotlib.use('TkAgg')
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import mplfinance as mpf

# ==============================================================================
# SECTION 2: HEALTH & BIOTECH CONFIGURATION
# ==============================================================================
class HealthConfig:
    """Configuration for Health & Biotech Application"""
    
    # --- Logging Configuration ---
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = '%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s'
    LOG_FILE = "tit_health_app_1.0.1.log"

    # --- API Key Configuration ---
    GOOGLE_API_KEY = "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og"

    # --- Major Pharmaceutical Companies ---
    PHARMA_COMPANIES = {
        'Big Pharma': {
            'USA': ['JNJ', 'PFE', 'MRK', 'ABBV', 'LLY', 'BMY', 'AMGN', 'GILD', 'BIIB', 'REGN'],
            'Europe': ['NVO', 'ASML', 'ROG.SW', 'NVS', 'AZN', 'GSK', 'SNY', 'BAYRY', 'TEVA'],
            'Japan': ['4502.T', '4503.T', '4568.T', '4151.T']  # Takeda, Astellas, Daiichi Sankyo, Kyowa Kirin
        },
        'Biotech': {
            'Large Cap': ['AMGN', 'GILD', 'BIIB', 'REGN', 'VRTX', 'ILMN', 'MRNA', 'BNTX'],
            'Mid Cap': ['ALNY', 'BMRN', 'TECH', 'INCY', 'EXAS', 'ARWR', 'CRSP', 'EDIT'],
            'Small Cap': ['FOLD', 'BEAM', 'NTLA', 'BLUE', 'SAGE', 'IONS', 'ACAD', 'PTCT']
        },
        'Medical Devices': ['MDT', 'ABT', 'TMO', 'DHR', 'SYK', 'BSX', 'EW', 'HOLX', 'ISRG', 'DXCM'],
        'Diagnostics': ['TMO', 'DHR', 'ILMN', 'QGEN', 'IQV', 'LH', 'DGX', 'EXAS', 'VEEV'],
        'Digital Health': ['TDOC', 'VEEV', 'DOCU', 'AMWL', 'ONEM', 'HIMS']
    }

    # --- Health Sector ETFs ---
    HEALTH_ETFS = {
        'XLV': 'Health Care Select Sector SPDR',
        'VHT': 'Vanguard Health Care ETF',
        'IHI': 'iShares Medical Devices ETF',
        'IBB': 'iShares Biotechnology ETF',
        'XBI': 'SPDR S&P Biotech ETF',
        'ARKG': 'ARK Genomic Revolution ETF',
        'GNOM': 'Global X Genomics & Biotechnology ETF',
        'SBIO': 'ALPS Medical Breakthroughs ETF',
        'CURE': 'Direxion Daily Healthcare Bull 3X',
        'RXL': 'ProShares Ultra Health Care'
    }

    # --- Drug Development Stages ---
    DEVELOPMENT_STAGES = {
        'Preclinical': 'Laboratory and animal testing',
        'Phase I': 'Safety testing in small groups (20-100 people)',
        'Phase II': 'Efficacy testing in larger groups (100-300 people)',
        'Phase III': 'Large-scale testing (1,000-3,000 people)',
        'FDA Review': 'Regulatory review and approval process',
        'Phase IV': 'Post-market surveillance'
    }

    # --- Therapeutic Areas ---
    THERAPEUTIC_AREAS = {
        'Oncology': ['Cancer', 'Immunotherapy', 'CAR-T', 'Checkpoint Inhibitors'],
        'Neurology': ['Alzheimer\'s', 'Parkinson\'s', 'Multiple Sclerosis', 'ALS'],
        'Immunology': ['Rheumatoid Arthritis', 'Psoriasis', 'IBD', 'Lupus'],
        'Infectious Disease': ['COVID-19', 'HIV', 'Hepatitis', 'Antimicrobials'],
        'Rare Disease': ['Orphan Drugs', 'Gene Therapy', 'Enzyme Replacement'],
        'Cardiovascular': ['Heart Disease', 'Hypertension', 'Cholesterol'],
        'Diabetes': ['Type 1', 'Type 2', 'Insulin', 'GLP-1'],
        'Mental Health': ['Depression', 'Anxiety', 'Schizophrenia', 'ADHD']
    }

    # --- Health News Sources ---
    HEALTH_NEWS_FEEDS = [
        # Primary source - The Globe and Mail (as preferred)
        'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/',
        'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/',
        
        # Medical and biotech news
        'https://feeds.reuters.com/reuters/health',
        'https://www.cnbc.com/id/10000108/device/rss/rss.html',  # Health
        'https://feeds.bloomberg.com/healthcare/news.rss',
        
        # Specialized biotech news
        'https://www.biopharmadive.com/feeds/news/',
        'https://www.fiercebiotech.com/rss/xml',
        'https://www.fiercepharma.com/rss/xml',
        'https://www.bioworld.com/rss/news.xml',
        'https://www.genengnews.com/rss/',
        
        # FDA and regulatory
        'https://www.fda.gov/about-fda/contact-fda/stay-informed/rss-feeds/drug-approvals-and-databases/rss.xml',
        'https://www.ema.europa.eu/en/rss_feeds',
        
        # Medical journals and research
        'https://www.nature.com/nm.rss',
        'https://www.nejm.org/action/showFeed?type=etoc&feed=rss',
        'https://www.thelancet.com/rssfeed/lancet_current.xml',
        
        # Digital health
        'https://www.mobihealthnews.com/feed',
        'https://www.healthcareitnews.com/rss.xml'
    ]

    # --- Cache Configuration ---
    CACHE_EXPIRATION_SECONDS = {
        "health_stocks": 60,     # 1 minute
        "biotech_data": 60,      # 1 minute
        "health_news": 300,      # 5 minutes
        "fda_approvals": 3600,   # 1 hour
        "clinical_trials": 3600, # 1 hour
        "drug_pipeline": 1800    # 30 minutes
    }

    # --- Health-themed UI Configuration ---
    THEME = 'arc'  # Modern theme
    FONT_FAMILY = "Segoe UI"
    FONT_SIZE_NORMAL = 10
    FONT_SIZE_LARGE = 12
    FONT_SIZE_HEADER = 14
    
    # Medical-themed Color Palette
    COLORS = {
        'primary': '#2E8B57',        # Sea Green (Medical)
        'secondary': '#4682B4',      # Steel Blue
        'accent': '#FF6347',         # Tomato
        'success': '#32CD32',        # Lime Green
        'danger': '#DC143C',         # Crimson
        'warning': '#FF8C00',        # Dark Orange
        'info': '#1E90FF',           # Dodger Blue
        'medical': '#2E8B57',        # Sea Green
        'biotech': '#9370DB',        # Medium Purple
        'pharma': '#4169E1',         # Royal Blue
        'device': '#20B2AA',         # Light Sea Green
        'diagnostic': '#FF69B4',     # Hot Pink
        'surface': '#FFFFFF',        # White
        'background': '#F0F8FF',     # Alice Blue (medical tint)
        'text_primary': '#212121',   # Dark Gray
        'text_secondary': '#757575'  # Medium Gray
    }
    
    UI_PADDING = 8

# Setup Logging
logging.basicConfig(
    level=HealthConfig.LOG_LEVEL,
    format=HealthConfig.LOG_FORMAT,
    handlers=[
        logging.FileHandler(HealthConfig.LOG_FILE, mode='w'),
        logging.StreamHandler()
    ]
)
logging.info("TiT Health App 1.0.1 Starting...")
logging.info("Copyright (C) 2025 Nguyen Le Vinh Quang. All rights reserved.")

# ==============================================================================
# SECTION 3: CORE SERVICES
# ==============================================================================
class HealthCacheService:
    """Enhanced cache service for health market data"""
    def __init__(self):
        self._cache = {}
        logging.info("HealthCacheService initialized.")

    def get(self, key):
        if key not in self._cache:
            return None
        data, timestamp = self._cache[key]
        cache_duration = HealthConfig.CACHE_EXPIRATION_SECONDS.get(key, 60)
        if time.time() - timestamp < cache_duration:
            logging.info(f"Cache hit for key: {key}")
            return data
        else:
            logging.info(f"Cache expired for key: {key}")
            del self._cache[key]
            return None

    def set(self, key, data):
        logging.info(f"Caching data for key: {key}")
        self._cache[key] = (data, time.time())

class HealthDataService:
    """Advanced health market data service"""
    def __init__(self, cache_service):
        self.cache = cache_service
        logging.info("HealthDataService initialized with comprehensive healthcare coverage.")

    def get_health_stocks_data(self, category=None):
        """Get data for health and biotech companies by category."""
        cache_key = f"health_stocks_{category}" if category else "health_stocks"
        cached_data = self.cache.get(cache_key)
        if cached_data: return cached_data
        
        logging.info(f"Fetching health stocks data for {category or 'all categories'}...")
        health_data = {}
        
        try:
            categories_to_fetch = [category] if category else HealthConfig.PHARMA_COMPANIES.keys()
            
            for category_name in categories_to_fetch:
                if category_name not in HealthConfig.PHARMA_COMPANIES:
                    continue
                    
                category_stocks = {}
                
                # Handle nested structure for some categories
                if isinstance(HealthConfig.PHARMA_COMPANIES[category_name], dict):
                    for subcategory, symbols in HealthConfig.PHARMA_COMPANIES[category_name].items():
                        for symbol in symbols:
                            try:
                                ticker = yf.Ticker(symbol)
                                info = ticker.info
                                hist = ticker.history(period="1d")
                                
                                if not hist.empty:
                                    current_price = hist['Close'].iloc[-1]
                                    prev_close = info.get('previousClose', current_price)
                                    change = current_price - prev_close
                                    change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                                    
                                    category_stocks[symbol] = {
                                        'name': info.get('longName', symbol),
                                        'symbol': symbol,
                                        'subcategory': subcategory,
                                        'price': current_price,
                                        'change': change,
                                        'change_percent': change_pct,
                                        'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                                        'market_cap': info.get('marketCap', 0),
                                        'sector': info.get('sector', 'Healthcare'),
                                        'industry': info.get('industry', 'Biotechnology')
                                    }
                            except Exception as e:
                                logging.warning(f"Error fetching data for {symbol}: {e}")
                                continue
                else:
                    # Handle flat structure
                    for symbol in HealthConfig.PHARMA_COMPANIES[category_name]:
                        try:
                            ticker = yf.Ticker(symbol)
                            info = ticker.info
                            hist = ticker.history(period="1d")
                            
                            if not hist.empty:
                                current_price = hist['Close'].iloc[-1]
                                prev_close = info.get('previousClose', current_price)
                                change = current_price - prev_close
                                change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                                
                                category_stocks[symbol] = {
                                    'name': info.get('longName', symbol),
                                    'symbol': symbol,
                                    'price': current_price,
                                    'change': change,
                                    'change_percent': change_pct,
                                    'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                                    'market_cap': info.get('marketCap', 0),
                                    'sector': info.get('sector', 'Healthcare'),
                                    'industry': info.get('industry', 'Biotechnology')
                                }
                        except Exception as e:
                            logging.warning(f"Error fetching data for {symbol}: {e}")
                            continue
                
                if category_stocks:
                    health_data[category_name] = category_stocks
            
            self.cache.set(cache_key, health_data)
            return health_data
            
        except Exception as e:
            logging.error(f"Error fetching health stocks data: {e}")
            return {}

    def get_health_etfs_data(self):
        """Get data for health sector ETFs."""
        cached_data = self.cache.get("health_etfs")
        if cached_data: return cached_data
        
        logging.info("Fetching health sector ETFs data...")
        etfs_data = {}
        
        try:
            for symbol, name in HealthConfig.HEALTH_ETFS.items():
                try:
                    ticker = yf.Ticker(symbol)
                    info = ticker.info
                    hist = ticker.history(period="1d")
                    
                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]
                        prev_close = info.get('previousClose', current_price)
                        change = current_price - prev_close
                        change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                        
                        etfs_data[symbol] = {
                            'name': name,
                            'symbol': symbol,
                            'price': current_price,
                            'change': change,
                            'change_percent': change_pct,
                            'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                            'assets_under_mgmt': info.get('totalAssets', 0),
                            'expense_ratio': info.get('annualReportExpenseRatio', 0)
                        }
                except Exception as e:
                    logging.warning(f"Error fetching data for {symbol}: {e}")
                    continue
            
            self.cache.set("health_etfs", etfs_data)
            return etfs_data
            
        except Exception as e:
            logging.error(f"Error fetching health ETFs data: {e}")
            return {}

# ==============================================================================
# SECTION 4: AI SERVICE FOR HEALTH MARKET ANALYSIS
# ==============================================================================
class HealthAIService:
    """AI service for health market analysis and predictions"""
    def __init__(self):
        if HealthConfig.GOOGLE_API_KEY:
            try:
                genai.configure(api_key=HealthConfig.GOOGLE_API_KEY)
                self.model = genai.GenerativeModel('gemini-pro')
                logging.info("HealthAIService initialized with Gemini Pro.")
            except Exception as e:
                logging.error(f"Failed to initialize AI service: {e}")
                self.model = None
        else:
            self.model = None
            logging.warning("No AI API key provided. AI features disabled.")

    def generate_health_market_analysis(self, health_data, etfs_data, news_data):
        """Generate comprehensive health market analysis."""
        if not self.model:
            return "AI analysis unavailable. Please configure API key."

        try:
            health_summary = self._prepare_health_data_summary(health_data)
            etfs_summary = self._prepare_etfs_summary(etfs_data)
            news_summary = self._prepare_news_summary(news_data)

            prompt = f"""
            As an expert healthcare and biotech analyst, provide comprehensive analysis of the current health market situation.

            HEALTH & BIOTECH COMPANIES PERFORMANCE:
            {health_summary}

            HEALTH SECTOR ETFS:
            {etfs_summary}

            RECENT NEWS HIGHLIGHTS:
            {news_summary}

            Please provide detailed analysis covering:

            ## 🧬 HEALTH & BIOTECH MARKET OVERVIEW
            **Current Market Sentiment**: [Bullish/Bearish/Neutral] with confidence level

            **Key Drivers**:
            - FDA approvals and regulatory developments
            - Clinical trial results and pipeline progress
            - Healthcare policy and reimbursement changes
            - Technological breakthroughs and innovation
            - Demographic trends and aging population

            ## 💊 PHARMACEUTICAL SECTOR ANALYSIS
            **Big Pharma Performance**:
            - Revenue growth and margin trends
            - Patent cliff impacts and generic competition
            - M&A activity and strategic partnerships
            - Dividend sustainability and shareholder returns

            **Pipeline Analysis**:
            - Late-stage clinical trials and approval timelines
            - Breakthrough therapy designations
            - Orphan drug opportunities
            - Biosimilar competition threats

            ## 🧪 BIOTECH SECTOR DYNAMICS
            **Innovation Trends**:
            - Gene therapy and cell therapy advances
            - Immunotherapy and precision medicine
            - Digital health and AI applications
            - Personalized medicine developments

            **Funding Environment**:
            - IPO market conditions for biotech
            - Venture capital investment trends
            - Public market valuations vs private
            - Risk appetite for early-stage companies

            ## 🏥 MEDICAL DEVICE & DIAGNOSTICS
            **Technology Adoption**:
            - Minimally invasive surgical technologies
            - AI-powered diagnostic tools
            - Remote monitoring and telemedicine
            - Robotic surgery and automation

            **Market Expansion**:
            - Emerging market penetration
            - Aging population device demand
            - Cost reduction and efficiency gains
            - Regulatory pathway improvements

            ## 📊 INVESTMENT OPPORTUNITIES
            **Sector Rotation Analysis**:
            - Healthcare vs other defensive sectors
            - Growth vs value opportunities
            - Large cap stability vs small cap innovation
            - Geographic diversification benefits

            **Thematic Investments**:
            - Aging population demographics
            - Obesity and metabolic diseases
            - Mental health and neurology
            - Rare disease treatments

            ## 🔮 MARKET OUTLOOK
            **Short-term (1-3 months)**:
            - Earnings season expectations
            - FDA approval catalysts
            - Conference presentations and data readouts
            - Policy developments and elections

            **Long-term (6-24 months)**:
            - Healthcare reform implications
            - Technology disruption timeline
            - Demographic trend acceleration
            - Global health initiatives impact

            **Key Catalysts to Watch**:
            - Major clinical trial readouts
            - FDA advisory committee meetings
            - Healthcare policy announcements
            - Breakthrough technology approvals

            Provide specific, actionable insights with confidence levels and investment implications.
            """

            response = self.model.generate_content(prompt)
            return response.text

        except Exception as e:
            logging.error(f"Error generating health market analysis: {e}")
            return f"Error generating analysis: {e}"

    def _prepare_health_data_summary(self, health_data):
        """Prepare health companies data summary for AI analysis."""
        if not health_data:
            return "No health companies data available."

        summary = []
        for category, companies in health_data.items():
            summary.append(f"\n{category}:")
            if isinstance(companies, dict):
                for symbol, data in list(companies.items())[:3]:
                    change_direction = "↑" if data['change'] >= 0 else "↓"
                    summary.append(f"- {data['name']}: ${data['price']:.2f} {change_direction} {data['change_percent']:.2f}%")

        return "\n".join(summary)

    def _prepare_etfs_summary(self, etfs_data):
        """Prepare health ETFs data summary for AI analysis."""
        if not etfs_data:
            return "No health ETFs data available."

        summary = []
        for symbol, data in etfs_data.items():
            change_direction = "↑" if data['change'] >= 0 else "↓"
            summary.append(f"- {data['name']}: ${data['price']:.2f} {change_direction} {data['change_percent']:.2f}%")

        return "\n".join(summary)

    def _prepare_news_summary(self, news_data):
        """Prepare news data summary for AI analysis."""
        if not news_data:
            return "No recent news available."

        summary = []
        for article in news_data[:10]:
            impact_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(article.get('impact_level', 'medium'), "🟡")
            summary.append(f"{impact_emoji} {article['title']} ({article['source']['name']})")

        return "\n".join(summary)

# ==============================================================================
# SECTION 5: MAIN HEALTH APPLICATION
# ==============================================================================
class TiTHealthApp:
    """Main Health Market Application"""
    def __init__(self, root):
        self.root = root
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        self.root.title("✨ TiT Health App 1.0.1 - OMNIVERSAL TRANSCENDENT Advanced Health & Biotech Intelligence Suite at ANOTHER HIGHEST LEVEL ✨")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        logging.info("TiT Health App main initialization started...")

        # Initialize services
        self.cache_service = HealthCacheService()
        self.data_service = HealthDataService(self.cache_service)
        self.ai_service = HealthAIService()

        # App state
        self.app_state = {
            'health_stocks': {},
            'health_etfs': {},
            'health_news': [],
            'analysis_text': ""
        }

        # UI components
        self.widgets = {}
        self.status_var = tk.StringVar(value="Ready - TiT Health App 1.0.1")

        # Setup UI
        self.setup_ui()

        # Initial data load
        self.refresh_all_data()

        logging.info("TiT Health App 1.0.1 initialized successfully.")

    def _on_closing(self):
        """Handle application closing."""
        logging.info("Health application closing...")
        self.root.destroy()

    def setup_ui(self):
        """Setup the main UI components."""
        # Main style configuration
        self.style = ttk.Style()
        self.style.configure("TLabel", font=(HealthConfig.FONT_FAMILY, HealthConfig.FONT_SIZE_NORMAL))
        self.style.configure("TButton", font=(HealthConfig.FONT_FAMILY, HealthConfig.FONT_SIZE_NORMAL))

        # Main container
        self.main_frame = ttk.Frame(self.root, padding=HealthConfig.UI_PADDING)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Top control bar
        self.control_frame = ttk.Frame(self.main_frame)
        self.control_frame.pack(fill=tk.X, pady=(0, 10))

        # Refresh button
        self.refresh_btn = ttk.Button(
            self.control_frame,
            text="Refresh All",
            command=self.refresh_all_data
        )
        self.refresh_btn.pack(side=tk.LEFT, padx=5)

        # Author box
        author_frame = ttk.LabelFrame(self.control_frame, text="")
        author_frame.pack(side=tk.LEFT, padx=(20, 5))

        author_label = ttk.Label(
            author_frame,
            text="Anh Quang",
            font=(HealthConfig.FONT_FAMILY, HealthConfig.FONT_SIZE_NORMAL, 'bold'),
            foreground=HealthConfig.COLORS['primary']
        )
        author_label.pack(padx=10, pady=2)

        # Status bar
        self.status_bar = ttk.Label(self.main_frame, textvariable=self.status_var, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))

        # Tab control
        self.tab_control = ttk.Notebook(self.main_frame)
        self.tab_control.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self.dashboard_tab = ttk.Frame(self.tab_control)
        self.companies_tab = ttk.Frame(self.tab_control)
        self.etfs_tab = ttk.Frame(self.tab_control)
        self.news_tab = ttk.Frame(self.tab_control)
        self.analysis_tab = ttk.Frame(self.tab_control)

        # Add tabs to notebook
        self.tab_control.add(self.dashboard_tab, text="Health Dashboard")
        self.tab_control.add(self.companies_tab, text="Health Companies")
        self.tab_control.add(self.etfs_tab, text="Health ETFs")
        self.tab_control.add(self.news_tab, text="Health News")
        self.tab_control.add(self.analysis_tab, text="AI Analysis")

        # Setup individual tab contents
        self.setup_dashboard_tab()
        self.setup_companies_tab()
        self.setup_etfs_tab()
        self.setup_news_tab()
        self.setup_analysis_tab()

        logging.info("Health app UI setup complete")

    def setup_dashboard_tab(self):
        """Setup the health dashboard tab."""
        # Health overview section
        overview_frame = ttk.LabelFrame(self.dashboard_tab, text="Health Market Overview")
        overview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for overview
        columns = ("Sector", "Companies", "Avg Change", "Top Performer", "Performance")
        self.overview_tree = ttk.Treeview(overview_frame, columns=columns, show="headings", height=8)

        # Configure columns
        for col in columns:
            self.overview_tree.heading(col, text=col)
            self.overview_tree.column(col, width=120)

        # Add scrollbar
        overview_scrollbar = ttk.Scrollbar(overview_frame, orient="vertical", command=self.overview_tree.yview)
        self.overview_tree.configure(yscrollcommand=overview_scrollbar.set)

        # Pack widgets
        self.overview_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        overview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Store widget reference
        self.widgets['overview_tree'] = self.overview_tree

    def setup_companies_tab(self):
        """Setup the health companies tab."""
        # Companies section
        companies_frame = ttk.LabelFrame(self.companies_tab, text="Health & Biotech Companies")
        companies_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for companies
        columns = ("Company", "Symbol", "Category", "Price", "Change", "Change %", "Market Cap")
        self.companies_tree = ttk.Treeview(companies_frame, columns=columns, show="headings", height=15)

        # Configure columns
        for col in columns:
            self.companies_tree.heading(col, text=col)
            self.companies_tree.column(col, width=100)

        # Add scrollbar
        companies_scrollbar = ttk.Scrollbar(companies_frame, orient="vertical", command=self.companies_tree.yview)
        self.companies_tree.configure(yscrollcommand=companies_scrollbar.set)

        # Pack widgets
        self.companies_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        companies_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Store widget reference
        self.widgets['companies_tree'] = self.companies_tree

    def setup_etfs_tab(self):
        """Setup the health ETFs tab."""
        # ETFs section
        etfs_frame = ttk.LabelFrame(self.etfs_tab, text="Health Sector ETFs")
        etfs_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for ETFs
        columns = ("ETF", "Symbol", "Price", "Change", "Change %", "AUM", "Expense Ratio")
        self.etfs_tree = ttk.Treeview(etfs_frame, columns=columns, show="headings", height=12)

        # Configure columns
        for col in columns:
            self.etfs_tree.heading(col, text=col)
            self.etfs_tree.column(col, width=120)

        # Add scrollbar
        etfs_scrollbar = ttk.Scrollbar(etfs_frame, orient="vertical", command=self.etfs_tree.yview)
        self.etfs_tree.configure(yscrollcommand=etfs_scrollbar.set)

        # Pack widgets
        self.etfs_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        etfs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Store widget reference
        self.widgets['etfs_tree'] = self.etfs_tree

    def setup_news_tab(self):
        """Setup the health news tab."""
        # News section
        news_frame = ttk.LabelFrame(self.news_tab, text="Health & Biotech News")
        news_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrolled text widget for news
        self.news_text = scrolledtext.ScrolledText(news_frame, wrap=tk.WORD, height=20)
        self.news_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Configure text tags
        self.news_text.tag_configure('title', font=(HealthConfig.FONT_FAMILY, HealthConfig.FONT_SIZE_LARGE, 'bold'))
        self.news_text.tag_configure('source', font=(HealthConfig.FONT_FAMILY, HealthConfig.FONT_SIZE_NORMAL - 1), foreground='gray')
        self.news_text.tag_configure('high_impact', foreground=HealthConfig.COLORS['danger'])
        self.news_text.tag_configure('medium_impact', foreground=HealthConfig.COLORS['warning'])
        self.news_text.tag_configure('low_impact', foreground=HealthConfig.COLORS['success'])

        # Store widget reference
        self.widgets['news_text'] = self.news_text

    def setup_analysis_tab(self):
        """Setup the AI analysis tab."""
        # Control frame
        control_frame = ttk.Frame(self.analysis_tab)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # Generate analysis button
        generate_btn = ttk.Button(
            control_frame,
            text="🧬 Generate Health Market Analysis",
            command=self.generate_health_analysis_threaded
        )
        generate_btn.pack(side=tk.LEFT, padx=5)

        # Export button
        export_btn = ttk.Button(
            control_frame,
            text="📊 Export Analysis",
            command=self.export_analysis
        )
        export_btn.pack(side=tk.LEFT, padx=5)

        # Analysis text area
        analysis_frame = ttk.LabelFrame(self.analysis_tab, text="AI Health Market Analysis")
        analysis_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, wrap=tk.WORD, height=20)
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Store widget reference
        self.widgets['analysis_text'] = self.analysis_text

    def refresh_all_data(self):
        """Refresh all health market data."""
        def refresh_worker():
            try:
                self.status_var.set("Refreshing health market data...")

                # Fetch health stocks data
                health_stocks = self.data_service.get_health_stocks_data()
                self.app_state['health_stocks'] = health_stocks

                # Fetch health ETFs data
                health_etfs = self.data_service.get_health_etfs_data()
                self.app_state['health_etfs'] = health_etfs

                # Update UI
                self.root.after(0, self.update_overview_display)
                self.root.after(0, self.update_companies_display)
                self.root.after(0, self.update_etfs_display)

                self.status_var.set("Health market data refreshed successfully.")

            except Exception as e:
                logging.error(f"Error refreshing health data: {e}")
                self.status_var.set(f"Error refreshing data: {e}")

        threading.Thread(target=refresh_worker, daemon=True).start()

    def update_overview_display(self):
        """Update the overview display."""
        tree = self.widgets.get('overview_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add overview data
        for category, companies in self.app_state['health_stocks'].items():
            if isinstance(companies, dict) and companies:
                changes = [data['change_percent'] for data in companies.values() if isinstance(data, dict)]
                avg_change = sum(changes) / len(changes) if changes else 0

                # Find top performer
                top_performer = max(companies.items(), key=lambda x: x[1].get('change_percent', 0) if isinstance(x[1], dict) else 0)

                tree.insert('', 'end', values=(
                    category,
                    len(companies),
                    f"{avg_change:.2f}%",
                    top_performer[1].get('name', top_performer[0]) if isinstance(top_performer[1], dict) else top_performer[0],
                    f"{top_performer[1].get('change_percent', 0):.2f}%" if isinstance(top_performer[1], dict) else "N/A"
                ))

    def update_companies_display(self):
        """Update the companies display."""
        tree = self.widgets.get('companies_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add companies data
        for category, companies in self.app_state['health_stocks'].items():
            if isinstance(companies, dict):
                for symbol, data in companies.items():
                    if isinstance(data, dict):
                        change_color = 'green' if data['change'] >= 0 else 'red'
                        market_cap_str = f"${data['market_cap']/1e9:.1f}B" if data['market_cap'] > 0 else "N/A"

                        tree.insert('', 'end', values=(
                            data['name'][:25] + "..." if len(data['name']) > 25 else data['name'],
                            data['symbol'],
                            category,
                            f"${data['price']:.2f}",
                            f"${data['change']:.2f}",
                            f"{data['change_percent']:.2f}%",
                            market_cap_str
                        ), tags=(change_color,))

        # Configure tags for colors
        tree.tag_configure('green', foreground=HealthConfig.COLORS['success'])
        tree.tag_configure('red', foreground=HealthConfig.COLORS['danger'])

    def update_etfs_display(self):
        """Update the ETFs display."""
        tree = self.widgets.get('etfs_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add ETFs data
        for symbol, data in self.app_state['health_etfs'].items():
            change_color = 'green' if data['change'] >= 0 else 'red'
            aum_str = f"${data['assets_under_mgmt']/1e9:.1f}B" if data['assets_under_mgmt'] > 0 else "N/A"
            expense_str = f"{data['expense_ratio']:.2f}%" if data['expense_ratio'] > 0 else "N/A"

            tree.insert('', 'end', values=(
                data['name'][:30] + "..." if len(data['name']) > 30 else data['name'],
                data['symbol'],
                f"${data['price']:.2f}",
                f"${data['change']:.2f}",
                f"{data['change_percent']:.2f}%",
                aum_str,
                expense_str
            ), tags=(change_color,))

        # Configure tags for colors
        tree.tag_configure('green', foreground=HealthConfig.COLORS['success'])
        tree.tag_configure('red', foreground=HealthConfig.COLORS['danger'])

    def generate_health_analysis_threaded(self):
        """Generate health market analysis in a separate thread."""
        def analysis_worker():
            try:
                self.status_var.set("AI is analyzing health market data...")

                analysis = self.ai_service.generate_health_market_analysis(
                    self.app_state['health_stocks'],
                    self.app_state['health_etfs'],
                    self.app_state['health_news']
                )

                self.app_state['analysis_text'] = analysis
                self.root.after(0, self.update_analysis_display)
                self.status_var.set("Health market analysis generated successfully.")

            except Exception as e:
                logging.error(f"Error generating health analysis: {e}")
                self.status_var.set(f"Error generating analysis: {e}")

        threading.Thread(target=analysis_worker, daemon=True).start()

    def update_analysis_display(self):
        """Update the analysis display."""
        analysis_text = self.widgets.get('analysis_text')
        if analysis_text and self.app_state['analysis_text']:
            analysis_text.delete(1.0, tk.END)
            analysis_text.insert(tk.END, self.app_state['analysis_text'])

    def export_analysis(self):
        """Export the health market analysis to a file."""
        try:
            if not self.app_state['analysis_text']:
                messagebox.showinfo("No Analysis", "Please generate an analysis first.")
                return

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"TiT_Health_Analysis_{timestamp}.txt"

            filepath = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="Export Health Market Analysis",
                initialfilename=filename
            )

            if filepath:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(self.app_state['analysis_text'])
                messagebox.showinfo("Export Successful", f"Analysis exported to:\n{filepath}")
                self.status_var.set(f"Analysis exported to {filepath}")

        except Exception as e:
            logging.error(f"Error exporting analysis: {e}")
            messagebox.showerror("Export Error", f"Failed to export analysis: {e}")

# ==============================================================================
# SECTION 6: MAIN EXECUTION
# ==============================================================================
if __name__ == "__main__":
    try:
        root = ThemedTk(theme=HealthConfig.THEME)
        app = TiTHealthApp(root)
        root.mainloop()
    except Exception as e:
        logging.critical(f"A critical, unhandled error occurred: {e}", exc_info=True)
        messagebox.showerror("Fatal Application Error", f"A fatal error occurred and the application must close.\n\nDetails: {e}")

# End of TiT Health App 1.0.1
