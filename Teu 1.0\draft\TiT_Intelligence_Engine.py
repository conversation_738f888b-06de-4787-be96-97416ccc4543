# TiT Intelligence Engine: Cross-App Market Correlation & Analysis System
# Version: 1.0.1 (Cross-Market Intelligence Module)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
# Date: 2025-06-17
#
# Description:
# Advanced Intelligence Engine that analyzes relationships and correlations
# between crypto, stocks, oil, gold, health, and geopolitical events to generate
# comprehensive market insights and predictive intelligence.

import logging
import statistics
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import google.generativeai as genai

# Import cross-app intelligence
try:
    from cross_app_intelligence import cross_app_intelligence, get_cross_sector_insights, get_market_regime
    INTELLIGENCE_AVAILABLE = True
    print("✅ Cross-app intelligence system loaded!")
except ImportError:
    INTELLIGENCE_AVAILABLE = False
    print("⚠️ Cross-app intelligence not found, using basic intelligence")

# ==============================================================================
# SECTION 1: CROSS-MARKET INTELLIGENCE ENGINE
# ==============================================================================
class CrossMarketIntelligenceEngine:
    """Advanced cross-market correlation and intelligence analysis system"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        if api_key:
            try:
                genai.configure(api_key=api_key)
                self.model = genai.GenerativeModel('gemini-pro')
                logging.info("CrossMarketIntelligenceEngine initialized with AI capabilities.")
            except Exception as e:
                logging.error(f"Failed to initialize AI: {e}")
                self.model = None
        else:
            self.model = None
            
        # Market correlation patterns
        self.correlation_patterns = {
            'risk_on_risk_off': {
                'risk_on': ['stocks_up', 'crypto_up', 'oil_up', 'gold_down'],
                'risk_off': ['stocks_down', 'crypto_down', 'oil_down', 'gold_up']
            },
            'inflation_hedge': {
                'high_inflation': ['gold_up', 'oil_up', 'stocks_mixed', 'crypto_up'],
                'low_inflation': ['gold_down', 'oil_down', 'stocks_up', 'crypto_mixed']
            },
            'geopolitical_stress': {
                'high_tension': ['gold_up', 'oil_up', 'defense_stocks_up', 'crypto_mixed'],
                'low_tension': ['gold_down', 'oil_stable', 'defense_stocks_down', 'risk_assets_up']
            },
            'monetary_policy': {
                'hawkish': ['stocks_down', 'gold_down', 'crypto_down', 'dollar_up'],
                'dovish': ['stocks_up', 'gold_up', 'crypto_up', 'dollar_down']
            }
        }
        
        # Event impact weights
        self.event_weights = {
            'fed_decision': 0.9,
            'geopolitical_crisis': 0.8,
            'oil_supply_shock': 0.7,
            'major_earnings': 0.6,
            'crypto_regulation': 0.7,
            'health_crisis': 0.8,
            'trade_war': 0.7
        }
        
        logging.info("Cross-market intelligence patterns loaded.")

    def analyze_market_relationships(self, market_data: Dict) -> Dict:
        """Comprehensive analysis of relationships between all markets"""
        try:
            analysis = {
                'correlation_analysis': {},
                'pattern_recognition': {},
                'causal_relationships': {},
                'market_regime': {},
                'cross_asset_signals': {},
                'intelligence_summary': {},
                'predictive_insights': {},
                'timestamp': datetime.now().isoformat()
            }
            
            # 1. Correlation Analysis
            analysis['correlation_analysis'] = self._calculate_cross_correlations(market_data)
            
            # 2. Pattern Recognition
            analysis['pattern_recognition'] = self._recognize_market_patterns(market_data)
            
            # 3. Causal Relationship Analysis
            analysis['causal_relationships'] = self._analyze_causal_relationships(market_data)
            
            # 4. Market Regime Detection
            analysis['market_regime'] = self._detect_market_regime(market_data)
            
            # 5. Cross-Asset Trading Signals
            analysis['cross_asset_signals'] = self._generate_cross_asset_signals(market_data)
            
            # 6. AI-Powered Intelligence Summary
            if self.model:
                analysis['intelligence_summary'] = self._generate_ai_intelligence_summary(market_data, analysis)
            
            # 7. Predictive Insights
            analysis['predictive_insights'] = self._generate_predictive_insights(market_data, analysis)
            
            return analysis
            
        except Exception as e:
            logging.error(f"Error in cross-market analysis: {e}")
            return {'error': f'Analysis failed: {e}', 'timestamp': datetime.now().isoformat()}

    def _calculate_cross_correlations(self, market_data: Dict) -> Dict:
        """Calculate correlations between different market sectors"""
        correlations = {}
        
        try:
            # Extract price changes for correlation analysis
            price_changes = {}
            
            for market, data in market_data.items():
                if isinstance(data, dict):
                    changes = []
                    for asset, asset_data in data.items():
                        if isinstance(asset_data, dict) and 'change_percent' in asset_data:
                            changes.append(asset_data['change_percent'])
                    
                    if changes:
                        price_changes[market] = statistics.mean(changes)
            
            # Calculate pairwise correlations (simplified)
            markets = list(price_changes.keys())
            for i, market1 in enumerate(markets):
                for market2 in markets[i+1:]:
                    # Simplified correlation based on directional movement
                    change1 = price_changes[market1]
                    change2 = price_changes[market2]
                    
                    # Directional correlation
                    if (change1 > 0 and change2 > 0) or (change1 < 0 and change2 < 0):
                        correlation = min(abs(change1), abs(change2)) / max(abs(change1), abs(change2), 0.1)
                    else:
                        correlation = -min(abs(change1), abs(change2)) / max(abs(change1), abs(change2), 0.1)
                    
                    correlations[f"{market1}_vs_{market2}"] = {
                        'correlation': correlation,
                        'strength': 'strong' if abs(correlation) > 0.7 else 'moderate' if abs(correlation) > 0.4 else 'weak',
                        'direction': 'positive' if correlation > 0 else 'negative'
                    }
            
            return {
                'correlations': correlations,
                'market_changes': price_changes,
                'strongest_correlation': max(correlations.items(), key=lambda x: abs(x[1]['correlation'])) if correlations else None
            }
            
        except Exception as e:
            logging.error(f"Error calculating correlations: {e}")
            return {'error': 'Correlation calculation failed'}

    def _recognize_market_patterns(self, market_data: Dict) -> Dict:
        """Recognize known market patterns and regimes"""
        patterns = {}
        
        try:
            # Extract market movements
            movements = {}
            for market, data in market_data.items():
                if isinstance(data, dict):
                    changes = []
                    for asset, asset_data in data.items():
                        if isinstance(asset_data, dict) and 'change_percent' in asset_data:
                            changes.append(asset_data['change_percent'])
                    
                    if changes:
                        avg_change = statistics.mean(changes)
                        movements[market] = 'up' if avg_change > 0.5 else 'down' if avg_change < -0.5 else 'stable'
            
            # Check for known patterns
            for pattern_name, pattern_data in self.correlation_patterns.items():
                pattern_matches = {}
                
                for regime, expected_moves in pattern_data.items():
                    match_score = 0
                    total_checks = 0
                    
                    for expected_move in expected_moves:
                        market_name = expected_move.split('_')[0]
                        expected_direction = expected_move.split('_')[1]
                        
                        if market_name in movements:
                            total_checks += 1
                            actual_movement = movements[market_name]
                            
                            if (expected_direction == 'up' and actual_movement == 'up') or \
                               (expected_direction == 'down' and actual_movement == 'down') or \
                               (expected_direction == 'stable' and actual_movement == 'stable'):
                                match_score += 1
                    
                    if total_checks > 0:
                        pattern_matches[regime] = {
                            'match_percentage': (match_score / total_checks) * 100,
                            'confidence': 'high' if match_score / total_checks > 0.8 else 'medium' if match_score / total_checks > 0.6 else 'low'
                        }
                
                patterns[pattern_name] = pattern_matches
            
            # Identify dominant pattern
            best_pattern = None
            best_score = 0
            
            for pattern_name, regimes in patterns.items():
                for regime, data in regimes.items():
                    if data['match_percentage'] > best_score:
                        best_score = data['match_percentage']
                        best_pattern = f"{pattern_name}_{regime}"
            
            return {
                'patterns': patterns,
                'dominant_pattern': best_pattern,
                'confidence_score': best_score,
                'market_movements': movements
            }
            
        except Exception as e:
            logging.error(f"Error recognizing patterns: {e}")
            return {'error': 'Pattern recognition failed'}

    def _analyze_causal_relationships(self, market_data: Dict) -> Dict:
        """Analyze potential causal relationships between markets"""
        causal_analysis = {}
        
        try:
            # Define known causal relationships
            causal_chains = {
                'oil_to_inflation': {
                    'trigger': 'oil_price_spike',
                    'effects': ['inflation_expectations_up', 'gold_up', 'stocks_down'],
                    'timeframe': 'weeks_to_months'
                },
                'fed_policy_to_markets': {
                    'trigger': 'interest_rate_change',
                    'effects': ['dollar_movement', 'gold_inverse', 'stocks_impact', 'crypto_impact'],
                    'timeframe': 'immediate_to_days'
                },
                'geopolitical_to_safe_havens': {
                    'trigger': 'geopolitical_tension',
                    'effects': ['gold_up', 'oil_up', 'stocks_down', 'crypto_volatile'],
                    'timeframe': 'immediate'
                },
                'crypto_to_risk_assets': {
                    'trigger': 'crypto_regulation_news',
                    'effects': ['crypto_down', 'tech_stocks_down', 'risk_off_sentiment'],
                    'timeframe': 'immediate_to_hours'
                }
            }
            
            # Analyze current market state for causal triggers
            for chain_name, chain_data in causal_chains.items():
                trigger_detected = self._detect_causal_trigger(market_data, chain_data['trigger'])
                
                if trigger_detected:
                    causal_analysis[chain_name] = {
                        'trigger_detected': True,
                        'trigger_strength': trigger_detected['strength'],
                        'expected_effects': chain_data['effects'],
                        'timeframe': chain_data['timeframe'],
                        'probability': trigger_detected['probability']
                    }
            
            return {
                'active_causal_chains': causal_analysis,
                'causal_chain_count': len(causal_analysis),
                'highest_probability_chain': max(causal_analysis.items(), key=lambda x: x[1]['probability']) if causal_analysis else None
            }
            
        except Exception as e:
            logging.error(f"Error analyzing causal relationships: {e}")
            return {'error': 'Causal analysis failed'}

    def _detect_causal_trigger(self, market_data: Dict, trigger_type: str) -> Optional[Dict]:
        """Detect if a specific causal trigger is present in the market data"""
        try:
            if trigger_type == 'oil_price_spike':
                oil_data = market_data.get('oil', {})
                for asset, data in oil_data.items():
                    if isinstance(data, dict) and 'change_percent' in data:
                        if data['change_percent'] > 5:  # 5% spike
                            return {'strength': 'strong', 'probability': 0.8}
                        elif data['change_percent'] > 2:
                            return {'strength': 'moderate', 'probability': 0.6}
            
            elif trigger_type == 'geopolitical_tension':
                # Check news for geopolitical keywords
                # This would be enhanced with actual news analysis
                return {'strength': 'moderate', 'probability': 0.5}
            
            elif trigger_type == 'crypto_regulation_news':
                crypto_data = market_data.get('crypto', {})
                significant_moves = 0
                for asset, data in crypto_data.items():
                    if isinstance(data, dict) and 'change_percent' in data:
                        if abs(data['change_percent']) > 10:
                            significant_moves += 1
                
                if significant_moves > 3:
                    return {'strength': 'strong', 'probability': 0.7}
            
            return None
            
        except Exception as e:
            logging.error(f"Error detecting trigger {trigger_type}: {e}")
            return None

    def _detect_market_regime(self, market_data: Dict) -> Dict:
        """Detect the current market regime"""
        try:
            regime_indicators = {
                'volatility': 0,
                'correlation': 0,
                'momentum': 0,
                'risk_sentiment': 0
            }
            
            # Calculate volatility indicator
            all_changes = []
            for market, data in market_data.items():
                if isinstance(data, dict):
                    for asset, asset_data in data.items():
                        if isinstance(asset_data, dict) and 'change_percent' in asset_data:
                            all_changes.append(abs(asset_data['change_percent']))
            
            if all_changes:
                avg_volatility = statistics.mean(all_changes)
                regime_indicators['volatility'] = min(avg_volatility / 5.0, 1.0)  # Normalize to 0-1
            
            # Determine regime
            if regime_indicators['volatility'] > 0.8:
                regime = 'high_volatility'
            elif regime_indicators['volatility'] > 0.4:
                regime = 'moderate_volatility'
            else:
                regime = 'low_volatility'
            
            # Add risk sentiment
            safe_haven_performance = 0
            risk_asset_performance = 0
            
            gold_data = market_data.get('gold', {})
            for asset, data in gold_data.items():
                if isinstance(data, dict) and 'change_percent' in data:
                    safe_haven_performance += data['change_percent']
            
            stock_data = market_data.get('stocks', {})
            for asset, data in stock_data.items():
                if isinstance(data, dict) and 'change_percent' in data:
                    risk_asset_performance += data['change_percent']
            
            if safe_haven_performance > risk_asset_performance:
                risk_sentiment = 'risk_off'
            else:
                risk_sentiment = 'risk_on'
            
            return {
                'regime': regime,
                'risk_sentiment': risk_sentiment,
                'indicators': regime_indicators,
                'confidence': 'high' if regime_indicators['volatility'] > 0.6 else 'moderate'
            }
            
        except Exception as e:
            logging.error(f"Error detecting market regime: {e}")
            return {'regime': 'unknown', 'error': str(e)}

    def _generate_cross_asset_signals(self, market_data: Dict) -> Dict:
        """Generate trading signals based on cross-asset analysis"""
        signals = {}
        
        try:
            # Signal 1: Gold-Dollar Divergence
            gold_performance = 0
            gold_data = market_data.get('gold', {})
            for asset, data in gold_data.items():
                if isinstance(data, dict) and 'change_percent' in data:
                    gold_performance += data['change_percent']
            
            if gold_performance > 2:
                signals['gold_strength'] = {
                    'signal': 'bullish',
                    'strength': 'strong',
                    'implication': 'Dollar weakness or inflation concerns'
                }
            
            # Signal 2: Oil-Stock Correlation Break
            oil_performance = 0
            oil_data = market_data.get('oil', {})
            for asset, data in oil_data.items():
                if isinstance(data, dict) and 'change_percent' in data:
                    oil_performance += data['change_percent']
            
            stock_performance = 0
            stock_data = market_data.get('stocks', {})
            for asset, data in stock_data.items():
                if isinstance(data, dict) and 'change_percent' in data:
                    stock_performance += data['change_percent']
            
            if oil_performance > 3 and stock_performance < -1:
                signals['oil_stock_divergence'] = {
                    'signal': 'bearish_stocks',
                    'strength': 'moderate',
                    'implication': 'Energy costs pressuring corporate margins'
                }
            
            # Signal 3: Crypto-Tech Stock Correlation
            crypto_performance = 0
            crypto_data = market_data.get('crypto', {})
            for asset, data in crypto_data.items():
                if isinstance(data, dict) and 'change_percent' in data:
                    crypto_performance += data['change_percent']
            
            if abs(crypto_performance) > 5:
                signals['crypto_volatility'] = {
                    'signal': 'high_volatility',
                    'strength': 'strong',
                    'implication': 'Risk asset volatility spillover expected'
                }
            
            return {
                'signals': signals,
                'signal_count': len(signals),
                'overall_sentiment': self._calculate_overall_sentiment(signals)
            }
            
        except Exception as e:
            logging.error(f"Error generating cross-asset signals: {e}")
            return {'error': 'Signal generation failed'}

    def _calculate_overall_sentiment(self, signals: Dict) -> str:
        """Calculate overall market sentiment from signals"""
        bullish_count = 0
        bearish_count = 0
        
        for signal_name, signal_data in signals.items():
            signal_type = signal_data.get('signal', '')
            if 'bullish' in signal_type:
                bullish_count += 1
            elif 'bearish' in signal_type:
                bearish_count += 1
        
        if bullish_count > bearish_count:
            return 'bullish'
        elif bearish_count > bullish_count:
            return 'bearish'
        else:
            return 'neutral'

    def _generate_ai_intelligence_summary(self, market_data: Dict, analysis: Dict) -> str:
        """Generate AI-powered intelligence summary"""
        if not self.model:
            return "AI analysis unavailable"
        
        try:
            # Prepare comprehensive data summary
            summary_data = self._prepare_comprehensive_summary(market_data, analysis)
            
            prompt = f"""
            As a master financial intelligence analyst, provide a comprehensive cross-market intelligence report based on the following data:

            MARKET DATA SUMMARY:
            {summary_data}

            CORRELATION ANALYSIS:
            {analysis.get('correlation_analysis', {})}

            PATTERN RECOGNITION:
            {analysis.get('pattern_recognition', {})}

            MARKET REGIME:
            {analysis.get('market_regime', {})}

            Please provide a detailed intelligence report covering:

            ## 🧠 CROSS-MARKET INTELLIGENCE SUMMARY

            **Market Regime Assessment**:
            - Current market regime and confidence level
            - Key driving forces and market dynamics
            - Risk sentiment and volatility assessment

            **Inter-Market Relationships**:
            - Strongest correlations and their implications
            - Unusual divergences or convergences
            - Causal relationships and transmission mechanisms

            **Pattern Recognition**:
            - Dominant market patterns identified
            - Historical context and precedents
            - Pattern reliability and confidence levels

            **Cross-Asset Signals**:
            - Key trading signals from cross-asset analysis
            - Signal strength and reliability
            - Recommended positioning and hedging strategies

            **Geopolitical & Macro Implications**:
            - How current events are affecting market relationships
            - Potential policy implications and market responses
            - Global economic themes and their market impact

            **Forward-Looking Intelligence**:
            - Likely market scenarios and their probabilities
            - Key events and catalysts to monitor
            - Early warning indicators for regime changes

            **Strategic Recommendations**:
            - Portfolio positioning recommendations
            - Risk management considerations
            - Opportunities and threats across asset classes

            Provide specific, actionable intelligence with confidence levels and time horizons.
            """

            response = self.model.generate_content(prompt)
            return response.text

        except Exception as e:
            logging.error(f"Error generating AI intelligence summary: {e}")
            return f"AI analysis error: {e}"

    def _prepare_comprehensive_summary(self, market_data: Dict, analysis: Dict) -> str:
        """Prepare comprehensive data summary for AI analysis"""
        summary_parts = []
        
        for market, data in market_data.items():
            if isinstance(data, dict):
                market_summary = f"\n{market.upper()} MARKET:\n"
                for asset, asset_data in list(data.items())[:5]:  # Top 5 assets per market
                    if isinstance(asset_data, dict):
                        change = asset_data.get('change_percent', 0)
                        price = asset_data.get('price', 0)
                        name = asset_data.get('name', asset)
                        market_summary += f"- {name}: ${price:.2f} ({change:+.2f}%)\n"
                summary_parts.append(market_summary)
        
        return "\n".join(summary_parts)

    def _generate_predictive_insights(self, market_data: Dict, analysis: Dict) -> Dict:
        """Generate predictive insights based on cross-market analysis"""
        insights = {}
        
        try:
            # Short-term predictions (1-7 days)
            short_term = []
            
            # Check for momentum patterns
            correlations = analysis.get('correlation_analysis', {}).get('correlations', {})
            for correlation_pair, correlation_data in correlations.items():
                if correlation_data['strength'] == 'strong':
                    short_term.append(f"Strong {correlation_data['direction']} correlation between {correlation_pair.replace('_vs_', ' and ')} likely to continue")
            
            # Medium-term predictions (1-4 weeks)
            medium_term = []
            
            regime = analysis.get('market_regime', {}).get('regime', 'unknown')
            if regime == 'high_volatility':
                medium_term.append("High volatility regime may persist, expect continued cross-asset correlations")
            elif regime == 'low_volatility':
                medium_term.append("Low volatility environment may lead to correlation breakdown")
            
            # Long-term structural insights (1-6 months)
            long_term = []
            
            patterns = analysis.get('pattern_recognition', {}).get('dominant_pattern', '')
            if 'risk_off' in patterns:
                long_term.append("Risk-off sentiment may drive continued safe-haven demand")
            elif 'risk_on' in patterns:
                long_term.append("Risk-on environment supports growth assets over safe havens")
            
            insights = {
                'short_term_1_7_days': short_term,
                'medium_term_1_4_weeks': medium_term,
                'long_term_1_6_months': long_term,
                'confidence_level': 'moderate',
                'key_risks': ['Unexpected policy changes', 'Geopolitical events', 'Economic data surprises'],
                'monitoring_points': ['Central bank communications', 'Geopolitical developments', 'Cross-asset correlation changes']
            }
            
            return insights
            
        except Exception as e:
            logging.error(f"Error generating predictive insights: {e}")
            return {'error': 'Prediction generation failed'}
