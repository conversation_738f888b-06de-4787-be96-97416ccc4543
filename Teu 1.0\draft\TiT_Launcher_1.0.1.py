# TiT Launcher 1.0.1: Unified Financial Intelligence Suite Launcher
# Version: 1.0.1 (Master Launcher)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>uang. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>uang)
# Date: 2025-06-17
#
# Description:
# Master launcher for the complete TiT App Suite providing unified access to all
# financial intelligence applications: Crypto, Stock, Oil, Gold, Health, Defense, Science.
# Features cross-app intelligence dashboard and comprehensive market overview.

# ==============================================================================
# SECTION 1: IMPORTS
# ==============================================================================
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
import logging
from datetime import datetime
import threading
import webbrowser

# Third-Party Library Imports
try:
    from ttkthemes import ThemedTk
    THEMED_TK_AVAILABLE = True
except ImportError:
    THEMED_TK_AVAILABLE = False
    print("⚠️ ttkthemes not available, using standard tkinter")

# ==============================================================================
# SECTION 2: LAUNCHER CONFIGURATION
# ==============================================================================
class LauncherConfig:
    """Configuration for TiT Launcher"""
    
    # --- Logging Configuration ---
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = '%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s'
    LOG_FILE = "tit_launcher_1.0.1.log"

    # --- TiT App Suite Applications ---
    APPLICATIONS = {
        'crypto': {
            'name': 'TiT Crypto App',
            'description': 'Advanced Cryptocurrency Intelligence Suite',
            'file': 'Teu 1.0.1.py',
            'icon': '₿',
            'color': '#F7931A',
            'features': ['Real-time crypto prices', 'DeFi analytics', 'News intelligence', 'AI predictions']
        },
        'stock': {
            'name': 'TiT Stock App',
            'description': 'Global Stock Market Intelligence Suite',
            'file': 'TiT_Stock_App_1.0.1.py',
            'icon': '📈',
            'color': '#2E8B57',
            'features': ['20+ countries coverage', 'Real-time indices', 'Earnings calendar', 'AI analysis']
        },
        'oil': {
            'name': 'TiT Oil App',
            'description': 'Oil Market Intelligence Suite',
            'file': 'TiT_Oil_App_1.0.1.py',
            'icon': '🛢️',
            'color': '#8B4513',
            'features': ['Oil futures tracking', 'OPEC analysis', 'Energy companies', 'Geopolitical impact']
        },
        'gold': {
            'name': 'TiT Gold App',
            'description': 'Precious Metals Intelligence Suite',
            'file': 'TiT_Gold_App_1.0.1.py',
            'icon': '🥇',
            'color': '#FFD700',
            'features': ['Precious metals prices', 'Mining companies', 'Central bank data', 'Safe haven analysis']
        },
        'health': {
            'name': 'TiT Health App',
            'description': 'Health & Biotech Intelligence Suite',
            'file': 'TiT_Health_App_1.0.1.py',
            'icon': '🧬',
            'color': '#2E8B57',
            'features': ['Biotech companies', 'Drug pipeline', 'FDA approvals', 'Healthcare trends']
        },
        'defense': {
            'name': 'TiT Defense App',
            'description': 'Geopolitical & Defense Intelligence Suite',
            'file': 'TiT_Defense_App_1.0.1.py',
            'icon': '⚔️',
            'color': '#2F4F4F',
            'features': ['Defense contractors', 'Conflict monitoring', 'Arms trade', 'Geopolitical analysis']
        },
        'science': {
            'name': 'TiT Science App',
            'description': 'Science, Technology & Space Intelligence Suite',
            'file': 'TiT_Science_App_1.0.1.py',
            'icon': '🚀',
            'color': '#4169E1',
            'features': ['Tech companies', 'Space exploration', 'AI developments', 'Innovation tracking']
        }
    }

    # --- UI Configuration ---
    THEME = 'arc'
    FONT_FAMILY = "Segoe UI"
    FONT_SIZE_NORMAL = 11
    FONT_SIZE_LARGE = 14
    FONT_SIZE_HEADER = 18
    
    # Launcher Color Palette
    COLORS = {
        'primary': '#2C3E50',        # Dark Blue Gray
        'secondary': '#3498DB',      # Blue
        'accent': '#E74C3C',         # Red
        'success': '#27AE60',        # Green
        'warning': '#F39C12',        # Orange
        'info': '#3498DB',           # Blue
        'surface': '#FFFFFF',        # White
        'background': '#ECF0F1',     # Light Gray
        'text_primary': '#2C3E50',   # Dark Blue Gray
        'text_secondary': '#7F8C8D', # Gray
        'border': '#BDC3C7'          # Light Gray
    }
    
    UI_PADDING = 15

# Setup Logging
logging.basicConfig(
    level=LauncherConfig.LOG_LEVEL,
    format=LauncherConfig.LOG_FORMAT,
    handlers=[
        logging.FileHandler(LauncherConfig.LOG_FILE, mode='w'),
        logging.StreamHandler()
    ]
)
logging.info("TiT Launcher 1.0.1 Starting...")
logging.info("Copyright (C) 2025 Nguyen Le Vinh Quang. All rights reserved.")

# ==============================================================================
# SECTION 3: MAIN LAUNCHER APPLICATION
# ==============================================================================
class TiTLauncher:
    """Main TiT Suite Launcher Application"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("✨ TiT Suite 1.0.1 - OMNIVERSAL TRANSCENDENT SUPREME ULTIMATE Financial Intelligence Launcher at ANOTHER HIGHEST LEVEL ✨")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # Center the window
        self.center_window()
        
        # Initialize UI
        self.setup_ui()
        
        logging.info("TiT Launcher initialized successfully.")

    def center_window(self):
        """Center the launcher window on screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_ui(self):
        """Setup the launcher UI."""
        # Main container
        self.main_frame = ttk.Frame(self.root, padding=LauncherConfig.UI_PADDING)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Header section
        self.create_header()
        
        # Applications grid
        self.create_applications_grid()
        
        # Footer section
        self.create_footer()

    def create_header(self):
        """Create the enhanced header section with beautiful graphics."""
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Main title with gradient effect simulation
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(pady=(0, 10))

        # Large decorative title
        title_label = ttk.Label(
            title_frame,
            text="🚀 TiT FINANCIAL INTELLIGENCE SUITE 🚀",
            font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_HEADER + 4, 'bold'),
            foreground='#2C3E50'
        )
        title_label.pack(pady=(0, 5))

        # Animated subtitle with emojis
        subtitle_label = ttk.Label(
            title_frame,
            text="💎 Advanced Market Intelligence & AI Analysis Platform 💎",
            font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_LARGE, 'italic'),
            foreground='#3498DB'
        )
        subtitle_label.pack(pady=(0, 5))

        # Version and status
        version_label = ttk.Label(
            title_frame,
            text="⭐ Version 1.0.1 - Professional Edition ⭐",
            font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_NORMAL, 'bold'),
            foreground='#27AE60'
        )
        version_label.pack(pady=(0, 15))

        # Enhanced author section with professional styling
        author_frame = ttk.LabelFrame(header_frame, text="👨‍💻 Created By")
        author_frame.pack(pady=(10, 0))

        author_label = ttk.Label(
            author_frame,
            text="🎯 Anh Quang (Nguyen Le Vinh Quang) 🎯",
            font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_NORMAL + 1, 'bold'),
            foreground='#E74C3C'
        )
        author_label.pack(padx=15, pady=8)

        # Professional tagline
        tagline_label = ttk.Label(
            author_frame,
            text="🌟 Professional Financial Intelligence Developer 🌟",
            font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_NORMAL - 1, 'italic'),
            foreground='#7F8C8D'
        )
        tagline_label.pack(padx=15, pady=(0, 8))

    def create_applications_grid(self):
        """Create the applications grid."""
        apps_frame = ttk.LabelFrame(self.main_frame, text="Available Applications")
        apps_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Create grid container
        grid_frame = ttk.Frame(apps_frame)
        grid_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Configure grid weights
        for i in range(3):  # 3 columns
            grid_frame.columnconfigure(i, weight=1)
        for i in range(3):  # 3 rows (7 apps + 1 extra)
            grid_frame.rowconfigure(i, weight=1)

        # Create application cards
        row, col = 0, 0
        for app_key, app_info in LauncherConfig.APPLICATIONS.items():
            self.create_app_card(grid_frame, app_key, app_info, row, col)
            
            col += 1
            if col >= 3:
                col = 0
                row += 1

    def create_app_card(self, parent, app_key, app_info, row, col):
        """Create a beautiful enhanced application card."""
        # Enhanced card frame with border styling
        card_frame = ttk.LabelFrame(parent, text=f"  {app_info['icon']} {app_info['name']}  ")
        card_frame.grid(row=row, column=col, padx=12, pady=12, sticky="nsew")

        # Status indicator
        status_frame = ttk.Frame(card_frame)
        status_frame.pack(fill=tk.X, padx=10, pady=(5, 0))

        status_label = ttk.Label(
            status_frame,
            text="🟢 READY",
            font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_NORMAL - 1, 'bold'),
            foreground='#27AE60'
        )
        status_label.pack(side=tk.RIGHT)

        # Large icon display
        icon_frame = ttk.Frame(card_frame)
        icon_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        large_icon_label = ttk.Label(
            icon_frame,
            text=app_info['icon'],
            font=(LauncherConfig.FONT_FAMILY, 32)
        )
        large_icon_label.pack()

        # Enhanced description with styling
        desc_label = ttk.Label(
            card_frame,
            text=f"📋 {app_info['description']}",
            font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_NORMAL, 'italic'),
            wraplength=280,
            foreground='#2C3E50'
        )
        desc_label.pack(fill=tk.X, padx=10, pady=(0, 10))

        # Enhanced features section
        features_frame = ttk.LabelFrame(card_frame, text="✨ Key Features")
        features_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        for i, feature in enumerate(app_info['features']):
            # Use different emojis for each feature
            feature_emojis = ['🎯', '⚡', '🔥', '💎']
            emoji = feature_emojis[i % len(feature_emojis)]

            feature_label = ttk.Label(
                features_frame,
                text=f"{emoji} {feature}",
                font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_NORMAL - 1),
                foreground='#34495E'
            )
            feature_label.pack(anchor=tk.W, padx=8, pady=1)

        # Enhanced launch button with styling
        button_frame = ttk.Frame(card_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=(5, 10))

        launch_btn = ttk.Button(
            button_frame,
            text=f"🚀 Launch {app_info['name']}",
            command=lambda: self.launch_application(app_key, app_info)
        )
        launch_btn.pack(fill=tk.X)

        # Add file status indicator
        file_status_label = ttk.Label(
            button_frame,
            text=f"📁 {app_info['file']}",
            font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_NORMAL - 2),
            foreground='#7F8C8D'
        )
        file_status_label.pack(pady=(2, 0))

    def create_footer(self):
        """Create the enhanced footer section with beautiful controls."""
        footer_frame = ttk.Frame(self.main_frame)
        footer_frame.pack(fill=tk.X)

        # Enhanced control buttons section
        controls_frame = ttk.LabelFrame(footer_frame, text="🎮 Control Center")
        controls_frame.pack(fill=tk.X, pady=(0, 15))

        # Button container
        button_container = ttk.Frame(controls_frame)
        button_container.pack(fill=tk.X, padx=10, pady=10)

        # Launch All button with enhanced styling
        launch_all_btn = ttk.Button(
            button_container,
            text="🚀 LAUNCH ALL APPLICATIONS",
            command=self.launch_all_applications
        )
        launch_all_btn.pack(side=tk.LEFT, padx=(0, 15))

        # Cross-App Intelligence button
        intelligence_btn = ttk.Button(
            button_container,
            text="🧠 CROSS-APP INTELLIGENCE",
            command=self.show_intelligence_dashboard
        )
        intelligence_btn.pack(side=tk.LEFT, padx=(0, 15))

        # Quality Engine button
        quality_btn = ttk.Button(
            button_container,
            text="📊 QUALITY ENGINE",
            command=self.show_quality_dashboard
        )
        quality_btn.pack(side=tk.LEFT, padx=(0, 15))

        # Exit button
        exit_btn = ttk.Button(
            button_container,
            text="❌ EXIT LAUNCHER",
            command=self.exit_application
        )
        exit_btn.pack(side=tk.RIGHT)

        # Enhanced status and copyright section
        status_frame = ttk.LabelFrame(footer_frame, text="📋 System Information")
        status_frame.pack(fill=tk.X)

        # Copyright with enhanced styling
        copyright_label = ttk.Label(
            status_frame,
            text="© 2025 Nguyen Le Vinh Quang. All rights reserved. | Professional Financial Intelligence Suite",
            font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_NORMAL - 1, 'italic'),
            foreground='#7F8C8D'
        )
        copyright_label.pack(side=tk.LEFT, padx=10, pady=5)

        # Enhanced version info
        version_label = ttk.Label(
            status_frame,
            text="🎯 TiT Suite v1.0.1 Professional",
            font=(LauncherConfig.FONT_FAMILY, LauncherConfig.FONT_SIZE_NORMAL - 1, 'bold'),
            foreground='#2C3E50'
        )
        version_label.pack(side=tk.RIGHT, padx=10, pady=5)

    def launch_application(self, app_key, app_info):
        """🚀 Launch a specific application with ULTRA-FAST startup and immediate feedback."""
        try:
            app_file = app_info['file']

            # Check if file exists
            if not os.path.exists(app_file):
                messagebox.showerror(
                    "❌ File Not Found",
                    f"Application file '{app_file}' not found.\n\n"
                    f"📁 Please ensure all TiT apps are in the same directory.\n\n"
                    f"🔍 Looking for: {app_file}"
                )
                return

            # IMMEDIATE launch with ULTRA-FAST startup
            logging.info(f"🚀 ULTRA-FAST launching {app_info['name']} ({app_file})")

            try:
                # Launch immediately without threading for FASTEST startup
                process = subprocess.Popen([sys.executable, app_file])
                logging.info(f"✅ {app_info['name']} launched successfully with PID: {process.pid}")

                # Show IMMEDIATE success message
                messagebox.showinfo(
                    "🚀 ULTRA-FAST Launch Successful!",
                    f"✅ {app_info['name']} is starting NOW!\n\n"
                    f"⚡ ULTRA-FAST startup initiated\n"
                    f"🖥️ Application window will appear in seconds\n"
                    f"📊 All features ready for immediate use\n"
                    f"🎯 Process ID: {process.pid}"
                )

            except Exception as e:
                logging.error(f"❌ Error launching {app_info['name']}: {e}")
                messagebox.showerror(
                    "❌ Launch Error",
                    f"Failed to launch {app_info['name']}:\n\n"
                    f"Error: {e}\n\n"
                    f"💡 Troubleshooting:\n"
                    f"• Check if Python is properly installed\n"
                    f"• Verify file permissions\n"
                    f"• Try running as administrator"
                )

        except Exception as e:
            logging.error(f"Error launching {app_key}: {e}")
            messagebox.showerror("Launch Error", f"Failed to launch application: {e}")

    def launch_all_applications(self):
        """Launch all applications."""
        try:
            result = messagebox.askyesno(
                "Launch All Applications",
                "This will launch all 7 TiT applications.\n\nThis may use significant system resources.\n\nContinue?"
            )
            
            if not result:
                return

            launched_count = 0
            failed_apps = []

            for app_key, app_info in LauncherConfig.APPLICATIONS.items():
                try:
                    app_file = app_info['file']
                    
                    if os.path.exists(app_file):
                        subprocess.Popen([sys.executable, app_file])
                        launched_count += 1
                        logging.info(f"Launched {app_info['name']}")
                    else:
                        failed_apps.append(f"{app_info['name']} ({app_file})")
                        logging.warning(f"File not found: {app_file}")

                except Exception as e:
                    failed_apps.append(f"{app_info['name']} (Error: {e})")
                    logging.error(f"Error launching {app_info['name']}: {e}")

            # Show results
            message = f"Successfully launched {launched_count} applications."
            if failed_apps:
                message += f"\n\nFailed to launch:\n" + "\n".join(failed_apps)

            messagebox.showinfo("Launch Results", message)

        except Exception as e:
            logging.error(f"Error launching all applications: {e}")
            messagebox.showerror("Launch Error", f"Failed to launch applications: {e}")

    def show_intelligence_dashboard(self):
        """Show cross-app intelligence dashboard."""
        try:
            # Check if intelligence engine exists
            intelligence_file = "TiT_Intelligence_Engine.py"

            if os.path.exists(intelligence_file):
                subprocess.Popen([sys.executable, intelligence_file])
                messagebox.showinfo(
                    "🧠 Intelligence Dashboard",
                    "🚀 Cross-App Intelligence Engine is starting...\n\n" +
                    "🔗 This will analyze relationships between all markets:\n" +
                    "• Market correlations and dependencies\n" +
                    "• Cross-sector impact analysis\n" +
                    "• Predictive intelligence insights\n\n" +
                    "⏳ Please wait for the dashboard to load..."
                )
            else:
                messagebox.showinfo(
                    "🧠 Intelligence Dashboard",
                    "🎯 Cross-App Intelligence Dashboard\n\n" +
                    "📊 This feature analyzes relationships between:\n" +
                    "• 💰 Cryptocurrency markets\n" +
                    "• 📈 Global stock markets\n" +
                    "• 🛢️ Oil and energy markets\n" +
                    "• 🥇 Precious metals markets\n" +
                    "• 🧬 Healthcare and biotech\n" +
                    "• ⚔️ Defense and geopolitical events\n" +
                    "• 🚀 Science and technology trends\n\n" +
                    "⚠️ Intelligence engine file not found.\n" +
                    "📁 Please ensure TiT_Intelligence_Engine.py is available."
                )

        except Exception as e:
            logging.error(f"Error showing intelligence dashboard: {e}")
            messagebox.showerror("❌ Error", f"Failed to show intelligence dashboard: {e}")

    def show_quality_dashboard(self):
        """Show quality assessment dashboard."""
        try:
            # Check if quality engine exists
            quality_file = "TiT_Quality_Engine.py"

            if os.path.exists(quality_file):
                subprocess.Popen([sys.executable, quality_file])
                messagebox.showinfo(
                    "📊 Quality Dashboard",
                    "🔍 Quality Assessment Engine is starting...\n\n" +
                    "✅ This will analyze data quality across all apps:\n" +
                    "• Data accuracy validation\n" +
                    "• Source reliability assessment\n" +
                    "• Performance optimization\n\n" +
                    "⏳ Please wait for the dashboard to load..."
                )
            else:
                messagebox.showinfo(
                    "📊 Quality Dashboard",
                    "🎯 Quality Assessment Dashboard\n\n" +
                    "🔍 This feature provides:\n" +
                    "• 📊 Data quality metrics\n" +
                    "• 🎯 Accuracy assessments\n" +
                    "• ⚡ Performance monitoring\n" +
                    "• 🔧 Optimization recommendations\n" +
                    "• 📈 Reliability scoring\n\n" +
                    "⚠️ Quality engine file not found.\n" +
                    "📁 Please ensure TiT_Quality_Engine.py is available."
                )

        except Exception as e:
            logging.error(f"Error showing quality dashboard: {e}")
            messagebox.showerror("❌ Error", f"Failed to show quality dashboard: {e}")

    def exit_application(self):
        """Exit the launcher application."""
        try:
            result = messagebox.askyesno(
                "Exit TiT Launcher",
                "Are you sure you want to exit the TiT Launcher?\n\nRunning TiT applications will continue to run."
            )
            
            if result:
                logging.info("TiT Launcher closing...")
                self.root.destroy()

        except Exception as e:
            logging.error(f"Error exiting application: {e}")
            self.root.destroy()

# ==============================================================================
# SECTION 4: MAIN EXECUTION
# ==============================================================================
if __name__ == "__main__":
    try:
        root = ThemedTk(theme=LauncherConfig.THEME)
        launcher = TiTLauncher(root)
        root.mainloop()
    except Exception as e:
        logging.critical(f"A critical, unhandled error occurred: {e}", exc_info=True)
        if 'messagebox' in globals():
            messagebox.showerror("Fatal Application Error", f"A fatal error occurred and the launcher must close.\n\nDetails: {e}")

# End of TiT Launcher 1.0.1
