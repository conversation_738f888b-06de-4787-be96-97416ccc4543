# TiT Quality Engine: Advanced Data Quality & Intelligence System
# Version: 1.0.1 (Quality Enhancement Module)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
# Date: 2025-06-17
#
# Description:
# Advanced Quality Engine for enhancing data accuracy, news intelligence,
# and cross-app correlation analysis across the entire TiT App Suite.
# This module provides quality scoring, validation, and intelligence services.

import logging
import time
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import re
import hashlib

# ==============================================================================
# SECTION 1: DATA QUALITY ENGINE
# ==============================================================================
class DataQualityEngine:
    """Advanced data quality assessment and enhancement system"""
    
    def __init__(self):
        self.quality_thresholds = {
            'excellent': 0.9,
            'good': 0.75,
            'acceptable': 0.6,
            'poor': 0.4
        }
        self.data_sources_credibility = {
            'yahoo_finance': 0.95,
            'globe_and_mail': 0.98,  # Highest credibility as preferred
            'reuters': 0.92,
            'bloomberg': 0.90,
            'cnbc': 0.85,
            'coingecko': 0.88,
            'fed_reserve': 0.99,
            'sec_filings': 0.97
        }
        logging.info("DataQualityEngine initialized with enhanced validation.")

    def assess_data_quality(self, data: Dict, data_type: str) -> Dict:
        """Comprehensive data quality assessment"""
        quality_score = 0.0
        quality_factors = {}
        
        try:
            # 1. Completeness Check
            completeness = self._check_completeness(data, data_type)
            quality_factors['completeness'] = completeness
            
            # 2. Freshness Check
            freshness = self._check_freshness(data)
            quality_factors['freshness'] = freshness
            
            # 3. Consistency Check
            consistency = self._check_consistency(data, data_type)
            quality_factors['consistency'] = consistency
            
            # 4. Source Credibility
            credibility = self._assess_source_credibility(data)
            quality_factors['credibility'] = credibility
            
            # 5. Data Validation
            validation = self._validate_data_ranges(data, data_type)
            quality_factors['validation'] = validation
            
            # Calculate weighted quality score
            weights = {
                'completeness': 0.25,
                'freshness': 0.20,
                'consistency': 0.20,
                'credibility': 0.20,
                'validation': 0.15
            }
            
            quality_score = sum(
                quality_factors[factor] * weights[factor] 
                for factor in weights.keys()
            )
            
            quality_level = self._get_quality_level(quality_score)
            
            return {
                'overall_score': quality_score,
                'quality_level': quality_level,
                'factors': quality_factors,
                'recommendations': self._generate_quality_recommendations(quality_factors),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error assessing data quality: {e}")
            return {
                'overall_score': 0.0,
                'quality_level': 'unknown',
                'factors': {},
                'recommendations': ['Data quality assessment failed'],
                'timestamp': datetime.now().isoformat()
            }

    def _check_completeness(self, data: Dict, data_type: str) -> float:
        """Check data completeness based on expected fields"""
        expected_fields = {
            'stock': ['price', 'change', 'volume', 'market_cap'],
            'crypto': ['price', 'change', 'volume', 'market_cap'],
            'oil': ['price', 'change', 'volume'],
            'gold': ['price', 'change', 'volume'],
            'news': ['title', 'source', 'publishedAt', 'description']
        }
        
        if data_type not in expected_fields:
            return 0.8  # Default for unknown types
            
        required_fields = expected_fields[data_type]
        
        if isinstance(data, dict):
            present_fields = sum(1 for field in required_fields if field in data and data[field] is not None)
            return present_fields / len(required_fields)
        elif isinstance(data, list) and data:
            # For lists, check average completeness
            completeness_scores = []
            for item in data[:10]:  # Sample first 10 items
                if isinstance(item, dict):
                    present_fields = sum(1 for field in required_fields if field in item and item[field] is not None)
                    completeness_scores.append(present_fields / len(required_fields))
            return statistics.mean(completeness_scores) if completeness_scores else 0.0
        
        return 0.0

    def _check_freshness(self, data: Dict) -> float:
        """Check data freshness based on timestamps"""
        now = datetime.now()
        
        if isinstance(data, dict):
            # Look for timestamp fields
            timestamp_fields = ['timestamp', 'publishedAt', 'last_updated', 'date']
            for field in timestamp_fields:
                if field in data and data[field]:
                    try:
                        if isinstance(data[field], str):
                            data_time = datetime.fromisoformat(data[field].replace('Z', '+00:00'))
                        else:
                            data_time = data[field]
                        
                        age_hours = (now - data_time).total_seconds() / 3600
                        
                        # Freshness scoring
                        if age_hours < 1:
                            return 1.0
                        elif age_hours < 6:
                            return 0.9
                        elif age_hours < 24:
                            return 0.7
                        elif age_hours < 168:  # 1 week
                            return 0.5
                        else:
                            return 0.2
                    except:
                        continue
        
        return 0.6  # Default if no timestamp found

    def _check_consistency(self, data: Dict, data_type: str) -> float:
        """Check data consistency and logical relationships"""
        consistency_score = 1.0
        
        if isinstance(data, dict):
            # Check for logical inconsistencies
            if data_type in ['stock', 'crypto', 'oil', 'gold']:
                price = data.get('price', 0)
                change = data.get('change', 0)
                change_percent = data.get('change_percent', 0)
                
                # Check if price change calculations are consistent
                if price > 0 and change != 0:
                    expected_change_percent = (change / (price - change)) * 100
                    if abs(change_percent - expected_change_percent) > 1.0:  # 1% tolerance
                        consistency_score -= 0.3
                
                # Check for reasonable price ranges
                if price <= 0:
                    consistency_score -= 0.5
                
                # Check for extreme changes (likely errors)
                if abs(change_percent) > 50:  # 50% change in a day is suspicious
                    consistency_score -= 0.2
        
        return max(0.0, consistency_score)

    def _assess_source_credibility(self, data: Dict) -> float:
        """Assess source credibility based on known reliable sources"""
        if isinstance(data, dict) and 'source' in data:
            source_name = data['source'].get('name', '').lower()
            
            for source, credibility in self.data_sources_credibility.items():
                if source.replace('_', ' ') in source_name:
                    return credibility
        
        return 0.7  # Default credibility for unknown sources

    def _validate_data_ranges(self, data: Dict, data_type: str) -> float:
        """Validate data falls within reasonable ranges"""
        validation_score = 1.0
        
        if isinstance(data, dict):
            if data_type == 'stock':
                price = data.get('price', 0)
                volume = data.get('volume', 0)
                market_cap = data.get('market_cap', 0)
                
                # Reasonable ranges for stocks
                if not (0.01 <= price <= 10000):  # $0.01 to $10,000
                    validation_score -= 0.3
                if volume < 0:
                    validation_score -= 0.2
                if market_cap < 0:
                    validation_score -= 0.2
                    
            elif data_type == 'crypto':
                price = data.get('price', 0)
                if not (0.000001 <= price <= 1000000):  # Very wide range for crypto
                    validation_score -= 0.3
                    
            elif data_type in ['oil', 'gold']:
                price = data.get('price', 0)
                if data_type == 'oil' and not (10 <= price <= 200):  # $10-$200 per barrel
                    validation_score -= 0.3
                elif data_type == 'gold' and not (1000 <= price <= 5000):  # $1000-$5000 per ounce
                    validation_score -= 0.3
        
        return max(0.0, validation_score)

    def _get_quality_level(self, score: float) -> str:
        """Convert quality score to quality level"""
        if score >= self.quality_thresholds['excellent']:
            return 'excellent'
        elif score >= self.quality_thresholds['good']:
            return 'good'
        elif score >= self.quality_thresholds['acceptable']:
            return 'acceptable'
        elif score >= self.quality_thresholds['poor']:
            return 'poor'
        else:
            return 'critical'

    def _generate_quality_recommendations(self, factors: Dict) -> List[str]:
        """Generate recommendations to improve data quality"""
        recommendations = []
        
        if factors.get('completeness', 1.0) < 0.8:
            recommendations.append("Improve data completeness by adding missing fields")
        
        if factors.get('freshness', 1.0) < 0.7:
            recommendations.append("Update data more frequently to improve freshness")
        
        if factors.get('consistency', 1.0) < 0.8:
            recommendations.append("Review data for logical inconsistencies")
        
        if factors.get('credibility', 1.0) < 0.8:
            recommendations.append("Consider using more credible data sources")
        
        if factors.get('validation', 1.0) < 0.8:
            recommendations.append("Validate data ranges and check for outliers")
        
        if not recommendations:
            recommendations.append("Data quality is excellent - maintain current standards")
        
        return recommendations

# ==============================================================================
# SECTION 2: NEWS INTELLIGENCE ENGINE
# ==============================================================================
class NewsIntelligenceEngine:
    """Advanced news analysis and intelligence system"""
    
    def __init__(self):
        self.impact_keywords = {
            'critical': ['breaking', 'urgent', 'emergency', 'crisis', 'crash', 'collapse'],
            'high': ['major', 'significant', 'massive', 'huge', 'dramatic', 'surge'],
            'medium': ['important', 'notable', 'considerable', 'substantial'],
            'low': ['minor', 'slight', 'small', 'limited', 'modest']
        }
        
        self.sentiment_keywords = {
            'very_positive': ['soar', 'surge', 'boom', 'breakthrough', 'triumph'],
            'positive': ['rise', 'gain', 'growth', 'increase', 'bullish', 'optimistic'],
            'neutral': ['stable', 'steady', 'unchanged', 'flat'],
            'negative': ['fall', 'drop', 'decline', 'bearish', 'pessimistic'],
            'very_negative': ['crash', 'plunge', 'collapse', 'disaster', 'crisis']
        }
        
        self.market_sectors = {
            'crypto': ['bitcoin', 'ethereum', 'cryptocurrency', 'blockchain', 'defi'],
            'stocks': ['stock', 'equity', 'shares', 'market', 'trading'],
            'oil': ['oil', 'crude', 'petroleum', 'energy', 'opec'],
            'gold': ['gold', 'precious metals', 'silver', 'platinum'],
            'health': ['health', 'biotech', 'pharma', 'medical', 'drug']
        }
        
        logging.info("NewsIntelligenceEngine initialized with advanced analysis.")

    def analyze_news_quality(self, news_articles: List[Dict]) -> Dict:
        """Comprehensive news quality and intelligence analysis"""
        try:
            if not news_articles:
                return {'quality_score': 0.0, 'analysis': 'No news articles provided'}
            
            analysis = {
                'total_articles': len(news_articles),
                'quality_distribution': {},
                'sentiment_analysis': {},
                'impact_analysis': {},
                'sector_coverage': {},
                'source_analysis': {},
                'temporal_analysis': {},
                'duplicate_detection': {},
                'overall_quality_score': 0.0
            }
            
            # Analyze each article
            article_scores = []
            sentiment_scores = []
            impact_scores = []
            
            for article in news_articles:
                # Individual article analysis
                article_quality = self._analyze_single_article(article)
                article_scores.append(article_quality['quality_score'])
                
                # Sentiment analysis
                sentiment = self._analyze_sentiment_advanced(article)
                sentiment_scores.append(sentiment)
                
                # Impact analysis
                impact = self._analyze_impact_advanced(article)
                impact_scores.append(impact)
            
            # Aggregate analysis
            analysis['overall_quality_score'] = statistics.mean(article_scores) if article_scores else 0.0
            analysis['sentiment_analysis'] = self._aggregate_sentiment(sentiment_scores)
            analysis['impact_analysis'] = self._aggregate_impact(impact_scores)
            analysis['sector_coverage'] = self._analyze_sector_coverage(news_articles)
            analysis['source_analysis'] = self._analyze_sources(news_articles)
            analysis['temporal_analysis'] = self._analyze_temporal_distribution(news_articles)
            analysis['duplicate_detection'] = self._detect_duplicates(news_articles)
            
            return analysis
            
        except Exception as e:
            logging.error(f"Error analyzing news quality: {e}")
            return {'quality_score': 0.0, 'analysis': f'Analysis failed: {e}'}

    def _analyze_single_article(self, article: Dict) -> Dict:
        """Analyze quality of a single news article"""
        quality_factors = {
            'completeness': 0.0,
            'credibility': 0.0,
            'relevance': 0.0,
            'freshness': 0.0
        }
        
        # Completeness check
        required_fields = ['title', 'description', 'source', 'publishedAt']
        present_fields = sum(1 for field in required_fields if field in article and article[field])
        quality_factors['completeness'] = present_fields / len(required_fields)
        
        # Source credibility
        source_name = article.get('source', {}).get('name', '').lower()
        if 'globe and mail' in source_name:
            quality_factors['credibility'] = 0.98
        elif any(source in source_name for source in ['reuters', 'bloomberg', 'cnbc']):
            quality_factors['credibility'] = 0.90
        else:
            quality_factors['credibility'] = 0.70
        
        # Content relevance (length and detail)
        description = article.get('description', '')
        if len(description) > 200:
            quality_factors['relevance'] = 1.0
        elif len(description) > 100:
            quality_factors['relevance'] = 0.8
        elif len(description) > 50:
            quality_factors['relevance'] = 0.6
        else:
            quality_factors['relevance'] = 0.3
        
        # Freshness
        try:
            published_at = article.get('publishedAt', '')
            if published_at:
                pub_time = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
                age_hours = (datetime.now() - pub_time).total_seconds() / 3600
                if age_hours < 6:
                    quality_factors['freshness'] = 1.0
                elif age_hours < 24:
                    quality_factors['freshness'] = 0.8
                elif age_hours < 72:
                    quality_factors['freshness'] = 0.6
                else:
                    quality_factors['freshness'] = 0.3
        except:
            quality_factors['freshness'] = 0.5
        
        # Calculate overall quality score
        quality_score = statistics.mean(quality_factors.values())
        
        return {
            'quality_score': quality_score,
            'factors': quality_factors
        }

    def _analyze_sentiment_advanced(self, article: Dict) -> Dict:
        """Advanced sentiment analysis of news article"""
        text = f"{article.get('title', '')} {article.get('description', '')}".lower()
        
        sentiment_scores = {
            'very_positive': 0,
            'positive': 0,
            'neutral': 0,
            'negative': 0,
            'very_negative': 0
        }
        
        for sentiment, keywords in self.sentiment_keywords.items():
            sentiment_scores[sentiment] = sum(1 for keyword in keywords if keyword in text)
        
        # Determine dominant sentiment
        max_sentiment = max(sentiment_scores, key=sentiment_scores.get)
        confidence = sentiment_scores[max_sentiment] / max(1, sum(sentiment_scores.values()))
        
        return {
            'sentiment': max_sentiment,
            'confidence': confidence,
            'scores': sentiment_scores
        }

    def _analyze_impact_advanced(self, article: Dict) -> Dict:
        """Advanced impact analysis of news article"""
        text = f"{article.get('title', '')} {article.get('description', '')}".lower()
        
        impact_scores = {
            'critical': 0,
            'high': 0,
            'medium': 0,
            'low': 0
        }
        
        for impact, keywords in self.impact_keywords.items():
            impact_scores[impact] = sum(1 for keyword in keywords if keyword in text)
        
        # Determine impact level
        max_impact = max(impact_scores, key=impact_scores.get)
        confidence = impact_scores[max_impact] / max(1, sum(impact_scores.values()))
        
        return {
            'impact': max_impact,
            'confidence': confidence,
            'scores': impact_scores
        }

    def _aggregate_sentiment(self, sentiment_scores: List[Dict]) -> Dict:
        """Aggregate sentiment analysis across all articles"""
        if not sentiment_scores:
            return {}
        
        sentiment_counts = {}
        for score in sentiment_scores:
            sentiment = score.get('sentiment', 'neutral')
            sentiment_counts[sentiment] = sentiment_counts.get(sentiment, 0) + 1
        
        total = len(sentiment_scores)
        sentiment_distribution = {k: v/total for k, v in sentiment_counts.items()}
        
        return {
            'distribution': sentiment_distribution,
            'dominant_sentiment': max(sentiment_counts, key=sentiment_counts.get),
            'sentiment_score': self._calculate_sentiment_score(sentiment_distribution)
        }

    def _calculate_sentiment_score(self, distribution: Dict) -> float:
        """Calculate overall sentiment score (-1 to 1)"""
        weights = {
            'very_negative': -1.0,
            'negative': -0.5,
            'neutral': 0.0,
            'positive': 0.5,
            'very_positive': 1.0
        }
        
        score = sum(distribution.get(sentiment, 0) * weight for sentiment, weight in weights.items())
        return score

    def _aggregate_impact(self, impact_scores: List[Dict]) -> Dict:
        """Aggregate impact analysis across all articles"""
        if not impact_scores:
            return {}
        
        impact_counts = {}
        for score in impact_scores:
            impact = score.get('impact', 'low')
            impact_counts[impact] = impact_counts.get(impact, 0) + 1
        
        total = len(impact_scores)
        impact_distribution = {k: v/total for k, v in impact_counts.items()}
        
        return {
            'distribution': impact_distribution,
            'dominant_impact': max(impact_counts, key=impact_counts.get)
        }

    def _analyze_sector_coverage(self, articles: List[Dict]) -> Dict:
        """Analyze which market sectors are covered in the news"""
        sector_counts = {sector: 0 for sector in self.market_sectors.keys()}
        
        for article in articles:
            text = f"{article.get('title', '')} {article.get('description', '')}".lower()
            for sector, keywords in self.market_sectors.items():
                if any(keyword in text for keyword in keywords):
                    sector_counts[sector] += 1
        
        total_articles = len(articles)
        sector_coverage = {k: v/total_articles for k, v in sector_counts.items()}
        
        return {
            'coverage': sector_coverage,
            'most_covered': max(sector_counts, key=sector_counts.get) if any(sector_counts.values()) else None
        }

    def _analyze_sources(self, articles: List[Dict]) -> Dict:
        """Analyze news source distribution and credibility"""
        source_counts = {}
        source_credibility = {}
        
        for article in articles:
            source_name = article.get('source', {}).get('name', 'Unknown')
            source_counts[source_name] = source_counts.get(source_name, 0) + 1
            
            # Assign credibility score
            if 'Globe and Mail' in source_name:
                source_credibility[source_name] = 0.98
            elif any(name in source_name for name in ['Reuters', 'Bloomberg']):
                source_credibility[source_name] = 0.90
            elif 'CNBC' in source_name:
                source_credibility[source_name] = 0.85
            else:
                source_credibility[source_name] = 0.70
        
        # Calculate weighted credibility
        total_articles = sum(source_counts.values())
        weighted_credibility = sum(
            (count / total_articles) * source_credibility.get(source, 0.70)
            for source, count in source_counts.items()
        )
        
        return {
            'source_distribution': source_counts,
            'source_credibility': source_credibility,
            'weighted_credibility': weighted_credibility,
            'primary_source': max(source_counts, key=source_counts.get) if source_counts else None
        }

    def _analyze_temporal_distribution(self, articles: List[Dict]) -> Dict:
        """Analyze temporal distribution of news articles"""
        time_buckets = {
            'last_hour': 0,
            'last_6_hours': 0,
            'last_24_hours': 0,
            'last_week': 0,
            'older': 0
        }
        
        now = datetime.now()
        
        for article in articles:
            try:
                published_at = article.get('publishedAt', '')
                if published_at:
                    pub_time = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
                    age_hours = (now - pub_time).total_seconds() / 3600
                    
                    if age_hours < 1:
                        time_buckets['last_hour'] += 1
                    elif age_hours < 6:
                        time_buckets['last_6_hours'] += 1
                    elif age_hours < 24:
                        time_buckets['last_24_hours'] += 1
                    elif age_hours < 168:  # 1 week
                        time_buckets['last_week'] += 1
                    else:
                        time_buckets['older'] += 1
            except:
                time_buckets['older'] += 1
        
        return time_buckets

    def _detect_duplicates(self, articles: List[Dict]) -> Dict:
        """Detect duplicate or very similar articles"""
        duplicates = []
        seen_hashes = set()
        
        for i, article in enumerate(articles):
            # Create content hash
            content = f"{article.get('title', '')} {article.get('description', '')}"
            content_hash = hashlib.md5(content.encode()).hexdigest()
            
            if content_hash in seen_hashes:
                duplicates.append({
                    'index': i,
                    'title': article.get('title', ''),
                    'hash': content_hash
                })
            else:
                seen_hashes.add(content_hash)
        
        return {
            'duplicate_count': len(duplicates),
            'duplicate_rate': len(duplicates) / len(articles) if articles else 0,
            'duplicates': duplicates
        }
