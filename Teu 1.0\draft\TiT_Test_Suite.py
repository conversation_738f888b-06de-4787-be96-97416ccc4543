# TiT Test Suite 1.0.1: Comprehensive Testing for All TiT Applications
# Version: 1.0.1 (Complete Test Suite)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
# Date: 2025-06-17
#
# Description:
# Comprehensive test suite to verify all TiT applications are working correctly,
# test performance, validate data integrity, and ensure smooth user experience.

import os
import sys
import subprocess
import time
import logging
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox
from ttkthemes import ThemedTk

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tit_test_results.log', mode='w'),
        logging.StreamHandler()
    ]
)

class TiTTestSuite:
    """Comprehensive test suite for all TiT applications."""
    
    def __init__(self):
        self.test_results = {}
        self.apps_to_test = {
            'Crypto App': 'Teu 1.0.1.py',
            'Stock App': 'TiT_Stock_App_1.0.1.py',
            'Oil App': 'TiT_Oil_App_1.0.1.py',
            'Gold App': 'TiT_Gold_App_1.0.1.py',
            'Health App': 'TiT_Health_App_1.0.1.py',
            'Defense App': 'TiT_Defense_App_1.0.1.py',
            'Science App': 'TiT_Science_App_1.0.1.py',
            'Launcher': 'TiT_Launcher_1.0.1.py'
        }
        
    def run_comprehensive_tests(self):
        """Run comprehensive tests on all TiT applications."""
        print("🚀 Starting TiT Suite Comprehensive Testing...")
        print("=" * 80)
        
        # Test 1: File Existence
        self.test_file_existence()
        
        # Test 2: Syntax Validation
        self.test_syntax_validation()
        
        # Test 3: Import Testing
        self.test_imports()
        
        # Test 4: Launch Testing
        self.test_app_launches()
        
        # Test 5: Performance Testing
        self.test_performance()
        
        # Generate final report
        self.generate_test_report()
        
    def test_file_existence(self):
        """Test if all application files exist."""
        print("\n📁 Testing File Existence...")
        
        for app_name, filename in self.apps_to_test.items():
            if os.path.exists(filename):
                self.test_results[f"{app_name}_file_exists"] = "✅ PASS"
                print(f"✅ {app_name}: {filename} - EXISTS")
            else:
                self.test_results[f"{app_name}_file_exists"] = "❌ FAIL"
                print(f"❌ {app_name}: {filename} - MISSING")
                
    def test_syntax_validation(self):
        """Test Python syntax validation for all files."""
        print("\n🔍 Testing Python Syntax...")
        
        for app_name, filename in self.apps_to_test.items():
            if os.path.exists(filename):
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        code = f.read()
                    compile(code, filename, 'exec')
                    self.test_results[f"{app_name}_syntax"] = "✅ PASS"
                    print(f"✅ {app_name}: Syntax Valid")
                except SyntaxError as e:
                    self.test_results[f"{app_name}_syntax"] = f"❌ FAIL: {e}"
                    print(f"❌ {app_name}: Syntax Error - {e}")
                except Exception as e:
                    self.test_results[f"{app_name}_syntax"] = f"❌ FAIL: {e}"
                    print(f"❌ {app_name}: Error - {e}")
            else:
                self.test_results[f"{app_name}_syntax"] = "⏭️ SKIP: File not found"
                
    def test_imports(self):
        """Test if all required imports are available."""
        print("\n📦 Testing Required Imports...")
        
        required_modules = [
            'tkinter', 'ttkthemes', 'requests', 'pandas', 
            'yfinance', 'feedparser', 'matplotlib', 'google.generativeai'
        ]
        
        for module in required_modules:
            try:
                __import__(module)
                self.test_results[f"import_{module}"] = "✅ PASS"
                print(f"✅ {module}: Available")
            except ImportError:
                self.test_results[f"import_{module}"] = "❌ FAIL"
                print(f"❌ {module}: Missing")
                
    def test_app_launches(self):
        """Test if applications can launch without immediate errors."""
        print("\n🚀 Testing Application Launches...")
        
        for app_name, filename in self.apps_to_test.items():
            if os.path.exists(filename):
                try:
                    print(f"🔄 Testing {app_name} launch...")
                    
                    # Launch app and check if it starts without immediate errors
                    process = subprocess.Popen(
                        [sys.executable, filename],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )
                    
                    # Wait a short time to see if it crashes immediately
                    time.sleep(3)
                    
                    if process.poll() is None:
                        # Process is still running, likely successful
                        self.test_results[f"{app_name}_launch"] = "✅ PASS"
                        print(f"✅ {app_name}: Launched Successfully")
                        
                        # Terminate the process
                        process.terminate()
                        try:
                            process.wait(timeout=5)
                        except subprocess.TimeoutExpired:
                            process.kill()
                    else:
                        # Process exited immediately, likely an error
                        stdout, stderr = process.communicate()
                        self.test_results[f"{app_name}_launch"] = f"❌ FAIL: {stderr[:100]}"
                        print(f"❌ {app_name}: Launch Failed - {stderr[:100]}")
                        
                except Exception as e:
                    self.test_results[f"{app_name}_launch"] = f"❌ FAIL: {e}"
                    print(f"❌ {app_name}: Launch Error - {e}")
            else:
                self.test_results[f"{app_name}_launch"] = "⏭️ SKIP: File not found"
                
    def test_performance(self):
        """Test performance metrics."""
        print("\n⚡ Testing Performance Metrics...")
        
        # Test file sizes
        total_size = 0
        for app_name, filename in self.apps_to_test.items():
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                total_size += size
                size_mb = size / (1024 * 1024)
                print(f"📊 {app_name}: {size_mb:.2f} MB")
                
        print(f"📊 Total Suite Size: {total_size / (1024 * 1024):.2f} MB")
        self.test_results["total_size_mb"] = f"{total_size / (1024 * 1024):.2f}"
        
        # Performance rating
        if total_size < 50 * 1024 * 1024:  # Less than 50MB
            self.test_results["performance_rating"] = "✅ EXCELLENT"
        elif total_size < 100 * 1024 * 1024:  # Less than 100MB
            self.test_results["performance_rating"] = "✅ GOOD"
        else:
            self.test_results["performance_rating"] = "⚠️ LARGE"
            
    def generate_test_report(self):
        """Generate comprehensive test report."""
        print("\n" + "=" * 80)
        print("📊 TiT SUITE COMPREHENSIVE TEST REPORT")
        print("=" * 80)
        
        # Count results
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results.values() if r.startswith("✅")])
        failed_tests = len([r for r in self.test_results.values() if r.startswith("❌")])
        skipped_tests = len([r for r in self.test_results.values() if r.startswith("⏭️")])
        
        print(f"📈 SUMMARY:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   ⏭️ Skipped: {skipped_tests}")
        print(f"   📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n📋 DETAILED RESULTS:")
        for test_name, result in self.test_results.items():
            print(f"   {test_name}: {result}")
            
        # Overall verdict
        if failed_tests == 0:
            verdict = "🎉 ALL TESTS PASSED! TiT Suite is ready for production!"
        elif failed_tests <= 2:
            verdict = "⚠️ Minor issues detected. TiT Suite is mostly ready."
        else:
            verdict = "❌ Multiple issues detected. Please review and fix."
            
        print(f"\n🎯 FINAL VERDICT: {verdict}")
        
        # Save report to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_filename = f"TiT_Test_Report_{timestamp}.txt"
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("TiT SUITE COMPREHENSIVE TEST REPORT\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Author: Anh Quang (Nguyen Le Vinh Quang)\n\n")
            
            f.write(f"SUMMARY:\n")
            f.write(f"Total Tests: {total_tests}\n")
            f.write(f"Passed: {passed_tests}\n")
            f.write(f"Failed: {failed_tests}\n")
            f.write(f"Skipped: {skipped_tests}\n")
            f.write(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%\n\n")
            
            f.write("DETAILED RESULTS:\n")
            for test_name, result in self.test_results.items():
                f.write(f"{test_name}: {result}\n")
                
            f.write(f"\nFINAL VERDICT: {verdict}\n")
            
        print(f"\n📄 Test report saved to: {report_filename}")

class TiTTestGUI:
    """GUI for TiT Test Suite."""
    
    def __init__(self):
        self.root = ThemedTk(theme='arc')
        self.root.title("TiT Suite Test Runner 1.0.1")
        self.root.geometry("800x600")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the test GUI."""
        # Header
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill=tk.X, padx=20, pady=20)
        
        title_label = ttk.Label(
            header_frame,
            text="🧪 TiT Suite Test Runner 1.0.1",
            font=('Segoe UI', 16, 'bold')
        )
        title_label.pack()
        
        subtitle_label = ttk.Label(
            header_frame,
            text="Comprehensive Testing for All TiT Applications",
            font=('Segoe UI', 12)
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Test button
        test_frame = ttk.Frame(self.root)
        test_frame.pack(fill=tk.X, padx=20, pady=20)
        
        self.test_btn = ttk.Button(
            test_frame,
            text="🚀 Run Comprehensive Tests",
            command=self.run_tests
        )
        self.test_btn.pack()
        
        # Results area
        results_frame = ttk.LabelFrame(self.root, text="Test Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def run_tests(self):
        """Run the comprehensive tests."""
        self.test_btn.config(state='disabled', text='🔄 Running Tests...')
        self.results_text.delete(1.0, tk.END)
        
        def test_worker():
            import io
            import sys
            
            # Capture output
            old_stdout = sys.stdout
            sys.stdout = captured_output = io.StringIO()
            
            try:
                # Run tests
                test_suite = TiTTestSuite()
                test_suite.run_comprehensive_tests()
                
                # Get output
                output = captured_output.getvalue()
                
                # Update GUI
                self.root.after(0, lambda: self.update_results(output))
                
            finally:
                sys.stdout = old_stdout
                self.root.after(0, lambda: self.test_btn.config(state='normal', text='🚀 Run Comprehensive Tests'))
        
        import threading
        threading.Thread(target=test_worker, daemon=True).start()
        
    def update_results(self, output):
        """Update the results display."""
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(1.0, output)
        
    def run(self):
        """Run the GUI."""
        self.root.mainloop()

if __name__ == "__main__":
    print("🧪 TiT Suite Test Runner 1.0.1")
    print("Choose testing mode:")
    print("1. Command Line Testing")
    print("2. GUI Testing")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "2":
        # Run GUI
        gui = TiTTestGUI()
        gui.run()
    else:
        # Run command line tests
        test_suite = TiTTestSuite()
        test_suite.run_comprehensive_tests()
