#!/usr/bin/env python3
# Advanced AI Analysis Integration for TiT Suite
# Provides comprehensive AI-powered analysis across all sectors

import json
import time
import logging
import threading
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from concurrent.futures import ThreadPoolExecutor
import requests

class AdvancedAIAnalysisEngine:
    """Advanced AI analysis engine with predictive capabilities"""
    
    def __init__(self, api_key=None):
        self.api_key = api_key
        self.analysis_cache = {}
        self.prediction_cache = {}
        self.cache_duration = 600  # 10 minutes
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # AI analysis models
        self.analysis_models = {
            'sentiment_analysis': {
                'positive_keywords': ['bullish', 'growth', 'surge', 'rally', 'breakthrough', 'approval', 'success'],
                'negative_keywords': ['bearish', 'decline', 'crash', 'crisis', 'failure', 'rejection', 'concern'],
                'neutral_keywords': ['stable', 'unchanged', 'sideways', 'consolidation', 'waiting']
            },
            'trend_analysis': {
                'bullish_patterns': ['higher_highs', 'breakout', 'golden_cross', 'support_bounce'],
                'bearish_patterns': ['lower_lows', 'breakdown', 'death_cross', 'resistance_rejection'],
                'neutral_patterns': ['sideways', 'triangle', 'range_bound', 'consolidation']
            },
            'volatility_analysis': {
                'low_volatility': 0.02,    # 2% daily moves
                'medium_volatility': 0.05,  # 5% daily moves
                'high_volatility': 0.10     # 10% daily moves
            }
        }
        
        # Sector-specific AI models
        self.sector_models = {
            'crypto': {
                'key_factors': ['adoption', 'regulation', 'institutional_interest', 'technology_development'],
                'sentiment_multiplier': 1.5,  # Crypto is more sentiment-driven
                'volatility_threshold': 0.15
            },
            'stocks': {
                'key_factors': ['earnings', 'economic_indicators', 'fed_policy', 'geopolitical_events'],
                'sentiment_multiplier': 1.0,
                'volatility_threshold': 0.05
            },
            'oil': {
                'key_factors': ['supply_demand', 'opec_decisions', 'geopolitical_tensions', 'economic_activity'],
                'sentiment_multiplier': 1.2,
                'volatility_threshold': 0.08
            },
            'gold': {
                'key_factors': ['inflation', 'currency_strength', 'interest_rates', 'crisis_events'],
                'sentiment_multiplier': 0.8,
                'volatility_threshold': 0.03
            },
            'health': {
                'key_factors': ['clinical_trials', 'fda_approvals', 'research_breakthroughs', 'healthcare_policy'],
                'sentiment_multiplier': 1.3,
                'volatility_threshold': 0.12
            },
            'defense': {
                'key_factors': ['geopolitical_tensions', 'defense_budgets', 'contracts', 'technology_advances'],
                'sentiment_multiplier': 1.1,
                'volatility_threshold': 0.06
            },
            'science': {
                'key_factors': ['innovation', 'funding', 'breakthroughs', 'space_missions'],
                'sentiment_multiplier': 1.4,
                'volatility_threshold': 0.10
            }
        }
    
    def analyze_sector_sentiment(self, sector, news_data, market_data):
        """Analyze sentiment for a specific sector"""
        try:
            sentiment_score = 0
            confidence = 0
            
            # Analyze news sentiment
            if news_data:
                news_sentiment = self.analyze_news_sentiment(news_data)
                sentiment_score += news_sentiment['score'] * 0.6  # 60% weight to news
                confidence += news_sentiment['confidence'] * 0.6
            
            # Analyze market data sentiment
            if market_data:
                market_sentiment = self.analyze_market_sentiment(market_data)
                sentiment_score += market_sentiment['score'] * 0.4  # 40% weight to market
                confidence += market_sentiment['confidence'] * 0.4
            
            # Apply sector-specific multiplier
            sector_config = self.sector_models.get(sector, {})
            multiplier = sector_config.get('sentiment_multiplier', 1.0)
            sentiment_score *= multiplier
            
            # Normalize sentiment score
            sentiment_score = max(-1, min(1, sentiment_score))
            
            # Classify sentiment
            if sentiment_score > 0.3:
                sentiment_label = "Bullish"
            elif sentiment_score < -0.3:
                sentiment_label = "Bearish"
            else:
                sentiment_label = "Neutral"
            
            return {
                'sector': sector,
                'sentiment_score': sentiment_score,
                'sentiment_label': sentiment_label,
                'confidence': confidence,
                'timestamp': datetime.now().isoformat(),
                'factors_analyzed': sector_config.get('key_factors', [])
            }
            
        except Exception as e:
            logging.error(f"Error analyzing sentiment for {sector}: {e}")
            return {'error': str(e)}
    
    def analyze_news_sentiment(self, news_data):
        """Analyze sentiment from news articles"""
        try:
            positive_count = 0
            negative_count = 0
            total_articles = len(news_data)
            
            if total_articles == 0:
                return {'score': 0, 'confidence': 0}
            
            positive_keywords = self.analysis_models['sentiment_analysis']['positive_keywords']
            negative_keywords = self.analysis_models['sentiment_analysis']['negative_keywords']
            
            for article in news_data:
                title = article.get('title', '').lower()
                description = article.get('description', '').lower()
                text = f"{title} {description}"
                
                # Count positive and negative keywords
                pos_matches = sum(1 for keyword in positive_keywords if keyword in text)
                neg_matches = sum(1 for keyword in negative_keywords if keyword in text)
                
                if pos_matches > neg_matches:
                    positive_count += 1
                elif neg_matches > pos_matches:
                    negative_count += 1
            
            # Calculate sentiment score
            net_sentiment = positive_count - negative_count
            sentiment_score = net_sentiment / total_articles
            
            # Calculate confidence based on article count and sentiment clarity
            confidence = min(1.0, total_articles / 20) * (abs(sentiment_score) + 0.1)
            
            return {
                'score': sentiment_score,
                'confidence': confidence,
                'positive_articles': positive_count,
                'negative_articles': negative_count,
                'total_articles': total_articles
            }
            
        except Exception as e:
            logging.error(f"Error analyzing news sentiment: {e}")
            return {'score': 0, 'confidence': 0, 'error': str(e)}
    
    def analyze_market_sentiment(self, market_data):
        """Analyze sentiment from market price movements"""
        try:
            if not market_data:
                return {'score': 0, 'confidence': 0}
            
            price_changes = []
            volumes = []
            
            for asset, data in market_data.items():
                if isinstance(data, dict):
                    change = data.get('change_percent', 0)
                    volume = data.get('volume_24h', 0)
                    
                    price_changes.append(change)
                    volumes.append(volume)
            
            if not price_changes:
                return {'score': 0, 'confidence': 0}
            
            # Calculate weighted sentiment based on price changes and volumes
            if volumes and sum(volumes) > 0:
                # Volume-weighted sentiment
                total_volume = sum(volumes)
                weighted_sentiment = sum(change * (vol / total_volume) 
                                       for change, vol in zip(price_changes, volumes))
            else:
                # Simple average sentiment
                weighted_sentiment = sum(price_changes) / len(price_changes)
            
            # Normalize sentiment score
            sentiment_score = weighted_sentiment / 100  # Convert percentage to -1 to 1 scale
            sentiment_score = max(-1, min(1, sentiment_score))
            
            # Calculate confidence based on consistency of moves
            consistency = 1 - (np.std(price_changes) / (np.mean(np.abs(price_changes)) + 1))
            confidence = max(0, min(1, consistency))
            
            return {
                'score': sentiment_score,
                'confidence': confidence,
                'average_change': np.mean(price_changes),
                'volatility': np.std(price_changes),
                'assets_analyzed': len(price_changes)
            }
            
        except Exception as e:
            logging.error(f"Error analyzing market sentiment: {e}")
            return {'score': 0, 'confidence': 0, 'error': str(e)}
    
    def generate_predictions(self, sector, historical_data, sentiment_data):
        """Generate AI-powered predictions for a sector"""
        try:
            predictions = {
                'sector': sector,
                'timestamp': datetime.now().isoformat(),
                'short_term': {},  # 1-7 days
                'medium_term': {},  # 1-4 weeks
                'long_term': {},   # 1-3 months
                'confidence_level': 'medium',
                'key_factors': []
            }
            
            # Analyze current sentiment
            current_sentiment = sentiment_data.get('sentiment_score', 0)
            sentiment_confidence = sentiment_data.get('confidence', 0.5)
            
            # Generate short-term prediction (1-7 days)
            short_term_direction = 'up' if current_sentiment > 0.2 else 'down' if current_sentiment < -0.2 else 'sideways'
            short_term_magnitude = abs(current_sentiment) * 10  # Convert to percentage
            
            predictions['short_term'] = {
                'direction': short_term_direction,
                'magnitude': f"{short_term_magnitude:.1f}%",
                'probability': sentiment_confidence * 0.8,
                'timeframe': '1-7 days'
            }
            
            # Generate medium-term prediction (1-4 weeks)
            # Assume some mean reversion
            medium_term_sentiment = current_sentiment * 0.7  # Partial reversion
            medium_term_direction = 'up' if medium_term_sentiment > 0.1 else 'down' if medium_term_sentiment < -0.1 else 'sideways'
            
            predictions['medium_term'] = {
                'direction': medium_term_direction,
                'magnitude': f"{abs(medium_term_sentiment) * 15:.1f}%",
                'probability': sentiment_confidence * 0.6,
                'timeframe': '1-4 weeks'
            }
            
            # Generate long-term prediction (1-3 months)
            # Consider fundamental factors
            sector_config = self.sector_models.get(sector, {})
            long_term_factors = sector_config.get('key_factors', [])
            
            # Simplified long-term prediction based on sector fundamentals
            if sector == 'crypto':
                long_term_bias = 0.1  # Slight positive bias for crypto long-term
            elif sector == 'stocks':
                long_term_bias = 0.05  # Slight positive bias for stocks
            elif sector == 'gold':
                long_term_bias = 0.02  # Small positive bias for gold
            else:
                long_term_bias = 0.0
            
            long_term_sentiment = (current_sentiment * 0.3) + long_term_bias
            long_term_direction = 'up' if long_term_sentiment > 0.05 else 'down' if long_term_sentiment < -0.05 else 'sideways'
            
            predictions['long_term'] = {
                'direction': long_term_direction,
                'magnitude': f"{abs(long_term_sentiment) * 20:.1f}%",
                'probability': 0.5,  # Lower confidence for long-term
                'timeframe': '1-3 months'
            }
            
            # Set overall confidence
            avg_confidence = (predictions['short_term']['probability'] + 
                            predictions['medium_term']['probability'] + 
                            predictions['long_term']['probability']) / 3
            
            if avg_confidence > 0.7:
                predictions['confidence_level'] = 'high'
            elif avg_confidence > 0.5:
                predictions['confidence_level'] = 'medium'
            else:
                predictions['confidence_level'] = 'low'
            
            predictions['key_factors'] = long_term_factors
            
            return predictions
            
        except Exception as e:
            logging.error(f"Error generating predictions for {sector}: {e}")
            return {'error': str(e)}
    
    def generate_comprehensive_analysis(self, sector, news_data, market_data):
        """Generate comprehensive AI analysis for a sector"""
        try:
            # Analyze sentiment
            sentiment_analysis = self.analyze_sector_sentiment(sector, news_data, market_data)
            
            # Generate predictions
            predictions = self.generate_predictions(sector, market_data, sentiment_analysis)
            
            # Combine into comprehensive analysis
            analysis = {
                'sector': sector,
                'timestamp': datetime.now().isoformat(),
                'sentiment_analysis': sentiment_analysis,
                'predictions': predictions,
                'risk_assessment': self.assess_risk_level(sector, market_data),
                'recommendations': self.generate_recommendations(sector, sentiment_analysis, predictions)
            }
            
            return analysis
            
        except Exception as e:
            logging.error(f"Error generating comprehensive analysis for {sector}: {e}")
            return {'error': str(e)}
    
    def assess_risk_level(self, sector, market_data):
        """Assess risk level for a sector"""
        try:
            if not market_data:
                return {'level': 'medium', 'factors': []}
            
            # Calculate volatility
            price_changes = []
            for asset, data in market_data.items():
                if isinstance(data, dict):
                    change = data.get('change_percent', 0)
                    price_changes.append(abs(change))
            
            if not price_changes:
                return {'level': 'medium', 'factors': []}
            
            avg_volatility = np.mean(price_changes) / 100  # Convert to decimal
            sector_config = self.sector_models.get(sector, {})
            volatility_threshold = sector_config.get('volatility_threshold', 0.05)
            
            if avg_volatility > volatility_threshold * 2:
                risk_level = 'high'
                risk_factors = ['High volatility', 'Significant price swings']
            elif avg_volatility > volatility_threshold:
                risk_level = 'medium'
                risk_factors = ['Moderate volatility']
            else:
                risk_level = 'low'
                risk_factors = ['Low volatility', 'Stable price action']
            
            return {
                'level': risk_level,
                'volatility': avg_volatility,
                'factors': risk_factors,
                'threshold': volatility_threshold
            }
            
        except Exception as e:
            logging.error(f"Error assessing risk for {sector}: {e}")
            return {'level': 'medium', 'factors': [], 'error': str(e)}
    
    def generate_recommendations(self, sector, sentiment_analysis, predictions):
        """Generate actionable recommendations"""
        try:
            recommendations = []
            
            sentiment_score = sentiment_analysis.get('sentiment_score', 0)
            short_term_direction = predictions.get('short_term', {}).get('direction', 'sideways')
            confidence = sentiment_analysis.get('confidence', 0.5)
            
            # Generate recommendations based on analysis
            if sentiment_score > 0.3 and short_term_direction == 'up' and confidence > 0.6:
                recommendations.append(f"Consider increasing {sector} allocation")
                recommendations.append("Strong bullish sentiment detected")
            elif sentiment_score < -0.3 and short_term_direction == 'down' and confidence > 0.6:
                recommendations.append(f"Consider reducing {sector} exposure")
                recommendations.append("Strong bearish sentiment detected")
            else:
                recommendations.append(f"Maintain current {sector} position")
                recommendations.append("Mixed or unclear signals")
            
            # Add sector-specific recommendations
            if sector == 'crypto':
                recommendations.append("Monitor regulatory developments")
            elif sector == 'gold':
                recommendations.append("Watch inflation indicators")
            elif sector == 'oil':
                recommendations.append("Track geopolitical events")
            
            return recommendations
            
        except Exception as e:
            logging.error(f"Error generating recommendations for {sector}: {e}")
            return [f"Error generating recommendations: {e}"]

# Global instance
advanced_ai_analysis = AdvancedAIAnalysisEngine()

def analyze_sector_ai(sector, news_data=None, market_data=None):
    """Analyze a sector using AI"""
    return advanced_ai_analysis.generate_comprehensive_analysis(sector, news_data, market_data)

def get_sector_predictions(sector, historical_data=None, sentiment_data=None):
    """Get AI predictions for a sector"""
    return advanced_ai_analysis.generate_predictions(sector, historical_data, sentiment_data)

if __name__ == "__main__":
    print("🚀 Testing Advanced AI Analysis System...")
    
    # Test crypto analysis
    test_market_data = {
        'bitcoin': {'change_percent': 5.2, 'volume_24h': 1000000},
        'ethereum': {'change_percent': 3.8, 'volume_24h': 800000}
    }
    
    analysis = advanced_ai_analysis.generate_comprehensive_analysis('crypto', [], test_market_data)
    print(f"📊 Analysis generated for crypto: {analysis.get('sentiment_analysis', {}).get('sentiment_label', 'Unknown')}")
    
    print("✅ Advanced AI analysis test completed!")
