#!/usr/bin/env python3
# Cross-App Intelligence System for TiT Suite
# Provides comprehensive correlation analysis and market intelligence

import json
import time
import logging
import threading
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from concurrent.futures import ThreadPoolExecutor
import requests
import yfinance as yf

class CrossAppIntelligenceEngine:
    """Advanced cross-app intelligence with market correlation analysis"""
    
    def __init__(self):
        self.correlation_cache = {}
        self.market_data_cache = {}
        self.intelligence_cache = {}
        self.cache_duration = 300  # 5 minutes
        self.executor = ThreadPoolExecutor(max_workers=20)
        
        # Market sectors and their relationships
        self.sector_relationships = {
            'crypto': {
                'correlated_with': ['tech_stocks', 'gold', 'risk_assets'],
                'inverse_correlated': ['bonds', 'usd_strength'],
                'leading_indicators': ['bitcoin', 'ethereum'],
                'sentiment_drivers': ['regulation', 'institutional_adoption']
            },
            'stocks': {
                'correlated_with': ['economic_growth', 'earnings', 'oil'],
                'inverse_correlated': ['interest_rates', 'inflation'],
                'leading_indicators': ['sp500', 'nasdaq', 'vix'],
                'sentiment_drivers': ['fed_policy', 'geopolitical_events']
            },
            'oil': {
                'correlated_with': ['energy_stocks', 'inflation', 'economic_activity'],
                'inverse_correlated': ['usd_strength', 'renewable_energy'],
                'leading_indicators': ['wti_crude', 'brent_crude'],
                'sentiment_drivers': ['opec_decisions', 'geopolitical_tensions']
            },
            'gold': {
                'correlated_with': ['inflation', 'currency_debasement', 'uncertainty'],
                'inverse_correlated': ['interest_rates', 'usd_strength'],
                'leading_indicators': ['gold_futures', 'mining_stocks'],
                'sentiment_drivers': ['central_bank_policy', 'crisis_events']
            },
            'health': {
                'correlated_with': ['aging_population', 'healthcare_spending'],
                'inverse_correlated': ['healthcare_costs'],
                'leading_indicators': ['biotech_index', 'fda_approvals'],
                'sentiment_drivers': ['drug_approvals', 'clinical_trials']
            },
            'defense': {
                'correlated_with': ['geopolitical_tensions', 'defense_spending'],
                'inverse_correlated': ['peace_agreements'],
                'leading_indicators': ['defense_contracts', 'military_budgets'],
                'sentiment_drivers': ['conflicts', 'arms_deals']
            },
            'science': {
                'correlated_with': ['innovation', 'rd_spending', 'tech_adoption'],
                'inverse_correlated': ['regulatory_restrictions'],
                'leading_indicators': ['patent_filings', 'research_funding'],
                'sentiment_drivers': ['breakthrough_discoveries', 'space_missions']
            }
        }
        
        # Key market indicators for correlation analysis
        self.market_indicators = {
            'crypto': ['BTC-USD', 'ETH-USD'],
            'stocks': ['^GSPC', '^DJI', '^IXIC', '^VIX'],
            'oil': ['CL=F', 'BZ=F'],
            'gold': ['GC=F', 'GOLD'],
            'bonds': ['^TNX', '^TYX'],
            'currencies': ['EURUSD=X', 'GBPUSD=X', 'JPYUSD=X']
        }
    
    def fetch_market_data(self, symbols, period="1mo"):
        """Fetch market data for correlation analysis"""
        try:
            data = {}
            for symbol in symbols:
                try:
                    ticker = yf.Ticker(symbol)
                    hist = ticker.history(period=period)
                    if not hist.empty:
                        data[symbol] = hist['Close'].pct_change().dropna()
                except Exception as e:
                    logging.warning(f"Error fetching {symbol}: {e}")
                    continue
            return data
        except Exception as e:
            logging.error(f"Error fetching market data: {e}")
            return {}
    
    def calculate_correlations(self):
        """Calculate cross-sector correlations"""
        try:
            # Collect all symbols
            all_symbols = []
            for sector_symbols in self.market_indicators.values():
                all_symbols.extend(sector_symbols)
            
            # Fetch data
            market_data = self.fetch_market_data(all_symbols)
            
            if len(market_data) < 2:
                return {}
            
            # Create DataFrame
            df = pd.DataFrame(market_data)
            
            # Calculate correlation matrix
            correlation_matrix = df.corr()
            
            # Organize by sectors
            sector_correlations = {}
            for sector1, symbols1 in self.market_indicators.items():
                sector_correlations[sector1] = {}
                for sector2, symbols2 in self.market_indicators.items():
                    if sector1 != sector2:
                        correlations = []
                        for s1 in symbols1:
                            for s2 in symbols2:
                                if s1 in correlation_matrix.index and s2 in correlation_matrix.columns:
                                    corr = correlation_matrix.loc[s1, s2]
                                    if not pd.isna(corr):
                                        correlations.append(corr)
                        
                        if correlations:
                            avg_correlation = np.mean(correlations)
                            sector_correlations[sector1][sector2] = {
                                'correlation': avg_correlation,
                                'strength': self.classify_correlation_strength(avg_correlation),
                                'sample_size': len(correlations)
                            }
            
            return sector_correlations
            
        except Exception as e:
            logging.error(f"Error calculating correlations: {e}")
            return {}
    
    def classify_correlation_strength(self, correlation):
        """Classify correlation strength"""
        abs_corr = abs(correlation)
        if abs_corr >= 0.8:
            return "Very Strong"
        elif abs_corr >= 0.6:
            return "Strong"
        elif abs_corr >= 0.4:
            return "Moderate"
        elif abs_corr >= 0.2:
            return "Weak"
        else:
            return "Very Weak"
    
    def analyze_market_regime(self):
        """Analyze current market regime"""
        try:
            # Fetch key indicators
            key_symbols = ['^GSPC', '^VIX', 'BTC-USD', 'GC=F', '^TNX']
            market_data = self.fetch_market_data(key_symbols, period="3mo")
            
            regime_analysis = {
                'timestamp': datetime.now().isoformat(),
                'regime_type': 'Unknown',
                'confidence': 0.0,
                'indicators': {},
                'recommendations': []
            }
            
            if '^VIX' in market_data:
                vix_current = market_data['^VIX'].iloc[-1] if len(market_data['^VIX']) > 0 else 20
                vix_avg = market_data['^VIX'].mean() if len(market_data['^VIX']) > 0 else 20
                
                regime_analysis['indicators']['vix'] = {
                    'current': vix_current,
                    'average': vix_avg,
                    'interpretation': 'High Fear' if vix_current > 25 else 'Low Fear'
                }
            
            if '^GSPC' in market_data:
                sp500_returns = market_data['^GSPC']
                volatility = sp500_returns.std() * np.sqrt(252)  # Annualized
                trend = 'Bullish' if sp500_returns.iloc[-5:].mean() > 0 else 'Bearish'
                
                regime_analysis['indicators']['stocks'] = {
                    'volatility': volatility,
                    'trend': trend,
                    'recent_performance': sp500_returns.iloc[-5:].mean()
                }
            
            # Determine regime
            if regime_analysis['indicators'].get('vix', {}).get('current', 20) > 30:
                regime_analysis['regime_type'] = 'Crisis/High Volatility'
                regime_analysis['confidence'] = 0.8
                regime_analysis['recommendations'] = [
                    'Consider defensive assets',
                    'Reduce risk exposure',
                    'Monitor for opportunities'
                ]
            elif regime_analysis['indicators'].get('stocks', {}).get('trend') == 'Bullish':
                regime_analysis['regime_type'] = 'Risk-On/Growth'
                regime_analysis['confidence'] = 0.7
                regime_analysis['recommendations'] = [
                    'Consider growth assets',
                    'Crypto may outperform',
                    'Monitor for overheating'
                ]
            else:
                regime_analysis['regime_type'] = 'Risk-Off/Defensive'
                regime_analysis['confidence'] = 0.6
                regime_analysis['recommendations'] = [
                    'Consider safe havens',
                    'Gold may outperform',
                    'Reduce crypto exposure'
                ]
            
            return regime_analysis
            
        except Exception as e:
            logging.error(f"Error analyzing market regime: {e}")
            return {'error': str(e)}
    
    def generate_cross_sector_insights(self):
        """Generate insights across all sectors"""
        try:
            correlations = self.calculate_correlations()
            regime = self.analyze_market_regime()
            
            insights = {
                'timestamp': datetime.now().isoformat(),
                'market_regime': regime,
                'sector_correlations': correlations,
                'key_insights': [],
                'trading_opportunities': [],
                'risk_warnings': []
            }
            
            # Generate insights based on correlations
            for sector1, sector_data in correlations.items():
                for sector2, corr_data in sector_data.items():
                    correlation = corr_data['correlation']
                    strength = corr_data['strength']
                    
                    if strength in ['Strong', 'Very Strong']:
                        if correlation > 0:
                            insights['key_insights'].append(
                                f"{sector1.title()} and {sector2.title()} are strongly positively correlated ({correlation:.2f})"
                            )
                        else:
                            insights['key_insights'].append(
                                f"{sector1.title()} and {sector2.title()} are strongly negatively correlated ({correlation:.2f})"
                            )
            
            # Generate trading opportunities
            if regime['regime_type'] == 'Risk-On/Growth':
                insights['trading_opportunities'].extend([
                    'Consider increasing crypto allocation',
                    'Growth stocks may outperform',
                    'Technology sector looks favorable'
                ])
            elif regime['regime_type'] == 'Risk-Off/Defensive':
                insights['trading_opportunities'].extend([
                    'Consider increasing gold allocation',
                    'Defensive stocks may outperform',
                    'Bonds could provide stability'
                ])
            
            # Generate risk warnings
            vix_level = regime.get('indicators', {}).get('vix', {}).get('current', 20)
            if vix_level > 25:
                insights['risk_warnings'].append('Elevated market volatility detected')
            
            return insights
            
        except Exception as e:
            logging.error(f"Error generating insights: {e}")
            return {'error': str(e)}
    
    def get_sector_recommendations(self, sector):
        """Get specific recommendations for a sector"""
        try:
            insights = self.generate_cross_sector_insights()
            regime = insights.get('market_regime', {})
            correlations = insights.get('sector_correlations', {})
            
            recommendations = {
                'sector': sector,
                'timestamp': datetime.now().isoformat(),
                'regime_impact': 'Neutral',
                'correlated_sectors': [],
                'recommendations': [],
                'risk_level': 'Medium'
            }
            
            # Analyze regime impact on sector
            regime_type = regime.get('regime_type', 'Unknown')
            
            if sector == 'crypto':
                if regime_type == 'Risk-On/Growth':
                    recommendations['regime_impact'] = 'Positive'
                    recommendations['recommendations'].append('Favorable environment for crypto')
                elif regime_type == 'Risk-Off/Defensive':
                    recommendations['regime_impact'] = 'Negative'
                    recommendations['recommendations'].append('Challenging environment for crypto')
            
            elif sector == 'gold':
                if regime_type == 'Risk-Off/Defensive':
                    recommendations['regime_impact'] = 'Positive'
                    recommendations['recommendations'].append('Safe haven demand supports gold')
                elif regime_type == 'Risk-On/Growth':
                    recommendations['regime_impact'] = 'Negative'
                    recommendations['recommendations'].append('Risk-on environment challenges gold')
            
            # Find correlated sectors
            if sector in correlations:
                for other_sector, corr_data in correlations[sector].items():
                    if corr_data['strength'] in ['Strong', 'Very Strong']:
                        recommendations['correlated_sectors'].append({
                            'sector': other_sector,
                            'correlation': corr_data['correlation'],
                            'strength': corr_data['strength']
                        })
            
            return recommendations
            
        except Exception as e:
            logging.error(f"Error getting sector recommendations: {e}")
            return {'error': str(e)}

# Global instance
cross_app_intelligence = CrossAppIntelligenceEngine()

def get_cross_sector_insights():
    """Get cross-sector insights"""
    return cross_app_intelligence.generate_cross_sector_insights()

def get_sector_recommendations(sector):
    """Get recommendations for a specific sector"""
    return cross_app_intelligence.get_sector_recommendations(sector)

def get_market_regime():
    """Get current market regime analysis"""
    return cross_app_intelligence.analyze_market_regime()

if __name__ == "__main__":
    print("🚀 Testing Cross-App Intelligence System...")
    
    # Test market regime analysis
    regime = cross_app_intelligence.analyze_market_regime()
    print(f"📊 Market Regime: {regime.get('regime_type', 'Unknown')}")
    
    # Test cross-sector insights
    insights = cross_app_intelligence.generate_cross_sector_insights()
    print(f"💡 Generated {len(insights.get('key_insights', []))} insights")
    
    print("✅ Cross-app intelligence test completed!")
