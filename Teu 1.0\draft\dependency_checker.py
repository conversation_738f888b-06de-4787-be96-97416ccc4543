#!/usr/bin/env python3
# TiT Suite Dependency Checker
# Checks all files and dependencies for the complete TiT Suite

import os
import sys
import subprocess
import importlib.util

def check_file_exists(filename):
    """Check if a file exists and return status"""
    exists = os.path.exists(filename)
    size = os.path.getsize(filename) if exists else 0
    return exists, size

def check_python_module(module_name):
    """Check if a Python module is available"""
    try:
        spec = importlib.util.find_spec(module_name)
        return spec is not None
    except ImportError:
        return False

def main():
    print("🔍 TiT Suite Dependency Checker")
    print("=" * 50)
    
    # Check all TiT app files
    app_files = [
        "Teu 1.0.1.py",  # Crypto app
        "TiT_Stock_App_1.0.1.py",
        "TiT_Oil_App_1.0.1.py", 
        "TiT_Gold_App_1.0.1.py",
        "TiT_Health_App_1.0.1.py",
        "TiT_Defense_App_1.0.1.py",
        "TiT_Science_App_1.0.1.py",
        "TiT_Launcher_1.0.1.py",
        "EMERGENCY_LAUNCHER.py"
    ]
    
    print("\n📁 FILE EXISTENCE CHECK:")
    all_files_exist = True
    for filename in app_files:
        exists, size = check_file_exists(filename)
        status = "✅" if exists else "❌"
        size_str = f"({size:,} bytes)" if exists else "(missing)"
        print(f"{status} {filename} {size_str}")
        if not exists:
            all_files_exist = False
    
    # Check Python modules (using import names)
    required_modules = [
        "tkinter",
        "requests",
        "pandas",
        "matplotlib",
        "numpy",
        "yfinance",
        "bs4",  # beautifulsoup4 imports as bs4
        "lxml",
        "PIL"   # Pillow imports as PIL
    ]
    
    optional_modules = [
        "ttkthemes",
        "plotly",
        "seaborn"
    ]
    
    print("\n🐍 REQUIRED PYTHON MODULES:")
    all_required_available = True
    for module in required_modules:
        available = check_python_module(module)
        status = "✅" if available else "❌"
        print(f"{status} {module}")
        if not available:
            all_required_available = False
    
    print("\n🎨 OPTIONAL PYTHON MODULES:")
    for module in optional_modules:
        available = check_python_module(module)
        status = "✅" if available else "⚠️"
        print(f"{status} {module}")
    
    # Check Python version
    print(f"\n🐍 PYTHON VERSION: {sys.version}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    
    if all_files_exist:
        print("✅ All TiT app files are present")
    else:
        print("❌ Some TiT app files are missing")
    
    if all_required_available:
        print("✅ All required Python modules are available")
    else:
        print("❌ Some required Python modules are missing")
        print("\n💡 To install missing modules, run:")
        print("pip install requests pandas matplotlib numpy yfinance beautifulsoup4 lxml Pillow")
    
    # Overall status
    if all_files_exist and all_required_available:
        print("\n🎉 ALL DEPENDENCIES SATISFIED! TiT Suite is ready to run!")
        return True
    else:
        print("\n⚠️ DEPENDENCIES NEED ATTENTION!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
