#!/usr/bin/env python3
# Enhanced News Integration Module for TiT Suite
# Provides ultra-fast news loading and intelligent categorization

import requests
import feedparser
import time
import json
import re
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class EnhancedNewsIntegration:
    """Enhanced news integration with ultra-fast loading and smart categorization"""
    
    def __init__(self):
        self.news_cache = {}
        self.cache_duration = 300  # 5 minutes
        self.executor = ThreadPoolExecutor(max_workers=15)
        
        # Enhanced RSS feeds for all sectors
        self.sector_feeds = {
            'crypto': [
                'https://cointelegraph.com/rss',
                'https://decrypt.co/feed',
                'https://www.coindesk.com/arc/outboundfeeds/rss/',
                'https://bitcoinmagazine.com/.rss/full/',
                'https://cryptonews.com/news/feed/'
            ],
            'stock': [
                'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/',
                'https://feeds.reuters.com/reuters/businessNews',
                'https://www.cnbc.com/id/100003114/device/rss/rss.html',
                'https://feeds.bloomberg.com/markets/news.rss',
                'https://finance.yahoo.com/news/rssindex'
            ],
            'oil': [
                'https://oilprice.com/rss/main',
                'https://www.rigzone.com/rss/news.asp',
                'https://www.offshore-mag.com/rss.xml',
                'https://www.worldoil.com/rss.xml'
            ],
            'gold': [
                'https://www.kitco.com/rss/KitcoNews.xml',
                'https://www.mining.com/rss/',
                'https://www.goldseek.com/rss.php',
                'https://www.mineweb.com/feed/'
            ],
            'health': [
                'https://www.biospace.com/rss',
                'https://www.fiercebiotech.com/rss/xml',
                'https://www.biopharmadive.com/feeds/news/',
                'https://www.statnews.com/feed/'
            ],
            'defense': [
                'https://www.defensenews.com/arc/outboundfeeds/rss/',
                'https://www.janes.com/feeds/news.xml',
                'https://breakingdefense.com/feed/',
                'https://www.military.com/rss/news'
            ],
            'science': [
                'https://www.space.com/feeds/all',
                'https://www.sciencedaily.com/rss/space_time/space_exploration.xml',
                'https://www.nasa.gov/rss/dyn/breaking_news.rss',
                'https://techcrunch.com/feed/'
            ]
        }
        
        # Enhanced keywords for better categorization
        self.sector_keywords = {
            'crypto': ['bitcoin', 'ethereum', 'cryptocurrency', 'blockchain', 'defi', 'nft', 'crypto', 'digital asset', 'altcoin', 'stablecoin', 'mining', 'wallet', 'exchange'],
            'stock': ['stock', 'equity', 'shares', 'nasdaq', 'dow jones', 's&p 500', 'market index', 'ipo', 'dividend', 'earnings', 'quarterly', 'revenue'],
            'oil': ['oil', 'crude', 'petroleum', 'opec', 'drilling', 'refinery', 'pipeline', 'energy', 'barrel', 'wti', 'brent'],
            'gold': ['gold', 'silver', 'platinum', 'precious metals', 'mining', 'bullion', 'commodity', 'metal prices'],
            'health': ['biotech', 'pharmaceutical', 'drug', 'clinical trial', 'fda approval', 'medicine', 'healthcare', 'therapy'],
            'defense': ['defense', 'military', 'weapon', 'contract', 'pentagon', 'army', 'navy', 'air force', 'security'],
            'science': ['space', 'nasa', 'rocket', 'satellite', 'technology', 'innovation', 'research', 'development']
        }
    
    def fetch_single_feed(self, url, sector):
        """Fetch a single RSS feed with error handling"""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            feed = feedparser.parse(response.content)
            articles = []
            
            for entry in feed.entries[:10]:  # Limit to 10 articles per feed
                try:
                    # Extract article data
                    article = {
                        'title': entry.get('title', 'No Title'),
                        'link': entry.get('link', ''),
                        'description': entry.get('summary', entry.get('description', '')),
                        'published': entry.get('published', ''),
                        'source': url,
                        'sector': sector,
                        'timestamp': time.time()
                    }
                    
                    # Clean description
                    if article['description']:
                        soup = BeautifulSoup(article['description'], 'html.parser')
                        article['description'] = soup.get_text()[:200] + '...'
                    
                    # Calculate relevance score
                    article['relevance'] = self.calculate_relevance(article, sector)
                    
                    articles.append(article)
                    
                except Exception as e:
                    continue
            
            return articles
            
        except Exception as e:
            print(f"Error fetching {url}: {e}")
            return []
    
    def calculate_relevance(self, article, sector):
        """Calculate relevance score based on keywords"""
        text = f"{article['title']} {article['description']}".lower()
        keywords = self.sector_keywords.get(sector, [])
        
        score = 0
        for keyword in keywords:
            if keyword in text:
                score += 1
        
        return score
    
    def fetch_sector_news(self, sector, max_articles=20):
        """Fetch news for a specific sector"""
        cache_key = f"news_{sector}"
        
        # Check cache
        if cache_key in self.news_cache:
            cached_data, timestamp = self.news_cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return cached_data
        
        feeds = self.sector_feeds.get(sector, [])
        if not feeds:
            return []
        
        # Fetch all feeds concurrently
        futures = []
        for feed_url in feeds:
            future = self.executor.submit(self.fetch_single_feed, feed_url, sector)
            futures.append(future)
        
        all_articles = []
        for future in as_completed(futures, timeout=30):
            try:
                articles = future.result()
                all_articles.extend(articles)
            except Exception as e:
                continue
        
        # Sort by relevance and timestamp
        all_articles.sort(key=lambda x: (x['relevance'], x['timestamp']), reverse=True)
        
        # Limit results
        result = all_articles[:max_articles]
        
        # Cache results
        self.news_cache[cache_key] = (result, time.time())
        
        return result
    
    def fetch_all_sectors_news(self):
        """Fetch news for all sectors"""
        all_news = {}
        
        futures = {}
        for sector in self.sector_feeds.keys():
            future = self.executor.submit(self.fetch_sector_news, sector, 15)
            futures[sector] = future
        
        for sector, future in futures.items():
            try:
                all_news[sector] = future.result(timeout=30)
            except Exception as e:
                all_news[sector] = []
                print(f"Error fetching news for {sector}: {e}")
        
        return all_news
    
    def get_breaking_news(self):
        """Get breaking news across all sectors"""
        breaking_keywords = ['breaking', 'urgent', 'alert', 'major', 'significant']
        
        all_news = self.fetch_all_sectors_news()
        breaking_news = []
        
        for sector, articles in all_news.items():
            for article in articles:
                title_lower = article['title'].lower()
                if any(keyword in title_lower for keyword in breaking_keywords):
                    article['breaking'] = True
                    breaking_news.append(article)
        
        # Sort by timestamp
        breaking_news.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return breaking_news[:10]  # Top 10 breaking news
    
    def get_trending_topics(self):
        """Extract trending topics from news"""
        all_news = self.fetch_all_sectors_news()
        word_count = {}
        
        for sector, articles in all_news.items():
            for article in articles:
                words = re.findall(r'\b[A-Za-z]{4,}\b', article['title'].lower())
                for word in words:
                    if word not in ['news', 'says', 'will', 'with', 'from', 'this', 'that']:
                        word_count[word] = word_count.get(word, 0) + 1
        
        # Get top trending words
        trending = sorted(word_count.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return [{'topic': word, 'mentions': count} for word, count in trending]

# Global instance
enhanced_news = EnhancedNewsIntegration()

def get_sector_news(sector):
    """Get news for a specific sector"""
    return enhanced_news.fetch_sector_news(sector)

def get_all_news():
    """Get news for all sectors"""
    return enhanced_news.fetch_all_sectors_news()

def get_breaking_news():
    """Get breaking news"""
    return enhanced_news.get_breaking_news()

if __name__ == "__main__":
    print("🚀 Testing Enhanced News Integration...")
    
    # Test crypto news
    start_time = time.time()
    crypto_news = enhanced_news.fetch_sector_news('crypto', 10)
    end_time = time.time()
    
    print(f"⚡ Fetched {len(crypto_news)} crypto articles in {end_time - start_time:.2f} seconds")
    
    if crypto_news:
        print(f"📰 Latest: {crypto_news[0]['title']}")
    
    print("✅ Enhanced news integration test completed!")
