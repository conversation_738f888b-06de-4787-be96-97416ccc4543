# TiT Suite 1.0.1 - Installation Script
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON>

import subprocess
import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
from ttkthemes import ThemedTk

class TiTInstaller:
    """Professional installer for TiT Suite 1.0.1"""
    
    def __init__(self):
        self.root = ThemedTk(theme='arc')
        self.root.title("TiT Suite 1.0.1 - Professional Installer")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # Center window
        self.center_window()
        
        # Setup UI
        self.setup_ui()
        
    def center_window(self):
        """Center the installer window."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_ui(self):
        """Setup the installer UI."""
        # Header
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill=tk.X, padx=20, pady=20)
        
        title_label = ttk.Label(
            header_frame,
            text="🚀 TiT Suite 1.0.1 Professional Installer",
            font=('Segoe UI', 16, 'bold')
        )
        title_label.pack()
        
        subtitle_label = ttk.Label(
            header_frame,
            text="Advanced Financial Intelligence Platform",
            font=('Segoe UI', 12)
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Author info
        author_label = ttk.Label(
            header_frame,
            text="Created by Anh Quang (Nguyen Le Vinh Quang)",
            font=('Segoe UI', 10, 'italic')
        )
        author_label.pack(pady=(10, 0))
        
        # Installation info
        info_frame = ttk.LabelFrame(self.root, text="Installation Information")
        info_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        info_text = """
🎯 What will be installed:
• All required Python packages
• TiT Suite dependencies
• Performance optimizations
• Quality assurance tools

📊 Applications included:
• TiT Crypto App (Advanced Cryptocurrency Intelligence)
• TiT Stock App (Global Stock Market Intelligence)
• TiT Oil App (Oil Market Intelligence)
• TiT Gold App (Precious Metals Intelligence)
• TiT Health App (Health & Biotech Intelligence)
• TiT Defense App (Geopolitical & Defense Intelligence)
• TiT Science App (Science, Technology & Space Intelligence)
• TiT Launcher (Unified Application Launcher)

⚡ Features:
• Lightning-fast performance
• AI-powered analysis
• Real-time data feeds
• Professional UI/UX
• Comprehensive testing suite
        """
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(padx=10, pady=10)
        
        # Progress section
        progress_frame = ttk.LabelFrame(self.root, text="Installation Progress")
        progress_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        self.progress_var = tk.StringVar(value="Ready to install...")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.pack(pady=5)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=10, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        self.install_btn = ttk.Button(
            button_frame,
            text="🚀 Install TiT Suite",
            command=self.start_installation
        )
        self.install_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_btn = ttk.Button(
            button_frame,
            text="🧪 Test Installation",
            command=self.test_installation,
            state='disabled'
        )
        self.test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.launch_btn = ttk.Button(
            button_frame,
            text="🎮 Launch TiT Suite",
            command=self.launch_suite,
            state='disabled'
        )
        self.launch_btn.pack(side=tk.LEFT)
        
        exit_btn = ttk.Button(
            button_frame,
            text="❌ Exit",
            command=self.root.destroy
        )
        exit_btn.pack(side=tk.RIGHT)
        
    def start_installation(self):
        """Start the installation process."""
        self.install_btn.config(state='disabled')
        self.progress_var.set("Starting installation...")
        self.progress_bar['value'] = 0
        
        import threading
        threading.Thread(target=self.install_worker, daemon=True).start()
        
    def install_worker(self):
        """Installation worker thread."""
        try:
            steps = [
                ("Checking Python version...", self.check_python),
                ("Installing core packages...", self.install_core_packages),
                ("Installing GUI packages...", self.install_gui_packages),
                ("Installing data packages...", self.install_data_packages),
                ("Installing AI packages...", self.install_ai_packages),
                ("Verifying installation...", self.verify_installation),
                ("Installation complete!", self.installation_complete)
            ]
            
            total_steps = len(steps)
            
            for i, (message, func) in enumerate(steps):
                self.root.after(0, lambda m=message: self.progress_var.set(m))
                self.root.after(0, lambda v=(i/total_steps)*100: setattr(self.progress_bar, 'value', v))
                
                if func:
                    success = func()
                    if not success:
                        self.root.after(0, lambda: self.progress_var.set("Installation failed!"))
                        return
                        
                import time
                time.sleep(1)
                
            self.root.after(0, lambda: setattr(self.progress_bar, 'value', 100))
            self.root.after(0, self.enable_post_install_buttons)
            
        except Exception as e:
            self.root.after(0, lambda: self.progress_var.set(f"Error: {e}"))
            
    def check_python(self):
        """Check Python version."""
        version = sys.version_info
        if version.major >= 3 and version.minor >= 7:
            return True
        else:
            messagebox.showerror("Python Version Error", "Python 3.7+ required!")
            return False
            
    def install_core_packages(self):
        """Install core packages."""
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
            return True
        except:
            return False
            
    def install_gui_packages(self):
        """Install GUI packages."""
        packages = ['ttkthemes']
        return self.install_packages(packages)
        
    def install_data_packages(self):
        """Install data packages."""
        packages = ['pandas', 'numpy', 'yfinance', 'requests', 'feedparser', 'beautifulsoup4']
        return self.install_packages(packages)
        
    def install_ai_packages(self):
        """Install AI packages."""
        packages = ['google-generativeai', 'matplotlib']
        return self.install_packages(packages)
        
    def install_packages(self, packages):
        """Install a list of packages."""
        try:
            for package in packages:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            return True
        except:
            return False
            
    def verify_installation(self):
        """Verify installation."""
        try:
            import tkinter
            import ttkthemes
            import pandas
            import yfinance
            import requests
            import feedparser
            import matplotlib
            return True
        except ImportError:
            return False
            
    def installation_complete(self):
        """Installation complete."""
        return True
        
    def enable_post_install_buttons(self):
        """Enable post-installation buttons."""
        self.test_btn.config(state='normal')
        self.launch_btn.config(state='normal')
        self.progress_var.set("✅ Installation completed successfully!")
        
    def test_installation(self):
        """Test the installation."""
        try:
            if os.path.exists('TiT_Test_Suite.py'):
                subprocess.Popen([sys.executable, 'TiT_Test_Suite.py'])
                messagebox.showinfo("Test Suite", "Test suite launched! Check the results.")
            else:
                messagebox.showinfo("Test Suite", "Test suite not found. Installation appears successful.")
        except Exception as e:
            messagebox.showerror("Test Error", f"Error launching test: {e}")
            
    def launch_suite(self):
        """Launch the TiT Suite."""
        try:
            if os.path.exists('TiT_Launcher_1.0.1.py'):
                subprocess.Popen([sys.executable, 'TiT_Launcher_1.0.1.py'])
                messagebox.showinfo("TiT Suite", "TiT Suite launcher started!")
                self.root.destroy()
            else:
                messagebox.showerror("Launch Error", "TiT Launcher not found!")
        except Exception as e:
            messagebox.showerror("Launch Error", f"Error launching TiT Suite: {e}")
            
    def run(self):
        """Run the installer."""
        self.root.mainloop()

if __name__ == "__main__":
    installer = TiTInstaller()
    installer.run()
