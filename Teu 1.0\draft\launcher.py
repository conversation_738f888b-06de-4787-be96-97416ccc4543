#!/usr/bin/env python3
"""
TiT App Launcher - Debug version to help identify GUI issues
"""

import sys
import os
import traceback
import subprocess

def check_dependencies():
    """Check if all required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    required_modules = [
        'tkinter',
        'ttkthemes', 
        'requests',
        'pandas',
        'pycoingecko',
        'tradingview_ta',
        'google.generativeai',
        'yfinance',
        'feedparser',
        'matplotlib'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module} - {e}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ Missing modules: {', '.join(missing_modules)}")
        print("Install them with: pip install " + " ".join(missing_modules))
        return False
    
    print("✅ All dependencies are available!")
    return True

def test_basic_tkinter():
    """Test if basic tkinter works"""
    print("\n🧪 Testing basic Tkinter...")
    
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox
        
        # Create a simple test window
        root = tk.Tk()
        root.title("🧪 TiT App - Tkinter Test")
        root.geometry("400x300")
        
        # Center window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (400 // 2)
        y = (root.winfo_screenheight() // 2) - (300 // 2)
        root.geometry(f"400x300+{x}+{y}")
        
        # Force to front
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(root.attributes, '-topmost', False)
        
        # Add content
        frame = ttk.Frame(root, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="🎉 Tkinter Test Successful!", font=("Arial", 14, "bold")).pack(pady=20)
        ttk.Label(frame, text="If you can see this, GUI is working!", font=("Arial", 10)).pack(pady=10)
        
        def close_test():
            print("✅ Tkinter test completed successfully!")
            root.destroy()
        
        ttk.Button(frame, text="✅ Close Test", command=close_test).pack(pady=20)
        
        print("🖥️ Test window should be visible now...")
        print("💡 If you don't see it, check your taskbar or try Alt+Tab")
        
        # Auto-close after 10 seconds
        root.after(10000, close_test)
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ Tkinter test failed: {e}")
        traceback.print_exc()
        return False

def launch_main_app():
    """Launch the main TiT application"""
    print("\n🚀 Launching TiT App...")
    
    try:
        # Import and run the main app
        print("📥 Importing main application...")
        
        # Add current directory to path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Try to import the main app
        print("🔄 Loading Teu 1.0.1...")
        
        # Run as subprocess to avoid import conflicts
        result = subprocess.run([
            sys.executable, 
            "Teu 1.0.1.py"
        ], capture_output=True, text=True, cwd=current_dir)
        
        if result.returncode == 0:
            print("✅ Application launched successfully!")
            if result.stdout:
                print("📤 Output:", result.stdout)
        else:
            print(f"❌ Application failed with return code: {result.returncode}")
            if result.stderr:
                print("📤 Error output:", result.stderr)
            if result.stdout:
                print("📤 Standard output:", result.stdout)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Failed to launch main app: {e}")
        traceback.print_exc()
        return False

def main():
    """Main launcher function"""
    print("🎯 TiT App Launcher v1.0")
    print("=" * 50)
    
    # Step 1: Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot proceed due to missing dependencies")
        input("Press Enter to exit...")
        return
    
    # Step 2: Test basic GUI
    print("\n" + "=" * 50)
    if not test_basic_tkinter():
        print("\n❌ Basic GUI test failed")
        input("Press Enter to exit...")
        return
    
    # Step 3: Launch main app
    print("\n" + "=" * 50)
    success = launch_main_app()
    
    if success:
        print("\n🎉 TiT App should be running!")
    else:
        print("\n❌ Failed to launch TiT App")
        print("💡 Try running 'python Teu 1.0.1.py' directly")
    
    input("\nPress Enter to exit launcher...")

if __name__ == "__main__":
    main()
