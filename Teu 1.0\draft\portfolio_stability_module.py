#!/usr/bin/env python3
# Portfolio Connection Stability Module for TiT Suite
# Provides robust, error-resistant portfolio management

import json
import os
import time
import logging
import threading
from datetime import datetime, timedelta
import requests
from decimal import Decimal, ROUND_HALF_UP
import hashlib

class StablePortfolioManager:
    """Ultra-stable portfolio manager with error recovery and data validation"""
    
    def __init__(self, portfolio_file="portfolio.json"):
        self.portfolio_file = portfolio_file
        self.portfolio_data = {}
        self.lock = threading.Lock()
        self.backup_interval = 300  # 5 minutes
        self.last_backup = 0
        self.validation_errors = []
        
        # Initialize portfolio
        self.load_portfolio()
        self.start_auto_backup()
        
    def load_portfolio(self):
        """Load portfolio with error recovery"""
        try:
            if os.path.exists(self.portfolio_file):
                with open(self.portfolio_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # Validate portfolio structure
                if self.validate_portfolio_structure(data):
                    self.portfolio_data = data
                    logging.info("✅ Portfolio loaded successfully")
                else:
                    logging.warning("⚠️ Portfolio validation failed, creating new portfolio")
                    self.create_default_portfolio()
            else:
                logging.info("📁 No portfolio file found, creating new portfolio")
                self.create_default_portfolio()
                
        except Exception as e:
            logging.error(f"❌ Error loading portfolio: {e}")
            self.create_default_portfolio()
    
    def validate_portfolio_structure(self, data):
        """Validate portfolio data structure"""
        self.validation_errors = []
        
        try:
            # Check required fields
            required_fields = ['holdings', 'metadata', 'transactions']
            for field in required_fields:
                if field not in data:
                    self.validation_errors.append(f"Missing required field: {field}")
            
            # Validate holdings
            if 'holdings' in data:
                for asset_id, holding in data['holdings'].items():
                    required_holding_fields = ['symbol', 'amount', 'avg_price', 'last_updated']
                    for field in required_holding_fields:
                        if field not in holding:
                            self.validation_errors.append(f"Missing field {field} in holding {asset_id}")
                    
                    # Validate numeric fields
                    try:
                        float(holding.get('amount', 0))
                        float(holding.get('avg_price', 0))
                    except (ValueError, TypeError):
                        self.validation_errors.append(f"Invalid numeric data in holding {asset_id}")
            
            # Validate metadata
            if 'metadata' in data:
                metadata = data['metadata']
                if 'created_at' not in metadata:
                    self.validation_errors.append("Missing created_at in metadata")
            
            return len(self.validation_errors) == 0
            
        except Exception as e:
            self.validation_errors.append(f"Validation error: {e}")
            return False
    
    def create_default_portfolio(self):
        """Create a default portfolio structure"""
        self.portfolio_data = {
            'holdings': {},
            'transactions': [],
            'metadata': {
                'created_at': datetime.now().isoformat(),
                'last_updated': datetime.now().isoformat(),
                'version': '1.0.1',
                'total_value_usd': 0.0,
                'total_invested': 0.0,
                'total_pnl': 0.0
            },
            'settings': {
                'default_currency': 'USD',
                'auto_sync': True,
                'backup_enabled': True
            }
        }
        self.save_portfolio()
        logging.info("✅ Default portfolio created")
    
    def save_portfolio(self):
        """Save portfolio with atomic write and backup"""
        try:
            with self.lock:
                # Update metadata
                self.portfolio_data['metadata']['last_updated'] = datetime.now().isoformat()
                
                # Create backup before saving
                self.create_backup()
                
                # Atomic write using temporary file
                temp_file = f"{self.portfolio_file}.tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(self.portfolio_data, f, indent=2, ensure_ascii=False)
                
                # Replace original file
                if os.path.exists(self.portfolio_file):
                    os.replace(temp_file, self.portfolio_file)
                else:
                    os.rename(temp_file, self.portfolio_file)
                
                logging.info("✅ Portfolio saved successfully")
                return True
                
        except Exception as e:
            logging.error(f"❌ Error saving portfolio: {e}")
            return False
    
    def create_backup(self):
        """Create timestamped backup"""
        try:
            if time.time() - self.last_backup < self.backup_interval:
                return  # Skip if backup was recent
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"portfolio_backup_{timestamp}.json"
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(self.portfolio_data, f, indent=2, ensure_ascii=False)
            
            self.last_backup = time.time()
            logging.info(f"📁 Portfolio backup created: {backup_file}")
            
            # Clean old backups (keep last 10)
            self.cleanup_old_backups()
            
        except Exception as e:
            logging.warning(f"⚠️ Backup creation failed: {e}")
    
    def cleanup_old_backups(self):
        """Remove old backup files"""
        try:
            backup_files = [f for f in os.listdir('.') if f.startswith('portfolio_backup_') and f.endswith('.json')]
            backup_files.sort(reverse=True)  # Newest first
            
            # Remove files beyond the 10 most recent
            for old_backup in backup_files[10:]:
                try:
                    os.remove(old_backup)
                    logging.info(f"🗑️ Removed old backup: {old_backup}")
                except:
                    pass
                    
        except Exception as e:
            logging.warning(f"⚠️ Backup cleanup failed: {e}")
    
    def add_holding(self, symbol, amount, price, transaction_type="buy"):
        """Add or update a holding with validation"""
        try:
            with self.lock:
                # Validate inputs
                if not symbol or not isinstance(symbol, str):
                    raise ValueError("Invalid symbol")
                
                amount = Decimal(str(amount))
                price = Decimal(str(price))
                
                if amount <= 0 or price <= 0:
                    raise ValueError("Amount and price must be positive")
                
                # Update or create holding
                if symbol in self.portfolio_data['holdings']:
                    holding = self.portfolio_data['holdings'][symbol]
                    current_amount = Decimal(str(holding['amount']))
                    current_avg_price = Decimal(str(holding['avg_price']))
                    
                    if transaction_type == "buy":
                        new_amount = current_amount + amount
                        new_avg_price = ((current_amount * current_avg_price) + (amount * price)) / new_amount
                    else:  # sell
                        new_amount = current_amount - amount
                        new_avg_price = current_avg_price  # Keep same avg price
                        
                        if new_amount < 0:
                            raise ValueError("Cannot sell more than owned")
                    
                    holding['amount'] = float(new_amount)
                    holding['avg_price'] = float(new_avg_price)
                else:
                    if transaction_type == "sell":
                        raise ValueError("Cannot sell asset not in portfolio")
                    
                    self.portfolio_data['holdings'][symbol] = {
                        'symbol': symbol,
                        'amount': float(amount),
                        'avg_price': float(price),
                        'last_updated': datetime.now().isoformat()
                    }
                
                # Add transaction record
                transaction = {
                    'id': hashlib.md5(f"{symbol}{amount}{price}{time.time()}".encode()).hexdigest()[:8],
                    'symbol': symbol,
                    'type': transaction_type,
                    'amount': float(amount),
                    'price': float(price),
                    'timestamp': datetime.now().isoformat(),
                    'total_value': float(amount * price)
                }
                
                self.portfolio_data['transactions'].append(transaction)
                
                # Save changes
                self.save_portfolio()
                
                logging.info(f"✅ {transaction_type.title()} transaction added: {amount} {symbol} @ ${price}")
                return True
                
        except Exception as e:
            logging.error(f"❌ Error adding holding: {e}")
            return False
    
    def get_portfolio_summary(self):
        """Get portfolio summary with error handling"""
        try:
            with self.lock:
                holdings = self.portfolio_data.get('holdings', {})
                
                summary = {
                    'total_holdings': len(holdings),
                    'holdings': holdings,
                    'last_updated': self.portfolio_data['metadata']['last_updated'],
                    'validation_status': 'valid' if len(self.validation_errors) == 0 else 'errors',
                    'validation_errors': self.validation_errors
                }
                
                return summary
                
        except Exception as e:
            logging.error(f"❌ Error getting portfolio summary: {e}")
            return {'error': str(e)}
    
    def start_auto_backup(self):
        """Start automatic backup thread"""
        def backup_worker():
            while True:
                time.sleep(self.backup_interval)
                if self.portfolio_data:
                    self.create_backup()
        
        backup_thread = threading.Thread(target=backup_worker, daemon=True)
        backup_thread.start()
        logging.info("🔄 Auto-backup thread started")

# Global instance
stable_portfolio = StablePortfolioManager()

def get_portfolio():
    """Get portfolio summary"""
    return stable_portfolio.get_portfolio_summary()

def add_transaction(symbol, amount, price, transaction_type="buy"):
    """Add a transaction"""
    return stable_portfolio.add_holding(symbol, amount, price, transaction_type)

if __name__ == "__main__":
    print("🚀 Testing Portfolio Stability Module...")
    
    # Test portfolio operations
    portfolio = StablePortfolioManager("test_portfolio.json")
    
    # Add test holdings
    portfolio.add_holding("BTC", 0.5, 45000, "buy")
    portfolio.add_holding("ETH", 2.0, 3000, "buy")
    
    # Get summary
    summary = portfolio.get_portfolio_summary()
    print(f"✅ Portfolio has {summary['total_holdings']} holdings")
    
    print("✅ Portfolio stability test completed!")
