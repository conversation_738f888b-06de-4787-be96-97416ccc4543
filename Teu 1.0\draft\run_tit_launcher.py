#!/usr/bin/env python3
"""
Quick script to run the TiT launcher
"""
import subprocess
import sys
import os

def run_tit_launcher():
    """Run the TiT launcher"""
    try:
        # Change to the correct directory
        os.chdir(r"c:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0")
        
        # Run the beautiful launcher
        subprocess.run([sys.executable, "BEAUTIFUL_WHITE_LAUNCHER.py"])
        
    except Exception as e:
        print(f"Error running TiT launcher: {e}")

if __name__ == "__main__":
    run_tit_launcher()
