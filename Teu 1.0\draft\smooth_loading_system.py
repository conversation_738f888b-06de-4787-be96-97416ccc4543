#!/usr/bin/env python3
# Smooth Loading System for TiT Suite
# Provides beautiful, smooth loading screens with progress bars

import tkinter as tk
from tkinter import ttk
import threading
import time
import math
import random

class SmoothLoadingScreen:
    """Ultra-smooth loading screen with animated progress bar"""
    
    def __init__(self, parent=None, title="Loading...", message="Please wait"):
        self.parent = parent
        self.title = title
        self.message = message
        self.progress_value = 0
        self.is_loading = False
        self.loading_window = None
        self.progress_bar = None
        self.status_label = None
        self.percentage_label = None
        
        # Animation variables
        self.animation_step = 0
        self.gradient_colors = ['#1e3c72', '#2a5298', '#3b82f6', '#60a5fa', '#93c5fd']
        
    def create_loading_window(self):
        """Create the loading window with smooth animations"""
        self.loading_window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.loading_window.title(self.title)
        self.loading_window.geometry("500x300")
        self.loading_window.configure(bg='#1a1a2e')
        self.loading_window.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Remove window decorations for modern look
        self.loading_window.overrideredirect(True)
        
        # Create main frame with gradient effect
        main_frame = tk.Frame(self.loading_window, bg='#1a1a2e', padx=40, pady=40)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title label with glow effect
        title_label = tk.Label(
            main_frame,
            text="🚀 TiT SUITE LOADING 🚀",
            font=("Arial", 20, "bold"),
            fg='#ffffff',
            bg='#1a1a2e'
        )
        title_label.pack(pady=(0, 20))
        
        # Status message
        self.status_label = tk.Label(
            main_frame,
            text=self.message,
            font=("Arial", 12),
            fg='#a0a0a0',
            bg='#1a1a2e'
        )
        self.status_label.pack(pady=(0, 30))
        
        # Progress bar frame
        progress_frame = tk.Frame(main_frame, bg='#1a1a2e')
        progress_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Custom progress bar with smooth animation
        self.create_custom_progress_bar(progress_frame)
        
        # Percentage label
        self.percentage_label = tk.Label(
            main_frame,
            text="0%",
            font=("Arial", 16, "bold"),
            fg='#60a5fa',
            bg='#1a1a2e'
        )
        self.percentage_label.pack()
        
        # Loading dots animation
        self.dots_label = tk.Label(
            main_frame,
            text="",
            font=("Arial", 14),
            fg='#93c5fd',
            bg='#1a1a2e'
        )
        self.dots_label.pack(pady=(10, 0))
        
        # Start animations
        self.start_animations()
        
    def create_custom_progress_bar(self, parent):
        """Create a custom animated progress bar"""
        # Progress bar background
        self.progress_bg = tk.Canvas(
            parent,
            width=400,
            height=20,
            bg='#1a1a2e',
            highlightthickness=0
        )
        self.progress_bg.pack()
        
        # Draw background
        self.progress_bg.create_rectangle(
            0, 0, 400, 20,
            fill='#2d2d44',
            outline='#3d3d5c',
            width=2
        )
        
        # Progress fill (will be animated)
        self.progress_fill = self.progress_bg.create_rectangle(
            0, 0, 0, 20,
            fill='#60a5fa',
            outline=''
        )
        
    def center_window(self):
        """Center the loading window on screen"""
        self.loading_window.update_idletasks()
        width = 500
        height = 300
        x = (self.loading_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.loading_window.winfo_screenheight() // 2) - (height // 2)
        self.loading_window.geometry(f'{width}x{height}+{x}+{y}')
        
    def start_animations(self):
        """Start all loading animations"""
        self.animate_dots()
        self.animate_progress_color()
        
    def animate_dots(self):
        """Animate loading dots"""
        if not self.is_loading:
            return
            
        dots_count = (self.animation_step // 10) % 4
        dots = "." * dots_count
        self.dots_label.config(text=f"Loading{dots}")
        
        self.animation_step += 1
        self.loading_window.after(100, self.animate_dots)
        
    def animate_progress_color(self):
        """Animate progress bar color gradient"""
        if not self.is_loading:
            return
            
        # Cycle through gradient colors
        color_index = (self.animation_step // 5) % len(self.gradient_colors)
        color = self.gradient_colors[color_index]
        
        try:
            self.progress_bg.itemconfig(self.progress_fill, fill=color)
        except:
            pass
            
        self.loading_window.after(50, self.animate_progress_color)
        
    def update_progress(self, value, status_text=None):
        """Update progress bar smoothly"""
        if not self.loading_window:
            return
            
        # Smooth progress animation
        target_value = max(0, min(100, value))
        
        def smooth_update():
            current = self.progress_value
            if current < target_value:
                self.progress_value = min(current + 1, target_value)
                
                # Update progress bar width
                progress_width = (self.progress_value / 100) * 400
                try:
                    self.progress_bg.coords(self.progress_fill, 0, 0, progress_width, 20)
                    self.percentage_label.config(text=f"{self.progress_value}%")
                except:
                    pass
                
                if self.progress_value < target_value:
                    self.loading_window.after(20, smooth_update)
            
        smooth_update()
        
        # Update status text
        if status_text and self.status_label:
            self.status_label.config(text=status_text)
            
        # Update window
        try:
            self.loading_window.update()
        except:
            pass
    
    def show(self):
        """Show the loading screen"""
        self.is_loading = True
        self.create_loading_window()
        
    def hide(self):
        """Hide the loading screen"""
        self.is_loading = False
        if self.loading_window:
            try:
                self.loading_window.destroy()
            except:
                pass
            self.loading_window = None

class DataLoadingManager:
    """Manages smooth loading for data operations"""
    
    def __init__(self):
        self.loading_screen = None
        
    def load_with_progress(self, load_function, steps=None, parent=None):
        """Load data with smooth progress indication"""
        if not steps:
            steps = [
                (10, "Initializing connection..."),
                (25, "Fetching market data..."),
                (50, "Processing information..."),
                (75, "Analyzing trends..."),
                (90, "Finalizing results..."),
                (100, "Complete!")
            ]
        
        # Show loading screen
        self.loading_screen = SmoothLoadingScreen(parent, "Loading Data", "Preparing to load...")
        self.loading_screen.show()
        
        def loading_worker():
            try:
                for progress, status in steps:
                    self.loading_screen.update_progress(progress, status)
                    time.sleep(0.1)  # Smooth animation
                
                # Execute the actual loading function
                result = load_function()
                
                # Complete
                self.loading_screen.update_progress(100, "Loading complete!")
                time.sleep(0.5)
                
                # Hide loading screen
                self.loading_screen.hide()
                
                return result
                
            except Exception as e:
                if self.loading_screen:
                    self.loading_screen.update_progress(100, f"Error: {str(e)}")
                    time.sleep(2)
                    self.loading_screen.hide()
                raise e
        
        # Run in thread to keep UI responsive
        thread = threading.Thread(target=loading_worker, daemon=True)
        thread.start()
        
        return thread

# Integration functions for TiT Suite apps
def show_section_loading(parent, section_name):
    """Show loading for app sections"""
    loading = SmoothLoadingScreen(parent, f"Loading {section_name}", f"Loading {section_name} data...")
    loading.show()
    
    # Simulate section loading
    steps = [
        (20, f"Connecting to {section_name} data source..."),
        (40, f"Downloading {section_name} information..."),
        (60, f"Processing {section_name} data..."),
        (80, f"Updating {section_name} display..."),
        (100, f"{section_name} loaded successfully!")
    ]
    
    def load_section():
        for progress, status in steps:
            loading.update_progress(progress, status)
            time.sleep(0.1)
        
        time.sleep(0.3)
        loading.hide()
    
    thread = threading.Thread(target=load_section, daemon=True)
    thread.start()
    
    return loading

def show_app_startup_loading():
    """Show loading for app startup"""
    loading = SmoothLoadingScreen(None, "TiT Suite Starting", "Initializing application...")
    loading.show()
    
    startup_steps = [
        (10, "Loading configuration..."),
        (20, "Initializing modules..."),
        (35, "Connecting to data sources..."),
        (50, "Loading market data..."),
        (65, "Setting up user interface..."),
        (80, "Finalizing startup..."),
        (95, "Ready to launch!"),
        (100, "Welcome to TiT Suite!")
    ]
    
    def startup_sequence():
        for progress, status in startup_steps:
            loading.update_progress(progress, status)
            time.sleep(0.15)
        
        time.sleep(0.5)
        loading.hide()
    
    thread = threading.Thread(target=startup_sequence, daemon=True)
    thread.start()
    
    return loading

if __name__ == "__main__":
    # Test the loading system
    print("🚀 Testing Smooth Loading System...")
    
    # Test startup loading
    startup_loading = show_app_startup_loading()
    
    # Keep the test running
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    def test_section_loading():
        time.sleep(3)  # Wait for startup to finish
        show_section_loading(None, "News")
    
    threading.Thread(target=test_section_loading, daemon=True).start()
    
    root.mainloop()
