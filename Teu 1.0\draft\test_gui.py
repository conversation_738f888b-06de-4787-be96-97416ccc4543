#!/usr/bin/env python3
"""
Simple GUI test to check if <PERSON><PERSON><PERSON> is working properly
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys

def test_basic_gui():
    """Test basic GUI functionality"""
    print("🚀 Testing basic GUI...")
    
    try:
        # Create root window
        root = tk.Tk()
        root.title("🧪 GUI Test - TiT App")
        root.geometry("800x600")
        
        # Make sure window appears on top and is visible
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(root.attributes, '-topmost', False)
        
        # Center the window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (800 // 2)
        y = (root.winfo_screenheight() // 2) - (600 // 2)
        root.geometry(f"800x600+{x}+{y}")
        
        # Create main frame
        main_frame = ttk.Frame(root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text="🎉 TiT App GUI Test", 
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=20)
        
        # Status
        status_label = ttk.Label(
            main_frame, 
            text="✅ GUI is working correctly!", 
            font=("Arial", 12)
        )
        status_label.pack(pady=10)
        
        # Test notebook (tabs)
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=20)
        
        # Tab 1
        tab1 = ttk.Frame(notebook)
        notebook.add(tab1, text="Dashboard")
        ttk.Label(tab1, text="📊 Dashboard Tab", font=("Arial", 14)).pack(pady=50)
        
        # Tab 2
        tab2 = ttk.Frame(notebook)
        notebook.add(tab2, text="Portfolio")
        ttk.Label(tab2, text="💼 Portfolio Tab", font=("Arial", 14)).pack(pady=50)
        
        # Tab 3
        tab3 = ttk.Frame(notebook)
        notebook.add(tab3, text="Charts")
        ttk.Label(tab3, text="📈 Charts Tab", font=("Arial", 14)).pack(pady=50)
        
        # Tab 4
        tab4 = ttk.Frame(notebook)
        notebook.add(tab4, text="News")
        ttk.Label(tab4, text="📰 News Tab", font=("Arial", 14)).pack(pady=50)
        
        # Test button
        def show_success():
            messagebox.showinfo("Success", "🎉 GUI Test Successful!\n\nYour system can run TiT App properly!")
        
        test_button = ttk.Button(
            main_frame,
            text="🧪 Test Button",
            command=show_success
        )
        test_button.pack(pady=20)
        
        # Instructions
        instructions = ttk.Label(
            main_frame,
            text="If you can see this window with tabs, your GUI is working!\nClick the test button to confirm.",
            font=("Arial", 10),
            justify=tk.CENTER
        )
        instructions.pack(pady=10)
        
        print("✅ GUI created successfully")
        print("🖥️ Window should be visible now")
        
        # Start the GUI
        root.mainloop()
        
    except Exception as e:
        print(f"❌ GUI Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 Starting GUI Test...")
    print("📍 Current working directory:", sys.path[0])
    
    success = test_basic_gui()
    
    if success:
        print("✅ GUI test completed successfully")
    else:
        print("❌ GUI test failed")
