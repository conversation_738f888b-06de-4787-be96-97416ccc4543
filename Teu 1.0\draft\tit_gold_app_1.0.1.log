2025-06-17 20:41:20,224 - INFO - [MainThread] - TiT Gold App 1.0.1 Starting...
2025-06-17 20:41:20,225 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-17 20:41:22,553 - INFO - [MainThread] - TiT Gold App main initialization started...
2025-06-17 20:41:22,553 - INFO - [MainThread] - GoldCacheService initialized.
2025-06-17 20:41:22,553 - INFO - [MainThread] - GoldDataService initialized with comprehensive precious metals coverage.
2025-06-17 20:41:22,553 - INFO - [MainThread] - GoldAIService initialized with Gemini Pro.
2025-06-17 20:41:24,250 - INFO - [MainThread] - Gold app UI setup complete
2025-06-17 20:41:24,251 - INFO - [MainThread] - TiT Gold App 1.0.1 initialized successfully.
2025-06-17 20:41:24,798 - INFO - [Thread-1 (refresh_worker)] - Fetching real-time precious metals prices...
2025-06-17 20:41:26,858 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:27,591 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:30,317 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:33,223 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: gold_prices
2025-06-17 20:41:33,223 - INFO - [Thread-1 (refresh_worker)] - Fetching gold miners data for all regions...
2025-06-17 20:41:35,517 - ERROR - [Thread-1 (refresh_worker)] - $AUY: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-17 20:41:36,861 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:37,564 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:42,522 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:44,329 - ERROR - [Thread-1 (refresh_worker)] - $YRI.TO: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-17 20:41:45,460 - ERROR - [Thread-1 (refresh_worker)] - $NCM.AX: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-17 20:41:45,761 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:48,934 - ERROR - [Thread-1 (refresh_worker)] - $SAR.AX: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-17 20:41:50,602 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:51,351 - ERROR - [Thread-1 (refresh_worker)] - $SGL.JO: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-17 20:41:51,740 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:52,353 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:53,282 - ERROR - [Thread-1 (refresh_worker)] - $POLY.ME: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-17 20:41:53,833 - ERROR - [Thread-1 (refresh_worker)] - $PLZL.ME: possibly delisted; no price data found  (period=1d)
2025-06-17 20:41:54,162 - ERROR - [Thread-1 (refresh_worker)] - $GMKN.ME: possibly delisted; no price data found  (period=1d)
2025-06-17 20:41:55,511 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:56,756 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:59,010 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:41:59,955 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: gold_miners
2025-06-17 20:41:59,956 - INFO - [Thread-1 (refresh_worker)] - Fetching gold and precious metals news from The Globe and Mail and financial sources...
2025-06-17 20:41:59,956 - INFO - [Thread-1 (refresh_worker)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/
2025-06-17 20:42:00,575 - INFO - [Thread-1 (refresh_worker)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/
2025-06-17 20:42:01,167 - INFO - [Thread-1 (refresh_worker)] - Parsing financial RSS feed: https://feeds.reuters.com/reuters/businessNews
2025-06-17 20:42:01,328 - INFO - [Thread-1 (refresh_worker)] - Parsing financial RSS feed: https://www.cnbc.com/id/10000664/device/rss/rss.html
2025-06-17 20:42:01,844 - INFO - [Thread-1 (refresh_worker)] - Parsing financial RSS feed: https://feeds.bloomberg.com/markets/news.rss
2025-06-17 20:42:02,317 - INFO - [Thread-1 (refresh_worker)] - Successfully fetched 11 gold market news articles
2025-06-17 20:42:02,318 - INFO - [Thread-1 (refresh_worker)] - The Globe and Mail articles: 10, Other sources: 1
2025-06-17 20:42:02,318 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: gold_news
2025-06-17 20:49:36,056 - INFO - [MainThread] - Gold application closing...
