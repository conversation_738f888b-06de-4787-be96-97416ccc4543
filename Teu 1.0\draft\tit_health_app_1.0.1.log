2025-06-17 20:32:04,622 - INFO - [MainThread] - TiT Health App 1.0.1 Starting...
2025-06-17 20:32:04,623 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-17 20:32:06,620 - INFO - [MainThread] - TiT Health App main initialization started...
2025-06-17 20:32:06,620 - INFO - [MainThread] - HealthCacheService initialized.
2025-06-17 20:32:06,620 - INFO - [MainThread] - HealthDataService initialized with comprehensive healthcare coverage.
2025-06-17 20:32:06,621 - INFO - [MainThread] - HealthAIService initialized with Gemini Pro.
2025-06-17 20:32:07,689 - INFO - [MainThread] - Health app UI setup complete
2025-06-17 20:32:07,690 - INFO - [MainThread] - TiT Health App 1.0.1 initialized successfully.
2025-06-17 20:32:07,816 - INFO - [Thread-1 (refresh_worker)] - Fetching health stocks data for all categories...
2025-06-17 20:32:08,639 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:09,742 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:10,529 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:11,421 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:13,021 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:13,702 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:14,321 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:14,945 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:15,508 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:16,442 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:18,158 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:18,822 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-17 20:32:19,490 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
