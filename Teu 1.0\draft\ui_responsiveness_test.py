#!/usr/bin/env python3
# TiT Suite UI Responsiveness Test
# Tests startup times and UI responsiveness of all apps

import subprocess
import sys
import time
import os
import psutil

def test_app_startup(app_file, app_name, timeout=10):
    """Test app startup time and responsiveness"""
    print(f"\n🚀 Testing {app_name}...")
    
    if not os.path.exists(app_file):
        print(f"❌ {app_file} not found")
        return False, 0
    
    start_time = time.time()
    
    try:
        # Launch the app
        process = subprocess.Popen([sys.executable, app_file])
        
        # Wait a moment for the app to initialize
        time.sleep(2)
        
        # Check if process is still running
        if process.poll() is None:
            startup_time = time.time() - start_time
            print(f"✅ {app_name} started successfully in {startup_time:.2f} seconds")
            print(f"   Process ID: {process.pid}")
            
            # Terminate the test process
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"   ✅ Process terminated cleanly")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"   ⚠️ Process had to be killed")
            
            return True, startup_time
        else:
            print(f"❌ {app_name} failed to start (process exited)")
            return False, 0
            
    except Exception as e:
        print(f"❌ Error testing {app_name}: {e}")
        return False, 0

def main():
    print("🔍 TiT Suite UI Responsiveness Test")
    print("=" * 50)
    
    # Test all apps
    apps_to_test = [
        ("Teu 1.0.1.py", "TiT Crypto App"),
        ("TiT_Stock_App_1.0.1.py", "TiT Stock App"),
        ("TiT_Oil_App_1.0.1.py", "TiT Oil App"),
        ("TiT_Gold_App_1.0.1.py", "TiT Gold App"),
        ("TiT_Health_App_1.0.1.py", "TiT Health App"),
        ("TiT_Defense_App_1.0.1.py", "TiT Defense App"),
        ("TiT_Science_App_1.0.1.py", "TiT Science App"),
        ("EMERGENCY_LAUNCHER.py", "Emergency Launcher")
    ]
    
    successful_tests = 0
    total_startup_time = 0
    startup_times = []
    
    for app_file, app_name in apps_to_test:
        success, startup_time = test_app_startup(app_file, app_name)
        if success:
            successful_tests += 1
            total_startup_time += startup_time
            startup_times.append(startup_time)
    
    # Results summary
    print("\n" + "=" * 50)
    print("📋 RESPONSIVENESS TEST RESULTS:")
    print(f"✅ Successful tests: {successful_tests}/{len(apps_to_test)}")
    
    if startup_times:
        avg_startup = total_startup_time / len(startup_times)
        fastest = min(startup_times)
        slowest = max(startup_times)
        
        print(f"⚡ Average startup time: {avg_startup:.2f} seconds")
        print(f"🏃 Fastest startup: {fastest:.2f} seconds")
        print(f"🐌 Slowest startup: {slowest:.2f} seconds")
        
        # Performance assessment
        if avg_startup < 3:
            print("🎉 EXCELLENT responsiveness! All apps start quickly.")
        elif avg_startup < 5:
            print("✅ GOOD responsiveness! Apps start reasonably fast.")
        elif avg_startup < 8:
            print("⚠️ MODERATE responsiveness. Could be improved.")
        else:
            print("❌ SLOW responsiveness. Optimization needed.")
    
    # Overall assessment
    if successful_tests == len(apps_to_test):
        print("\n🎯 ALL APPS PASSED RESPONSIVENESS TEST!")
        return True
    else:
        print(f"\n⚠️ {len(apps_to_test) - successful_tests} apps failed the test")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
