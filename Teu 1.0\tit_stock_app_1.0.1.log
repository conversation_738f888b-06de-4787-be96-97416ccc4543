2025-06-20 21:56:49,672 - INFO - [MainThread] - TiT Stock App 1.0.1 Application Starting...
2025-06-20 21:56:49,672 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-20 21:56:49,673 - INFO - [MainThread] - Configuration loaded successfully.
2025-06-20 21:56:52,356 - INFO - [MainThread] - TiT Stock App main initialization started...
2025-06-20 21:56:52,357 - INFO - [MainThread] - CacheService initialized.
2025-06-20 21:56:52,357 - INFO - [MainThread] - StockDataService initialized.
2025-06-20 21:56:52,358 - INFO - [MainThread] - StockAIService initialized with Gemini 1.5 Flash and working API key.
2025-06-20 21:56:52,358 - INFO - [MainThread] - Setting up UI components...
2025-06-20 21:56:55,055 - INFO - [MainThread] - UI setup complete.
2025-06-20 21:56:55,056 - INFO - [MainThread] - Ti<PERSON> <PERSON> App initialized successfully.
2025-06-20 21:56:55,501 - INFO - [Thread-1 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-20 21:56:56,688 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-20 21:56:57,395 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-20 21:56:57,991 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-20 21:56:58,608 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-20 21:56:59,383 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-20 21:57:00,094 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-20 21:57:01,326 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-20 21:57:01,775 - ERROR - [Thread-1 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 21:57:01,776 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-20 21:57:02,855 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 404: 
2025-06-20 21:57:03,395 - ERROR - [Thread-1 (_refresh_task)] - $VN30.VI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 21:57:03,397 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^VNI: Empty or insufficient historical data
2025-06-20 21:57:04,223 - ERROR - [Thread-1 (_refresh_task)] - $VN30.VI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 21:57:04,227 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^VN30: Empty or insufficient historical data
2025-06-20 21:57:04,844 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-20 21:57:05,484 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-20 21:57:06,119 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-20 21:57:06,823 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-20 21:57:07,963 - ERROR - [Thread-1 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 21:57:07,964 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-20 21:57:08,634 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-20 21:57:09,233 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-20 21:57:09,832 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-20 21:57:10,501 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-20 21:57:11,133 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-20 21:57:11,779 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-20 21:57:12,422 - ERROR - [Thread-1 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-20 21:57:12,467 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-20 21:57:13,646 - ERROR - [Thread-1 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 21:57:13,647 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-20 21:57:14,797 - ERROR - [Thread-1 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 21:57:14,799 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-20 21:57:15,375 - ERROR - [Thread-1 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-20 21:57:15,376 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-20 21:57:16,589 - ERROR - [Thread-1 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 21:57:16,590 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-20 21:57:16,591 - INFO - [Thread-1 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-20 21:57:16,591 - INFO - [Thread-1 (_refresh_task)] - Caching data for key: indices_data
2025-06-20 21:57:16,592 - ERROR - [Thread-1 (_refresh_task)] - Error refreshing data: 'TiTStockApp' object has no attribute 'update_indices_display'
2025-06-20 23:08:13,841 - INFO - [Thread-2 (_refresh_task)] - Cache expired for key: indices_data
2025-06-20 23:08:13,842 - INFO - [Thread-2 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-20 23:08:14,434 - INFO - [Thread-3 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-20 23:08:14,552 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-20 23:08:15,151 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-20 23:08:15,163 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-20 23:08:15,765 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-20 23:08:15,781 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-20 23:08:16,400 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-20 23:08:16,439 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-20 23:08:16,997 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-20 23:08:17,063 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-20 23:08:17,607 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-20 23:08:17,682 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-20 23:08:18,275 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-20 23:08:18,566 - ERROR - [Thread-2 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:18,568 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-20 23:08:19,024 - ERROR - [Thread-3 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:19,047 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-20 23:08:19,490 - ERROR - [Thread-2 (_refresh_task)] - $VN30.VI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:19,497 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^VNI: Empty or insufficient historical data
2025-06-20 23:08:19,838 - ERROR - [Thread-3 (_refresh_task)] - $VN30.VI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:19,839 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^VNI: Empty or insufficient historical data
2025-06-20 23:08:20,329 - ERROR - [Thread-2 (_refresh_task)] - $VN30.VI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:20,330 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^VN30: Empty or insufficient historical data
2025-06-20 23:08:20,648 - ERROR - [Thread-3 (_refresh_task)] - $VN30.VI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:20,649 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^VN30: Empty or insufficient historical data
2025-06-20 23:08:20,930 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-20 23:08:21,247 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-20 23:08:21,554 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-20 23:08:21,860 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-20 23:08:22,218 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-20 23:08:22,471 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-20 23:08:22,852 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-20 23:08:23,134 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-20 23:08:23,633 - ERROR - [Thread-2 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:23,635 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-20 23:08:23,918 - ERROR - [Thread-3 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:23,919 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-20 23:08:24,227 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-20 23:08:24,592 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-20 23:08:24,811 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-20 23:08:25,247 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-20 23:08:25,403 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-20 23:08:25,884 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-20 23:08:25,994 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-20 23:08:26,538 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-20 23:08:26,574 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-20 23:08:27,184 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-20 23:08:27,233 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-20 23:08:27,773 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-20 23:08:27,817 - ERROR - [Thread-2 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-20 23:08:27,819 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-20 23:08:28,413 - ERROR - [Thread-3 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-20 23:08:28,414 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-20 23:08:28,559 - ERROR - [Thread-2 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:28,560 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-20 23:08:29,327 - ERROR - [Thread-3 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:29,328 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-20 23:08:29,415 - INFO - [Thread-4 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-20 23:08:29,434 - ERROR - [Thread-2 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:29,435 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-20 23:08:30,120 - ERROR - [Thread-2 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-20 23:08:30,121 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-20 23:08:30,178 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-20 23:08:30,219 - ERROR - [Thread-3 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:30,223 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-20 23:08:30,810 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-20 23:08:30,882 - ERROR - [Thread-3 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-20 23:08:30,883 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-20 23:08:30,986 - ERROR - [Thread-2 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:30,987 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-20 23:08:30,988 - INFO - [Thread-2 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-20 23:08:30,988 - INFO - [Thread-2 (_refresh_task)] - Caching data for key: indices_data
2025-06-20 23:08:30,989 - ERROR - [Thread-2 (_refresh_task)] - Error refreshing data: 'TiTStockApp' object has no attribute 'update_indices_display'
2025-06-20 23:08:31,421 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-20 23:08:31,788 - ERROR - [Thread-3 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:31,789 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-20 23:08:31,790 - INFO - [Thread-3 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-20 23:08:31,790 - INFO - [Thread-3 (_refresh_task)] - Caching data for key: indices_data
2025-06-20 23:08:31,791 - ERROR - [Thread-3 (_refresh_task)] - Error refreshing data: 'TiTStockApp' object has no attribute 'update_indices_display'
2025-06-20 23:08:32,005 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-20 23:08:32,586 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-20 23:08:33,250 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-20 23:08:34,226 - ERROR - [Thread-4 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:34,227 - WARNING - [Thread-4 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-20 23:08:35,002 - ERROR - [Thread-4 (_refresh_task)] - $VN30.VI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:35,003 - WARNING - [Thread-4 (_refresh_task)] - Creating placeholder data for ^VNI: Empty or insufficient historical data
2025-06-20 23:08:35,827 - ERROR - [Thread-4 (_refresh_task)] - $VN30.VI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:35,828 - WARNING - [Thread-4 (_refresh_task)] - Creating placeholder data for ^VN30: Empty or insufficient historical data
2025-06-20 23:08:36,491 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-20 23:08:37,197 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-20 23:08:37,806 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-20 23:08:38,469 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-20 23:08:39,239 - ERROR - [Thread-4 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:39,239 - WARNING - [Thread-4 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-20 23:08:39,822 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-20 23:08:40,405 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-20 23:08:41,002 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-20 23:08:41,661 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-20 23:08:42,248 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-20 23:08:42,867 - INFO - [Thread-4 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-20 23:08:43,541 - ERROR - [Thread-4 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-20 23:08:43,542 - WARNING - [Thread-4 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-20 23:08:44,513 - ERROR - [Thread-4 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:44,513 - WARNING - [Thread-4 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-20 23:08:45,377 - ERROR - [Thread-4 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:45,379 - WARNING - [Thread-4 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-20 23:08:45,997 - ERROR - [Thread-4 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-20 23:08:45,998 - WARNING - [Thread-4 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-20 23:08:46,805 - ERROR - [Thread-4 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-20 23:08:46,806 - WARNING - [Thread-4 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-20 23:08:46,806 - INFO - [Thread-4 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-20 23:08:46,807 - INFO - [Thread-4 (_refresh_task)] - Caching data for key: indices_data
2025-06-20 23:08:46,807 - ERROR - [Thread-4 (_refresh_task)] - Error refreshing data: 'TiTStockApp' object has no attribute 'update_indices_display'
2025-06-20 23:08:55,184 - INFO - [Thread-5 (_refresh_news_task)] - Fetching stock market news from The Globe and Mail and other sources...
2025-06-20 23:08:55,184 - INFO - [Thread-5 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/
2025-06-20 23:08:55,785 - INFO - [Thread-5 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/
2025-06-20 23:08:56,379 - INFO - [Thread-5 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/report-on-business/
2025-06-20 23:08:57,189 - INFO - [Thread-5 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/economy/
2025-06-20 23:08:58,108 - INFO - [Thread-5 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/globe-investor/
2025-06-20 23:08:58,649 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/reuters/businessNews
2025-06-20 23:08:58,869 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/news/economy
2025-06-20 23:08:59,066 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/100003114/device/rss/rss.html
2025-06-20 23:08:59,481 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/10000664/device/rss/rss.html
2025-06-20 23:08:59,645 - INFO - [Thread-6 (_refresh_news_task)] - Fetching stock market news from The Globe and Mail and other sources...
2025-06-20 23:08:59,646 - INFO - [Thread-6 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/
2025-06-20 23:09:00,370 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://feeds.bloomberg.com/markets/news.rss
2025-06-20 23:09:00,422 - INFO - [Thread-6 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/
2025-06-20 23:09:01,047 - INFO - [Thread-6 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/report-on-business/
2025-06-20 23:09:01,348 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://feeds.finance.yahoo.com/rss/2.0/headline
2025-06-20 23:09:01,670 - INFO - [Thread-6 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/economy/
2025-06-20 23:09:01,981 - INFO - [Thread-6 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/globe-investor/
2025-06-20 23:09:02,187 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.marketwatch.com/rss/topstories
2025-06-20 23:09:02,600 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/reuters/businessNews
2025-06-20 23:09:02,853 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/news/economy
2025-06-20 23:09:03,077 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/100003114/device/rss/rss.html
2025-06-20 23:09:03,585 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/10000664/device/rss/rss.html
2025-06-20 23:09:03,803 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-20 23:09:04,121 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://feeds.bloomberg.com/markets/news.rss
2025-06-20 23:09:04,543 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-20 23:09:05,212 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-20 23:09:05,519 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://feeds.finance.yahoo.com/rss/2.0/headline
2025-06-20 23:09:05,895 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.marketwatch.com/rss/topstories
2025-06-20 23:09:06,060 - INFO - [Thread-5 (_refresh_news_task)] - Successfully fetched 42 stock market news articles
2025-06-20 23:09:06,061 - INFO - [Thread-5 (_refresh_news_task)] - The Globe and Mail articles: 30, Other sources: 12
2025-06-20 23:09:06,062 - INFO - [Thread-5 (_refresh_news_task)] - Caching data for key: news
2025-06-20 23:09:06,417 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-20 23:09:07,066 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-20 23:09:07,815 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-20 23:09:08,342 - INFO - [Thread-6 (_refresh_news_task)] - Successfully fetched 42 stock market news articles
2025-06-20 23:09:08,345 - INFO - [Thread-6 (_refresh_news_task)] - The Globe and Mail articles: 30, Other sources: 12
2025-06-20 23:09:08,346 - INFO - [Thread-6 (_refresh_news_task)] - Caching data for key: news
2025-06-21 00:22:10,825 - INFO - [Thread-7 (_refresh_task)] - Cache expired for key: indices_data
2025-06-21 00:22:10,826 - INFO - [Thread-7 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-21 00:22:11,501 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-21 00:22:12,092 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-21 00:22:12,678 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-21 00:22:13,333 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-21 00:22:13,941 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-21 00:22:14,575 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-21 00:22:15,325 - ERROR - [Thread-7 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 00:22:15,326 - WARNING - [Thread-7 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-21 00:22:16,239 - ERROR - [Thread-7 (_refresh_task)] - $VN30.VI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 00:22:16,242 - WARNING - [Thread-7 (_refresh_task)] - Creating placeholder data for ^VNI: Empty or insufficient historical data
2025-06-21 00:22:17,053 - ERROR - [Thread-7 (_refresh_task)] - $VN30.VI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 00:22:17,056 - WARNING - [Thread-7 (_refresh_task)] - Creating placeholder data for ^VN30: Empty or insufficient historical data
2025-06-21 00:22:17,716 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-21 00:22:18,303 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-21 00:22:18,887 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-21 00:22:19,516 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-21 00:22:20,303 - ERROR - [Thread-7 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 00:22:20,304 - WARNING - [Thread-7 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-21 00:22:20,890 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-21 00:22:21,510 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-21 00:22:22,120 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-21 00:22:22,733 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-21 00:22:23,416 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-21 00:22:24,067 - INFO - [Thread-7 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-21 00:22:24,639 - ERROR - [Thread-7 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-21 00:22:24,640 - WARNING - [Thread-7 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-21 00:22:25,451 - ERROR - [Thread-7 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 00:22:25,452 - WARNING - [Thread-7 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-21 00:22:26,275 - ERROR - [Thread-7 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 00:22:26,276 - WARNING - [Thread-7 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-21 00:22:26,878 - ERROR - [Thread-7 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-21 00:22:26,880 - WARNING - [Thread-7 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-21 00:22:27,704 - ERROR - [Thread-7 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 00:22:27,705 - WARNING - [Thread-7 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-21 00:22:27,706 - INFO - [Thread-7 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-21 00:22:27,706 - INFO - [Thread-7 (_refresh_task)] - Caching data for key: indices_data
2025-06-21 00:22:27,707 - ERROR - [Thread-7 (_refresh_task)] - Error refreshing data: 'TiTStockApp' object has no attribute 'update_indices_display'
