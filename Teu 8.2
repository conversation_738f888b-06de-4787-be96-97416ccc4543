# Teu_9.py: Upgraded AI Market Assistant with real-time data and chat functionality
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import requests
import json
from datetime import datetime
import yfinance as yf
from newsapi import NewsApiClient
import openai

# API keys (replace with your actual keys)
NEWSAPI_KEY = 'YOUR_NEWSAPI_KEY'
OPENAI_API_KEY = 'YOUR_OPENAI_API_KEY'

class TeuMarketAssistant:
    """
    A Tkinter-based AI Market Assistant desktop application.
    
    Features:
      - AI chat assistant with real-time news and market data context
      - Automatic market prediction and analysis
      - Real-time display of Bitcoin, Oil, stock indices, and forex prices
      - Fetches latest news for context
      - Generates influencer quotes about the predictions
      - Responsive design with threading to avoid UI blocking
    """
    def __init__(self, root):
        """Initialize the market assistant UI and start data fetching."""
        self.root = root
        self.root.title("Teu Market Assistant v9")
        self.root.geometry('1000x800')
        # Use a modern theme if available
        style = ttk.Style()
        try:
            style.theme_use('clam')
        except:
            pass

        # Create Notebook for tabs
        self.notebook = ttk.Notebook(root)
        self.tab_chat = ttk.Frame(self.notebook)
        self.tab_data = ttk.Frame(self.notebook)
        self.notebook.add(self.tab_chat, text="🧠 AI Market Assistant")
        self.notebook.add(self.tab_data, text="Market Data & News")
        self.notebook.pack(fill='both', expand=True)

        # Set up chat UI and data UI
        self.setup_chat_ui()
        self.setup_data_ui()

        # Add menu for options (clear chat, exit)
        menubar = tk.Menu(root)
        root.config(menu=menubar)
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Options", menu=file_menu)
        file_menu.add_command(label="Clear Chat", command=self.clear_chat)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)

        # Storage for data
        self.news_headlines = []
        self.market_data = {}

        # Initialize OpenAI API key
        openai.api_key = OPENAI_API_KEY

        # Fetch data initially
        self.fetch_initial_data()

    def setup_chat_ui(self):
        """Set up the chat interface (history, input, prediction display)."""
        chat_frame = ttk.Frame(self.tab_chat)
        chat_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Chat history text area (read-only)
        self.chat_history = scrolledtext.ScrolledText(chat_frame, wrap=tk.WORD, height=20, state='disabled')
        self.chat_history.pack(fill=tk.BOTH, expand=True)

        # Entry for user input and send button
        entry_frame = ttk.Frame(self.tab_chat)
        entry_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        self.entry_message = tk.Entry(entry_frame, width=80, font=('Arial', 12))
        self.entry_message.pack(side=tk.LEFT, fill=tk.X, expand=True)
        send_button = ttk.Button(entry_frame, text="Send", command=self.send_message)
        send_button.pack(side=tk.RIGHT)

        # Bind Enter key to submit chat:contentReference[oaicite:0]{index=0}
        self.entry_message.bind("<Return>", lambda event: self.send_message())

        # Prediction display
        pred_frame = ttk.Frame(self.tab_chat)
        pred_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        self.prediction_label = tk.Label(pred_frame, text="Prediction and report will appear here.", anchor='w', justify='left', wraplength=500, font=('Arial', 11))
        self.prediction_label.pack(fill=tk.X)

    def setup_data_ui(self):
        """Set up UI for market data summary, news, and quotes."""
        # Summary of market data
        summary_frame = ttk.LabelFrame(self.tab_data, text="Market Summary", padding=10)
        summary_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=10)

        # Labels for each item
        self.labels = {}
        items = [("Bitcoin (USD)",), ("WTI Crude (USD)",), ("Brent Crude (USD)",),
                 ("Nasdaq",), ("S&P 500",), ("Dow Jones",),
                 ("USD/VND",), ("CAD/VND",)]
        for i, (name,) in enumerate(items):
            lbl = ttk.Label(summary_frame, text=f"{name}: updating...", font=('Arial', 11))
            lbl.grid(row=i, column=0, sticky='w', padx=5, pady=2)
            self.labels[name] = lbl

        # News headlines
        news_frame = ttk.LabelFrame(self.tab_data, text="Latest News (Headlines)", padding=10)
        news_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.news_text = scrolledtext.ScrolledText(news_frame, wrap=tk.WORD, height=15, state='disabled')
        self.news_text.pack(fill=tk.BOTH, expand=True)

        # Influencer quotes
        quotes_frame = ttk.LabelFrame(self.tab_data, text="Influencer Quotes", padding=10)
        quotes_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.quotes_text = scrolledtext.ScrolledText(quotes_frame, wrap=tk.WORD, height=15, state='disabled')
        self.quotes_text.pack(fill=tk.BOTH, expand=True)

    def fetch_initial_data(self):
        """Start background data fetch (news and market data)."""
        threading.Thread(target=self.fetch_all_data, daemon=True).start()
        # Refresh data every 5 minutes (300000 ms)
        self.root.after(300000, self.fetch_initial_data)

    def fetch_all_data(self):
        """Fetch news and market prices in background."""
        self.fetch_news()
        self.fetch_market_prices()

    def fetch_news(self):
        """Fetch latest news headlines from NewsAPI."""
        try:
            # Using NewsAPI to fetch top headlines (pageSize=20 by default):contentReference[oaicite:1]{index=1}
            newsapi = NewsApiClient(api_key=NEWSAPI_KEY)
            top_headlines = newsapi.get_top_headlines(language='en', page_size=20)
            articles = top_headlines.get('articles', [])
            self.news_headlines = []
            for art in articles:
                title = art.get('title', 'No title')
                source = art.get('source', {}).get('name', 'Unknown')
                published = art.get('publishedAt', '')[:10]
                self.news_headlines.append(f"{title} ({source}, {published})")
            # Update news UI in main thread
            self.root.after(0, self.display_news)
        except Exception as e:
            print("Error fetching news:", e)

    def display_news(self):
        """Display fetched news headlines in the UI."""
        self.news_text.configure(state='normal')
        self.news_text.delete('1.0', tk.END)
        for line in self.news_headlines:
            self.news_text.insert(tk.END, line + "\n\n")
        self.news_text.configure(state='disabled')

    def fetch_market_prices(self):
        """Fetch current market prices using yfinance."""
        # Use yfinance to fetch real-time market data (stocks, crypto, commodities):contentReference[oaicite:2]{index=2}
        symbols = {
            "Bitcoin (USD)": "BTC-USD",
            "WTI Crude (USD)": "CL=F",
            "Brent Crude (USD)": "BZ=F",
            "Nasdaq": "^IXIC",
            "S&P 500": "^GSPC",
            "Dow Jones": "^DJI",
            "USD/VND": "USDVND=X",
            "CAD/VND": "CADVND=X"
        }
        for name, symbol in symbols.items():
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period='2d')
                if len(hist) >= 2:
                    prev_close = hist['Close'][-2]
                    curr_price = hist['Close'][-1]
                elif len(hist) == 1:
                    prev_close = hist['Close'][-1]
                    curr_price = prev_close
                else:
                    prev_close = curr_price = 0
                change_pct = ((curr_price - prev_close) / prev_close * 100) if prev_close else 0
                self.market_data[name] = (curr_price, change_pct)
            except Exception as e:
                print(f"Error fetching {name} data:", e)
                self.market_data[name] = (0, 0)
        # Update market UI in main thread
        self.root.after(0, self.display_market_data)

    def display_market_data(self):
        """Display market prices and percentage changes in the UI."""
        for name, (price, change) in self.market_data.items():
            arrow = '▲' if change >= 0 else '▼'
            text = f"{price:.2f} {arrow}{abs(change):.2f}%"
            lbl = self.labels.get(name)
            if lbl:
                lbl.config(text=f"{name}: {text}")

    def send_message(self):
        """Handle user message submission and start AI processing."""
        user_msg = self.entry_message.get().strip()
        if not user_msg:
            return
        self.entry_message.delete(0, tk.END)
        # Display user message in chat history
        self.chat_history.configure(state='normal')
        self.chat_history.insert(tk.END, f"You: {user_msg}\n")
        self.chat_history.configure(state='disabled')
        self.chat_history.see(tk.END)
        # Process AI response and update prediction in background (avoid UI freeze):contentReference[oaicite:3]{index=3}
        threading.Thread(target=self.handle_user_message, args=(user_msg,), daemon=True).start()

    def handle_user_message(self, user_msg):
        """Process the user message: query AI assistant and update chat."""
        # Prepare system prompt including market data and news for context
        market_info = ", ".join(f"{k}={v[0]:.2f}" for k,v in self.market_data.items())
        news_info = " | ".join(self.news_headlines[:5]) if self.news_headlines else ""
        system_prompt = (
            "You are an AI financial market assistant. "
            f"Current market data: {market_info}. "
            f"Latest news headlines: {news_info}. "
            "Answer the user's question about market trends based on this information."
        )
        # Include chat history in system prompt for conversation context
        chat_history_text = self.chat_history.get("1.0", tk.END).strip()
        if chat_history_text:
            system_prompt += f" Conversation so far: {chat_history_text}"
        try:
            # Call OpenAI ChatCompletion to get AI assistant's response:contentReference[oaicite:4]{index=4}
            response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_msg}
                ],
                temperature=0.7,
            )
            assistant_msg = response["choices"][0]["message"]["content"].strip()
        except Exception as e:
            assistant_msg = "Error: could not get response."
            print("OpenAI API error:", e)
        # Display assistant response
        self.root.after(0, lambda: self.display_message("Assistant", assistant_msg))
        # After response, update prediction and quotes
        self.root.after(0, self.update_prediction_and_quotes)

    def display_message(self, sender, message):
        """Display a message in the chat history."""
        self.chat_history.configure(state='normal')
        self.chat_history.insert(tk.END, f"{sender}: {message}\n\n")
        self.chat_history.configure(state='disabled')
        self.chat_history.see(tk.END)

    def update_prediction_and_quotes(self):
        """Generate and display market prediction with influencer quotes."""
        # Build context for prediction
        market_info = ", ".join(f"{k} is {v[0]:.2f} (change {v[1]:+.2f}%)" for k,v in self.market_data.items())
        news_info = " | ".join(self.news_headlines[:5]) if self.news_headlines else ""
        system_prompt = (
            "You are an expert financial analyst. "
            f"Based on current market data ({market_info}) and news headlines ({news_info}), "
            "provide a concise market prediction (bullish or bearish) with reasoning."
        )
        # Include chat context in prediction prompt
        chat_context = self.chat_history.get("1.0", tk.END).strip()
        if chat_context:
            system_prompt += f" Conversation so far: {chat_context}"
        try:
            # Get market prediction from AI (GPT-4):contentReference[oaicite:5]{index=5}
            pred_response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": "What is your market prediction and why?"}
                ],
                temperature=0.7,
            )
            prediction_text = pred_response["choices"][0]["message"]["content"].strip()
        except Exception as e:
            prediction_text = "Prediction unavailable."
            print("Prediction error:", e)
        # Display prediction
        self.prediction_label.config(text=prediction_text)
        # Generate influencer quotes
        influencers = ["Elon Musk", "Donald Trump", "Vladimir Putin", "Warren Buffett", "Bill Gates"]
        quotes = []
        for name in influencers:
            try:
                quote_resp = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=[
                        {"role": "system", "content": f"You are {name}. Write a quote about this market prediction: \"{prediction_text}\""},
                        {"role": "user", "content": f"The prediction is: {prediction_text}"}
                    ],
                    temperature=0.7,
                )
                quote_text = quote_resp["choices"][0]["message"]["content"].strip()
            except Exception as e:
                quote_text = f"[Error generating quote for {name}]"
                print(f"Quote error for {name}:", e)
            quotes.append(f"{name}: \"{quote_text}\"")
        # Display quotes
        self.quotes_text.configure(state='normal')
        self.quotes_text.delete('1.0', tk.END)
        for q in quotes:
            self.quotes_text.insert(tk.END, q + "\n\n")
        self.quotes_text.configure(state='disabled')

    def clear_chat(self):
        """Clear chat history, prediction, and quotes from the UI."""
        self.chat_history.configure(state='normal')
        self.chat_history.delete('1.0', tk.END)
        self.chat_history.configure(state='disabled')
        self.prediction_label.config(text="")
        self.quotes_text.configure(state='normal')
        self.quotes_text.delete('1.0', tk.END)
        self.quotes_text.configure(state='disabled')

if __name__ == "__main__":
    root = tk.Tk()
    app = TeuMarketAssistant(root)
    root.mainloop()
