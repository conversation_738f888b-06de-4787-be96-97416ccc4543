# Teu 9: Full AI-Powered Financial Assistant with Market Prediction and Live Data

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import requests
import json
from datetime import datetime
import yfinance as yf
from newsapi import NewsApiClient
import openai

# ----------------- CONFIGURATION -----------------
# Replace with your actual keys
NEWSAPI_KEY = "YOUR_NEWSAPI_KEY"
OPENAI_API_KEY = "YOUR_OPENAI_API_KEY"

openai.api_key = OPENAI_API_KEY
class TeuMarketAssistant:
    def __init__(self, root):
        self.root = root
        self.root.title("Teu 9 - AI Market Assistant")
        self.root.geometry("1200x850")

        self.news_headlines = []
        self.market_data = {}
        
        self._setup_ui()
        self._start_fetching_data()
    
    def _setup_ui(self):
        self.notebook = ttk.Notebook(self.root)
        self.tab_chat = ttk.Frame(self.notebook)
        self.tab_data = ttk.Frame(self.notebook)
        self.notebook.add(self.tab_chat, text="🧠 AI Market Assistant")
        self.notebook.add(self.tab_data, text="📈 Market Data & News")
        self.notebook.pack(fill=tk.BOTH, expand=True)

        self._setup_chat_ui()
        self._setup_data_ui()

        menubar = tk.Menu(self.root)
        filemenu = tk.Menu(menubar, tearoff=0)
        filemenu.add_command(label="Clear Chat", command=self.clear_chat)
        filemenu.add_separator()
        filemenu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="Options", menu=filemenu)
        self.root.config(menu=menubar)
    def _setup_chat_ui(self):
        # Chat panel
        chat_frame = ttk.Frame(self.tab_chat)
        chat_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.chat_history = scrolledtext.ScrolledText(chat_frame, wrap=tk.WORD, height=20, state='disabled')
        self.chat_history.pack(fill=tk.BOTH, expand=True)

        input_frame = ttk.Frame(self.tab_chat)
        input_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        self.entry_message = tk.Entry(input_frame, font=('Arial', 12))
        self.entry_message.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.entry_message.bind("<Return>", lambda e: self.send_message())

        send_button = ttk.Button(input_frame, text="Send", command=self.send_message)
        send_button.pack(side=tk.RIGHT)

        self.prediction_label = tk.Label(
            self.tab_chat,
            text="Market prediction will appear here.",
            wraplength=1000,
            justify='left',
            font=('Arial', 11, 'italic'),
            anchor='w'
        )
        self.prediction_label.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)

    def _setup_data_ui(self):
        # Market and news panel
        self.labels = {}
        market_frame = ttk.LabelFrame(self.tab_data, text="Live Market Data")
        market_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)

        fields = [
            "Bitcoin (USD)", "WTI Crude (USD)", "Brent Crude (USD)",
            "NASDAQ", "S&P 500", "Dow Jones", "USD/VND", "CAD/VND"
        ]
        for i, field in enumerate(fields):
            label = ttk.Label(market_frame, text=f"{field}: Loading...", font=("Arial", 11))
            label.grid(row=i, column=0, sticky="w", padx=10, pady=5)
            self.labels[field] = label

        news_frame = ttk.LabelFrame(self.tab_data, text="Top 20 Market News")
        news_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.news_text = scrolledtext.ScrolledText(news_frame, wrap=tk.WORD, height=18, state='disabled')
        self.news_text.pack(fill=tk.BOTH, expand=True)

        quotes_frame = ttk.LabelFrame(self.tab_data, text="Influencer Quotes")
        quotes_frame.pack(side=tk.BOTTOM, fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.quotes_text = scrolledtext.ScrolledText(quotes_frame, wrap=tk.WORD, height=10, state='disabled')
        self.quotes_text.pack(fill=tk.BOTH, expand=True)
    def _setup_chat_ui(self):
        # Chat panel
        chat_frame = ttk.Frame(self.tab_chat)
        chat_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.chat_history = scrolledtext.ScrolledText(chat_frame, wrap=tk.WORD, height=20, state='disabled')
        self.chat_history.pack(fill=tk.BOTH, expand=True)

        input_frame = ttk.Frame(self.tab_chat)
        input_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        self.entry_message = tk.Entry(input_frame, font=('Arial', 12))
        self.entry_message.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.entry_message.bind("<Return>", lambda e: self.send_message())

        send_button = ttk.Button(input_frame, text="Send", command=self.send_message)
        send_button.pack(side=tk.RIGHT)

        self.prediction_label = tk.Label(
            self.tab_chat,
            text="Market prediction will appear here.",
            wraplength=1000,
            justify='left',
            font=('Arial', 11, 'italic'),
            anchor='w'
        )
        self.prediction_label.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)

    def _setup_data_ui(self):
        # Market and news panel
        self.labels = {}
        market_frame = ttk.LabelFrame(self.tab_data, text="Live Market Data")
        market_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)

        fields = [
            "Bitcoin (USD)", "WTI Crude (USD)", "Brent Crude (USD)",
            "NASDAQ", "S&P 500", "Dow Jones", "USD/VND", "CAD/VND"
        ]
        for i, field in enumerate(fields):
            label = ttk.Label(market_frame, text=f"{field}: Loading...", font=("Arial", 11))
            label.grid(row=i, column=0, sticky="w", padx=10, pady=5)
            self.labels[field] = label

        news_frame = ttk.LabelFrame(self.tab_data, text="Top 20 Market News")
        news_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.news_text = scrolledtext.ScrolledText(news_frame, wrap=tk.WORD, height=18, state='disabled')
        self.news_text.pack(fill=tk.BOTH, expand=True)

        quotes_frame = ttk.LabelFrame(self.tab_data, text="Influencer Quotes")
        quotes_frame.pack(side=tk.BOTTOM, fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.quotes_text = scrolledtext.ScrolledText(quotes_frame, wrap=tk.WORD, height=10, state='disabled')
        self.quotes_text.pack(fill=tk.BOTH, expand=True)
    def _start_fetching_data(self):
        threading.Thread(target=self._fetch_all_data, daemon=True).start()
        self.root.after(300000, self._start_fetching_data)  # auto-refresh every 5 mins

    def _fetch_all_data(self):
        self._fetch_market_prices()
        self._fetch_news()

    def _fetch_market_prices(self):
        symbols = {
            "Bitcoin (USD)": "BTC-USD",
            "WTI Crude (USD)": "CL=F",
            "Brent Crude (USD)": "BZ=F",
            "NASDAQ": "^IXIC",
            "S&P 500": "^GSPC",
            "Dow Jones": "^DJI",
            "USD/VND": "USDVND=X",
            "CAD/VND": "CADVND=X"
        }

        for name, symbol in symbols.items():
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="2d")
                if len(hist) >= 2:
                    prev = hist["Close"][-2]
                    curr = hist["Close"][-1]
                else:
                    prev = curr = hist["Close"][-1] if len(hist) else 0

                change = (curr - prev) / prev * 100 if prev else 0
                self.market_data[name] = (curr, change)
            except Exception as e:
                print(f"Error fetching {name}:", e)
                self.market_data[name] = (0, 0)

        self.root.after(0, self._display_market_prices)

    def _display_market_prices(self):
        for name, (price, change) in self.market_data.items():
            label = self.labels.get(name)
            arrow = '▲' if change >= 0 else '▼'
            pct = f"{arrow} {abs(change):.2f}%"
            label.config(text=f"{name}: ${price:,.2f} ({pct})")
    def _fetch_news(self):
        try:
            newsapi = NewsApiClient(api_key=NEWSAPI_KEY)
            response = newsapi.get_top_headlines(language='en', page_size=20)
            self.news_headlines = []

            for article in response.get('articles', []):
                title = article.get('title', 'No Title')
                src = article.get('source', {}).get('name', 'Unknown')
                date = article.get('publishedAt', '')[:10]
                self.news_headlines.append(f"{title} ({src}, {date})")

            self.root.after(0, self._display_news)
        except Exception as e:
            print("News fetch error:", e)
            self.news_headlines = []

    def _display_news(self):
        self.news_text.config(state='normal')
        self.news_text.delete("1.0", tk.END)
        for line in self.news_headlines:
            self.news_text.insert(tk.END, line + "\n\n")
        self.news_text.config(state='disabled')

    def send_message(self):
        msg = self.entry_message.get().strip()
        if not msg:
            return
        self.entry_message.delete(0, tk.END)
        self._append_chat("You", msg)
        threading.Thread(target=self._process_ai_response, args=(msg,), daemon=True).start()

    def _append_chat(self, sender, message):
        self.chat_history.config(state='normal')
        self.chat_history.insert(tk.END, f"{sender}: {message}\n\n")
        self.chat_history.config(state='disabled')
        self.chat_history.see(tk.END)

    def _process_ai_response(self, msg):
        try:
            market_summary = ", ".join(f"{k}: {v[0]:.2f}" for k, v in self.market_data.items())
            news_summary = " | ".join(self.news_headlines[:5])

            system_prompt = (
                f"You are an expert financial AI assistant.\n"
                f"Market data: {market_summary}\n"
                f"Top news: {news_summary}\n"
                f"User chat so far: {self.chat_history.get('1.0', tk.END).strip()}\n"
                f"Respond professionally."
            )

            response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": msg}
                ],
                temperature=0.6
            )
            ai_reply = response['choices'][0]['message']['content'].strip()
            self.root.after(0, lambda: self._append_chat("AI", ai_reply))
            self.root.after(0, self._generate_prediction_and_quotes)
        except Exception as e:
            print("AI Chat error:", e)
            self.root.after(0, lambda: self._append_chat("AI", "Sorry, I couldn't process that."))

    def _generate_prediction_and_quotes(self):
        try:
            prompt = (
                "Based on the following market data and news, give a 24h Bitcoin forecast.\n"
                f"Market: {json.dumps(self.market_data)}\n"
                f"News: {' '.join(self.news_headlines[:5])}\n"
            )
            pred = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}]
            )
            pred_text = pred['choices'][0]['message']['content'].strip()
            self.prediction_label.config(text="📊 Prediction:\n" + pred_text)

            influencers = ["Elon Musk", "Donald Trump", "Vladimir Putin", "MicroStrategy CEO", "BlackRock Analyst"]
            quotes = []

            for name in influencers:
                q_prompt = f"What might {name} say about this market prediction: {pred_text}"
                q = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=[{"role": "user", "content": q_prompt}]
                )
                quotes.append(f"{name}: \"{q['choices'][0]['message']['content'].strip()}\"")

            self.quotes_text.config(state='normal')
            self.quotes_text.delete("1.0", tk.END)
            for q in quotes:
                self.quotes_text.insert(tk.END, q + "\n\n")
            self.quotes_text.config(state='disabled')

        except Exception as e:
            print("Prediction error:", e)
            self.prediction_label.config(text="Error generating prediction.")
    def clear_chat(self):
        self.chat_history.config(state='normal')
        self.chat_history.delete("1.0", tk.END)
        self.chat_history.config(state='disabled')
        self.prediction_label.config(text="")
        self.quotes_text.config(state='normal')
        self.quotes_text.delete("1.0", tk.END)
        self.quotes_text.config(state='disabled')


if __name__ == "__main__":
    root = tk.Tk()
    app = TeuMarketAssistant(root)
    root.mainloop()







