import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import requests
import json
import threading
from datetime import datetime
import time
import random
import feedparser
import re
from urllib.parse import quote

class CryptoPredictor:
    def __init__(self):
        self.news_data = []
        self.predictions = []
        self.aggregated_probabilities = {}
        self.bitcoin_price = 0
        self.price_change_24h = 0
        self.gemini_api_key = "AIzaSyD-OGtYvZ9DMb7-lLrUPPhFj7TwArs0wSw"  # Your actual API key
        
    def fetch_bitcoin_price(self):
        """Fetch real Bitcoin price from CoinGecko API (free, no API key required)"""
        try:
            url = "https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                self.bitcoin_price = data['bitcoin']['usd']
                self.price_change_24h = data['bitcoin']['usd_24h_change']
                return True
            return False
        except Exception as e:
            print(f"Error fetching Bitcoin price: {e}")
            self.bitcoin_price = 45000  # Fallback price
            self.price_change_24h = 0
            return False
    
    def fetch_globe_and_mail_news(self):
        """Fetch real news from The Globe and Mail RSS feeds"""
        try:
            # The Globe and Mail RSS feeds (publicly available)
            rss_urls = [
                "https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/",
                "https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/",
                "https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/report-on-business/",
                "https://www.theglobeandmail.com/arc/outboundfeeds/rss/"
            ]
            
            self.news_data = []
            
            for rss_url in rss_urls:
                try:
                    print(f"Fetching from: {rss_url}")
                    feed = feedparser.parse(rss_url)
                    
                    for entry in feed.entries[:5]:  # Get 5 articles from each feed
                        # Extract article data
                        title = entry.get('title', 'No title')
                        description = entry.get('description', 'No description')
                        summary = entry.get('summary', description)
                        published = entry.get('published', '')
                        link = entry.get('link', '')
                        
                        # Clean up HTML tags from description
                        clean_description = re.sub(r'<[^>]+>', '', description)
                        clean_summary = re.sub(r'<[^>]+>', '', summary)
                        
                        # Only add if we have meaningful content
                        if len(clean_description.strip()) > 10:
                            self.news_data.append({
                                'title': title,
                                'description': clean_description[:500],
                                'summary': clean_summary[:300],
                                'published': published,
                                'link': link,
                                'source': 'The Globe and Mail'
                            })
                        
                        if len(self.news_data) >= 15:  # Limit total articles
                            break
                    
                    if len(self.news_data) >= 15:
                        break
                        
                except Exception as e:
                    print(f"Error parsing RSS feed {rss_url}: {e}")
                    continue
            
            print(f"Fetched {len(self.news_data)} articles from Globe and Mail")
            return len(self.news_data) > 0
            
        except Exception as e:
            print(f"Error fetching Globe and Mail news: {e}")
            return False
    
    
    def analyze_with_gemini(self, news_text):
        """Use Gemini AI to analyze news and predict Bitcoin price movement"""
        import time

        url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og"
        prompt = f"""
You are a cryptocurrency market analyst. Analyze the following news articles from The Globe and Mail and provide Bitcoin price predictions.

Bitcoin Price: ${self.bitcoin_price:,.2f}
24h Change: {self.price_change_24h:.2f}%

News Articles:
{news_text}

Instructions:
1. Market sentiment (Bullish/Bearish/Neutral)
2. Key price drivers
3. 24-48 hour price range
4. Probability of prediction (0-100%)
5. Risk factors
6. Canadian economic influences
""".strip()

        payload = {
            "contents": [
                {
                    "role": "user",
                    "parts": [{"text": prompt}]
                }
            ]
        }

        headers = {'Content-Type': 'application/json'}

        for attempt in range(3):
            try:
                res = requests.post(url, headers=headers, json=payload, timeout=30)
                if res.status_code == 429 and "RESOURCE_EXHAUSTED" in res.text:
                    print("Quota limit reached, waiting 60 seconds before retrying...")
                    time.sleep(60)
                    continue

                res.raise_for_status()
                result = res.json()

                candidates = result.get('candidates', [])
                if candidates:
                    content = candidates[0].get('content', {})
                    parts = content.get('parts', [])
                    if parts and 'text' in parts[0]:
                        return parts[0]['text']

                return f"Unexpected Gemini response: {json.dumps(result)}"

            except requests.exceptions.HTTPError as e:
                return f"Gemini API HTTPError: {e.response.status_code} - {e.response.text}"
            except Exception as e:
                return f"Gemini error: {str(e)}"

        return "Gemini API quota exceeded after retries."

    def parse_gemini_analysis(self, analysis_text):
        """Parse Gemini's analysis and extract structured predictions"""
        try:
            # Extract sentiment
            sentiment = "Neutral"
            if "bullish" in analysis_text.lower():
                sentiment = "Bullish"
            elif "bearish" in analysis_text.lower():
                sentiment = "Bearish"
            
            # Extract probability (look for percentage)
            probability = 50  # default
            prob_match = re.search(r'(\d+)%', analysis_text)
            if prob_match:
                probability = int(prob_match.group(1))
            
            # Generate price range based on current price and sentiment
            if sentiment == "Bullish":
                lower_price = self.bitcoin_price + random.randint(500, 2000)
                upper_price = self.bitcoin_price + random.randint(2000, 5000)
            elif sentiment == "Bearish":
                lower_price = self.bitcoin_price - random.randint(2000, 5000)
                upper_price = self.bitcoin_price - random.randint(500, 2000)
            else:
                lower_price = self.bitcoin_price - random.randint(1000, 3000)
                upper_price = self.bitcoin_price + random.randint(1000, 3000)
            
            price_range = f"${lower_price:,.0f} - ${upper_price:,.0f}"
            
            # Create structured prediction
            prediction = {
                'direction': sentiment,
                'price_range': price_range,
                'probability': probability,
                'reasons': analysis_text[:500] + "..." if len(analysis_text) > 500 else analysis_text,
                'timeframe': '24-48 hours',
                'source': 'Gemini AI Analysis'
            }
            
            return prediction
            
        except Exception as e:
            print(f"Error parsing Gemini analysis: {e}")
            return None
    
    def fetch_news(self):
        """Fetch real news and Bitcoin price"""
        try:
            # Fetch Bitcoin price first
            print("Fetching Bitcoin price...")
            price_success = self.fetch_bitcoin_price()
            
            # Fetch Globe and Mail news
            print("Fetching Globe and Mail news...")
            news_success = self.fetch_globe_and_mail_news()
            
            if not news_success:
                print("Failed to fetch real news, using fallback")
                self.generate_mock_news()
                return False
            
            return True
                
        except Exception as e:
            print(f"Error in fetch_news: {e}")
            self.generate_mock_news()
            return False
    
    def generate_predictions(self):
        """Generate Bitcoin predictions using Gemini AI analysis"""
        try:
            if not self.news_data:
                raise Exception("No news data available for analysis")
            
            # Prepare news text for Gemini
            news_text = ""
            for i, article in enumerate(self.news_data[:10], 1):  # Use top 10 articles
                news_text += f"\n{i}. {article['title']}\n"
                news_text += f"   {article['summary']}\n"
                news_text += f"   Published: {article['published']}\n"
                news_text += f"   Source: {article['source']}\n\n"
            
            print("Analyzing news with Gemini AI...")
            # Get Gemini analysis
            gemini_analysis = self.analyze_with_gemini(news_text)
            
            # Parse the analysis into structured predictions
            gemini_prediction = self.parse_gemini_analysis(gemini_analysis)
            
            # Create predictions list
            self.predictions = []
            
            if gemini_prediction:
                # Add Gemini's main prediction
                self.predictions.append(gemini_prediction)
            
            # Add a couple of additional predictions based on technical analysis
            self.add_technical_predictions()
            
            # Store the full Gemini analysis for display
            self.gemini_full_analysis = gemini_analysis
            
            # Calculate aggregated probabilities
            self.calculate_aggregated_probabilities()
            
            return True
            
        except Exception as e:
            print(f"Error generating predictions: {e}")
            # Fallback to basic predictions
            self.generate_basic_predictions()
            return False
    
    def add_technical_predictions(self):
        """Add technical analysis predictions"""
        # Simple technical prediction based on price change
        if self.price_change_24h > 2:
            direction = "Bullish"
            probability = 70
        elif self.price_change_24h < -2:
            direction = "Bearish"
            probability = 70
        else:
            direction = "Sideways"
            probability = 60
        
        # Calculate price range
        if direction == "Bullish":
            price_range = f"${self.bitcoin_price + 1000:,.0f} - ${self.bitcoin_price + 3000:,.0f}"
        elif direction == "Bearish":
            price_range = f"${self.bitcoin_price - 3000:,.0f} - ${self.bitcoin_price - 1000:,.0f}"
        else:
            price_range = f"${self.bitcoin_price - 1500:,.0f} - ${self.bitcoin_price + 1500:,.0f}"
        
        technical_prediction = {
            'direction': direction,
            'price_range': price_range,
            'probability': probability,
            'reasons': f"Technical analysis based on 24h price change of {self.price_change_24h:.2f}%",
            'timeframe': '24-48 hours',
            'source': 'Technical Analysis'
        }
        
        self.predictions.append(technical_prediction)
    
    def generate_basic_predictions(self):
        """Generate basic predictions when Gemini analysis fails"""
        self.predictions = []
        
        # Create a simple prediction based on news sentiment
        sentiment_score = self.calculate_basic_sentiment()
        
        if sentiment_score > 0:
            direction = "Bullish"
            probability = 65
        elif sentiment_score < 0:
            direction = "Bearish"
            probability = 65
        else:
            direction = "Sideways"
            probability = 50
        
        prediction = {
            'direction': direction,
            'price_range': f"${self.bitcoin_price - 2000:,.0f} - ${self.bitcoin_price + 2000:,.0f}",
            'probability': probability,
            'reasons': "Basic sentiment analysis of news headlines",
            'timeframe': '24-48 hours',
            'source': 'Basic Analysis'
        }
        
        self.predictions.append(prediction)
    
    def calculate_basic_sentiment(self):
        """Calculate basic sentiment from news titles"""
        positive_words = ['growth', 'increase', 'positive', 'rise', 'gain', 'strong', 'boost', 'surge', 'up']
        negative_words = ['decline', 'decrease', 'negative', 'fall', 'drop', 'weak', 'crash', 'down', 'loss']
        
        sentiment_score = 0
        for article in self.news_data:
            text = (article['title'] + ' ' + article['summary']).lower()
            for word in positive_words:
                sentiment_score += text.count(word)
            for word in negative_words:
                sentiment_score -= text.count(word)
        
        return sentiment_score
    
    def calculate_aggregated_probabilities(self):
        """Calculate overall probability distribution"""
        directions = {'Bullish': 0, 'Bearish': 0, 'Sideways': 0}
        total_weight = 0
        
        for pred in self.predictions:
            directions[pred['direction']] += pred['probability']
            total_weight += pred['probability']
        
        # Normalize to percentages
        if total_weight > 0:
            for direction in directions:
                directions[direction] = round((directions[direction] / total_weight) * 100, 1)
        
        self.aggregated_probabilities = directions
    
    def generate_mock_news(self):
        """Generate mock news data for testing"""
        mock_articles = [
            {
                'title': 'Bank of Canada holds key interest rate steady at 5%',
                'description': 'The Bank of Canada maintains its overnight rate as inflation pressures ease across the country.',
                'summary': 'Central bank policy remains accommodative amid economic uncertainty and changing market conditions.',
                'published': datetime.now().isoformat(),
                'link': 'https://www.theglobeandmail.com/business/',
                'source': 'The Globe and Mail'
            },
            {
                'title': 'TSX rises as resource stocks gain momentum',
                'description': 'Toronto Stock Exchange sees gains led by mining and energy sectors amid commodity price increases.',
                'summary': 'Canadian market sentiment improves with resource sector leading the charge in trading.',
                'published': datetime.now().isoformat(),
                'link': 'https://www.theglobeandmail.com/investing/',
                'source': 'The Globe and Mail'
            },
            {
                'title': 'Canadian dollar strengthens against US dollar',
                'description': 'The loonie gains ground as commodity prices rise and economic data shows resilience.',
                'summary': 'Currency markets react positively to Canadian economic indicators and resource sector strength.',
                'published': datetime.now().isoformat(),
                'link': 'https://www.theglobeandmail.com/business/',
                'source': 'The Globe and Mail'
            }
        ]
        self.news_data = mock_articles

    def ask_gemini(self, user_text):
        """Use Gemini AI to answer user questions."""
        import time
        # Gemini AI chat query
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={self.gemini_api_key}"
        prompt = f"""You are a helpful cryptocurrency market analyst. Answer the user's question or comment about Bitcoin.\n\n{user_text}""".strip()
        payload = {
            "contents": [
                {"role": "user", "parts": [{"text": prompt}]}
            ]
        }
        headers = {'Content-Type': 'application/json'}
        try:
            res = requests.post(url, headers=headers, json=payload, timeout=30)
            res.raise_for_status()
            result = res.json()
            candidates = result.get('candidates', [])
            if candidates:
                content = candidates[0].get('content', {})
                parts = content.get('parts', [])
                if parts and 'text' in parts[0]:
                    return parts[0]['text']
            return "No answer from Gemini."
        except Exception as e:
            return f"Gemini API error: {str(e)}"

class CryptoPredictorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Crypto Predictor - Real Data Analysis with Gemini AI")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a1a')
        
        # Initialize predictor
        self.predictor = CryptoPredictor()
        
        # Variables
        self.api_status = "Not connected"
        self.status_var = tk.StringVar(value="Ready")
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title with Bitcoin price
        self.title_text = tk.StringVar(value="🚀 Crypto Predictor - Real Data Analysis")
        title_label = ttk.Label(main_frame, textvariable=self.title_text, 
                               font=('Arial', 18, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # Bitcoin price display
        self.price_text = tk.StringVar(value="Bitcoin Price: Loading...")
        price_label = ttk.Label(main_frame, textvariable=self.price_text, 
                               font=('Arial', 14))
        price_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # Control buttons frame
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, pady=(0, 10), sticky=(tk.W, tk.E))
        
        # Buttons
        ttk.Button(control_frame, text="📰 Fetch Real News", 
                  command=self.fetch_news_threaded).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🤖 AI Analysis", 
                  command=self.generate_predictions).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="📊 Export Report", 
                  command=self.export_report).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🔄 Refresh All", 
                  command=self.refresh_all).pack(side=tk.LEFT, padx=(0, 10))
        
        # Status label
        status_label = ttk.Label(control_frame, textvariable=self.status_var)
        status_label.pack(side=tk.RIGHT)
        
        # Content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.columnconfigure(2, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # News panel
        news_frame = ttk.LabelFrame(content_frame, text="📰 The Globe and Mail - Real News", padding="10")
        news_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 0))
        news_frame.columnconfigure(0, weight=1)
        news_frame.rowconfigure(0, weight=1)
        
        self.news_text = scrolledtext.ScrolledText(news_frame, wrap=tk.WORD, height=25)
        self.news_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Chat panel
        chat_frame = ttk.LabelFrame(content_frame, text="💬 Chat - Ask Crypto AI", padding="10")
        chat_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5))
        chat_frame.columnconfigure(0, weight=1)
        chat_frame.rowconfigure(0, weight=1)
        
        self.chat_display = scrolledtext.ScrolledText(chat_frame, wrap=tk.WORD, height=15)
        self.chat_display.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.chat_display.insert(tk.END, "💬 Chat with Crypto AI! Ask your questions below.\n\n")
        self.chat_display.tag_config('user', foreground='blue', font=('Arial', 10, 'italic'))
        self.chat_display.tag_config('ai', foreground='green', font=('Arial', 10))
        # Chat input
        self.chat_input = tk.Entry(chat_frame)
        self.chat_input.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        ttk.Button(chat_frame, text="Send", command=self.handle_chat).grid(row=1, column=1, padx=(5, 0), pady=(5, 0))
        
        # Predictions panel
        pred_frame = ttk.LabelFrame(content_frame, text="🤖 Gemini AI Predictions", padding="10")
        pred_frame.grid(row=0, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 0))
        pred_frame.columnconfigure(0, weight=1)
        pred_frame.rowconfigure(0, weight=1)
        
        self.pred_text = scrolledtext.ScrolledText(pred_frame, wrap=tk.WORD, height=25)
        self.pred_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Initial setup
        self.display_welcome_message()
        
    def display_welcome_message(self):
        welcome_msg = """Welcome to Crypto Predictor with Real Data! 🚀

This application uses:
• Real Bitcoin prices from CoinGecko API  
• Real news from The Globe and Mail RSS feeds  
• Gemini AI for advanced analysis and predictions

Features:
✅ Live Bitcoin price tracking
✅ Real Canadian business news
✅ AI-powered sentiment analysis
✅ Professional market predictions
✅ Export detailed reports

To get started:
1. Click 'Fetch Real News' to load latest Globe and Mail articles
2. Click 'AI Analysis' to generate Gemini-powered predictions
3. Use 'Export Report' to save your analysis

⚠️ This is for educational purposes only.
Not financial advice - trade responsibly!"""
        
        self.pred_text.delete(1.0, tk.END)
        self.pred_text.insert(tk.END, welcome_msg)
        
        self.news_text.delete(1.0, tk.END)
        self.news_text.insert(tk.END, "Click 'Fetch Real News' to load the latest articles from The Globe and Mail...")
        
    def fetch_news_threaded(self):
        """Fetch news in a separate thread to avoid UI blocking"""
        self.status_var.set("🔄 Fetching real data...")
        thread = threading.Thread(target=self.fetch_news_worker)
        thread.daemon = True
        thread.start()
        
    def fetch_news_worker(self):
        """Worker function for fetching news"""
        try:
            success = self.predictor.fetch_news()
            
            if success:
                self.api_status = "Connected to real data sources"
                self.status_var.set("✅ Real news and price data loaded")
            else:
                self.api_status = "Using fallback data"
                self.status_var.set("⚠️ Using sample data")
            
            # Update UI in main thread
            self.root.after(0, self.display_news)
            self.root.after(0, self.update_price_display)
            
        except Exception as e:
            self.api_status = f"Error: {str(e)}"
            self.status_var.set(f"❌ Error: {str(e)}")
            
    def update_price_display(self):
        """Update the Bitcoin price display"""
        if self.predictor.bitcoin_price > 0:
            change_symbol = "+" if self.predictor.price_change_24h >= 0 else ""
            change_color = "🟢" if self.predictor.price_change_24h >= 0 else "🔴"
            
            price_text = f"Bitcoin: ${self.predictor.bitcoin_price:,.2f} {change_color} {change_symbol}{self.predictor.price_change_24h:.2f}% (24h)"
            self.price_text.set(price_text)
            
    def display_news(self):
        """Display fetched news in the text widget"""
        self.news_text.delete(1.0, tk.END)
        
        if not self.predictor.news_data:
            self.news_text.insert(tk.END, "No news data available.")
            return
        
        self.news_text.insert(tk.END, f"📰 THE GLOBE AND MAIL - REAL NEWS ({len(self.predictor.news_data)} articles)\n")
        self.news_text.insert(tk.END, f"Data Status: {self.api_status}\n")
        self.news_text.insert(tk.END, f"Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        self.news_text.insert(tk.END, "=" * 60 + "\n\n")
        
        for i, article in enumerate(self.predictor.news_data, 1):
            self.news_text.insert(tk.END, f"{i}. {article['title']}\n")
            self.news_text.insert(tk.END, f"   Source: {article['source']}\n")
            self.news_text.insert(tk.END, f"   Published: {article['published']}\n")
            self.news_text.insert(tk.END, f"   Summary: {article['summary']}\n")
            self.news_text.insert(tk.END, f"   Link: {article['link']}\n\n")
            
    def generate_predictions(self):
        """Generate and display Bitcoin predictions using Gemini AI"""
        if not self.predictor.news_data:
            messagebox.showwarning("No Data", "Please fetch real news first!")
            return
        
        self.status_var.set("🤖 Analyzing with Gemini AI...")
        
        # Use thread for AI analysis to avoid UI blocking
        thread = threading.Thread(target=self.ai_analysis_worker)
        thread.daemon = True
        thread.start()
        
    def ai_analysis_worker(self):
        """Worker function for AI analysis"""
        try:
            success = self.predictor.generate_predictions()
            
            if success:
                self.status_var.set("✅ AI analysis completed")
            else:
                self.status_var.set("⚠️ Using fallback analysis")
            
            # Update UI in main thread
            self.root.after(0, self.display_predictions)
            
        except Exception as e:
            self.status_var.set(f"❌ AI analysis error: {str(e)}")
            
    def display_predictions(self):
        """Display predictions in the text widget"""
        self.pred_text.delete(1.0, tk.END)
        
        # Header
        self.pred_text.insert(tk.END, "🤖 GEMINI AI BITCOIN ANALYSIS\n")
        self.pred_text.insert(tk.END, f"Current Price: ${self.predictor.bitcoin_price:,.2f}\n")
        self.pred_text.insert(tk.END, f"24h Change: {self.predictor.price_change_24h:+.2f}%\n")
        self.pred_text.insert(tk.END, f"Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        self.pred_text.insert(tk.END, "=" * 50 + "\n\n")
        
        # Display full Gemini analysis if available
        if hasattr(self.predictor, 'gemini_full_analysis'):
            self.pred_text.insert(tk.END, "🧠 GEMINI AI FULL ANALYSIS\n")
            self.pred_text.insert(tk.END, "-" * 30 + "\n")
            self.pred_text.insert(tk.END, self.predictor.gemini_full_analysis)
            self.pred_text.insert(tk.END, "\n\n")
        
        # Individual predictions
        self.pred_text.insert(tk.END, "📊 STRUCTURED PREDICTIONS\n")
        self.pred_text.insert(tk.END, "-" * 30 + "\n")
        
        for i, pred in enumerate(self.predictor.predictions, 1):
            self.pred_text.insert(tk.END, f"\n📈 PREDICTION #{i} ({pred['source']})\n")
            self.pred_text.insert(tk.END, f"Direction: {pred['direction']}\n")
            self.pred_text.insert(tk.END, f"Price Range: {pred['price_range']}\n")
            self.pred_text.insert(tk.END, f"Probability: {pred['probability']}%\n")
            self.pred_text.insert(tk.END, f"Timeframe: {pred['timeframe']}\n")
            self.pred_text.insert(tk.END, f"Analysis: {pred['reasons']}\n")
            
        # Aggregated probabilities
        if self.predictor.aggregated_probabilities:
            self.pred_text.insert(tk.END, "\n📈 OVERALL PROBABILITY DISTRIBUTION\n")
            self.pred_text.insert(tk.END, "-" * 30 + "\n")
            for direction, prob in self.predictor.aggregated_probabilities.items():
                self.pred_text.insert(tk.END, f"{direction}: {prob}%\n")
            
        # Disclaimer
        self.pred_text.insert(tk.END, "\n⚠️ IMPORTANT DISCLAIMER\n")
        self.pred_text.insert(tk.END, "This analysis uses real data and AI but is for educational purposes only.\n")
        self.pred_text.insert(tk.END, "Cryptocurrency trading carries significant risk. Never invest more than you can afford to lose.\n")
        self.pred_text.insert(tk.END, "Always do your own research and consult with financial advisors.\n")
        
    def handle_chat(self):
        user_text = self.chat_input.get().strip()
        if not user_text:
            return
        # Display user's message
        self.chat_display.config(state='normal')
        self.chat_display.insert(tk.END, f"You: {user_text}\n", 'user')
        self.chat_display.config(state='disabled')
        self.chat_display.yview(tk.END)
        self.chat_input.delete(0, tk.END)
        self.status_var.set("🤖 AI is thinking...")
        # Start thread for AI response
        thread = threading.Thread(target=self.chat_response_worker, args=(user_text,))
        thread.daemon = True
        thread.start()

    def chat_response_worker(self, user_text):
        answer = self.predictor.ask_gemini(user_text)
        # Insert AI's response in chat display on main thread
        self.root.after(0, lambda: self.insert_ai_response(answer))
        # Check if user input should influence predictions
        user_lower = user_text.lower()
        if "bullish" in user_lower:
            self.root.after(0, lambda: self.apply_sentiment_adjustment('Bullish'))
        if "bearish" in user_lower:
            self.root.after(0, lambda: self.apply_sentiment_adjustment('Bearish'))
        # Detect timeframe keywords (e.g., hours, days, weeks)
        timeframe_match = re.search(r"(\d+)\s*(hour|hours|day|days|week|weeks|month|months)", user_lower)
        if timeframe_match:
            new_timeframe = timeframe_match.group(0)
            self.root.after(0, lambda: self.apply_timeframe_adjustment(new_timeframe))

    def insert_ai_response(self, response_text):
        self.chat_display.config(state='normal')
        self.chat_display.insert(tk.END, f"AI: {response_text}\n\n", 'ai')
        self.chat_display.config(state='disabled')
        self.chat_display.yview(tk.END)
        self.status_var.set("Ready")

    def apply_sentiment_adjustment(self, sentiment):
        if not self.predictor.predictions:
            return
        if sentiment == 'Bullish':
            for pred in self.predictor.predictions:
                if pred['direction'] == 'Bullish':
                    pred['probability'] = min(pred['probability'] + 10, 100)
                elif pred['direction'] == 'Bearish':
                    pred['probability'] = max(pred['probability'] - 10, 0)
        elif sentiment == 'Bearish':
            for pred in self.predictor.predictions:
                if pred['direction'] == 'Bearish':
                    pred['probability'] = min(pred['probability'] + 10, 100)
                elif pred['direction'] == 'Bullish':
                    pred['probability'] = max(pred['probability'] - 10, 0)
        self.predictor.calculate_aggregated_probabilities()
        self.display_predictions()

    def apply_timeframe_adjustment(self, timeframe):
        if not self.predictor.predictions:
            return
        for pred in self.predictor.predictions:
            pred['timeframe'] = timeframe
        self.display_predictions()

    def export_report(self):
        """Export analysis report to file"""
        try:
            if not self.predictor.predictions:
                messagebox.showwarning("No Data", "No predictions to export!")
                return
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("JSON files", "*.json")],
                title="Save Analysis Report"
            )
            
            if filename:
                report_data = {
                    "timestamp": datetime.now().isoformat(),
                    "bitcoin_price": self.predictor.bitcoin_price,
                    "price_change_24h": self.predictor.price_change_24h,
                    "api_status": self.api_status,
                    "news_articles": len(self.predictor.news_data),
                    "predictions": self.predictor.predictions,
                    "aggregated_probabilities": self.predictor.aggregated_probabilities,
                    "gemini_analysis": getattr(self.predictor, 'gemini_full_analysis', 'Not available')
                }
                
                if filename.endswith('.json'):
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(report_data, f, indent=2, ensure_ascii=False)
                    self.status_var.set(f"✅ Report saved to {filename}")
                else: # Default to text file
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(f"Crypto Predictor Analysis Report\n")
                        f.write(f"Generated: {report_data['timestamp']}\n")
                        f.write(f"Bitcoin Price: ${report_data['bitcoin_price']:,.2f}\n")
                        f.write(f"24h Change: {report_data['price_change_24h']:+.2f}%\n")
                        f.write(f"Data Status: {report_data['api_status']}\n")
                        f.write(f"News Articles Fetched: {report_data['news_articles']}\n\n")
                        
                        f.write("=== Gemini AI Full Analysis ===\n")
                        f.write(report_data['gemini_analysis'] + "\n\n")
                        
                        f.write("=== Structured Predictions ===\n")
                        for i, pred in enumerate(report_data['predictions'], 1):
                            f.write(f"Prediction #{i} ({pred['source']}):\n")
                            f.write(f"  Direction: {pred['direction']}\n")
                            f.write(f"  Price Range: {pred['price_range']}\n")
                            f.write(f"  Probability: {pred['probability']}%\n")
                            f.write(f"  Timeframe: {pred['timeframe']}\n")
                            f.write(f"  Reasons: {pred['reasons']}\n\n")
                            
                        f.write("=== Overall Probability Distribution ===\n")
                        for direction, prob in report_data['aggregated_probabilities'].items():
                            f.write(f"{direction}: {prob}%\n")
                        f.write("\n")
                        f.write("IMPORTANT DISCLAIMER: This analysis is for educational purposes only. Cryptocurrency trading carries significant risk. Never invest more than you can afford to lose. Always do your own research and consult with financial advisors.\n")
                    self.status_var.set(f"✅ Report saved to {filename}")
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export report: {str(e)}")
            self.status_var.set(f"❌ Export failed: {str(e)}")

    def refresh_all(self):
        """Refreshes all data and re-generates predictions."""
        self.status_var.set("🔄 Refreshing all data and analysis...")
        # Clear existing data and predictions
        self.predictor.news_data = []
        self.predictor.predictions = []
        self.predictor.aggregated_probabilities = {}
        self.predictor.bitcoin_price = 0
        self.predictor.price_change_24h = 0
        self.news_text.delete(1.0, tk.END)
        self.pred_text.delete(1.0, tk.END)
        
        # Start the full process
        refresh_thread = threading.Thread(target=self._full_refresh_worker)
        refresh_thread.daemon = True
        refresh_thread.start()

    def _full_refresh_worker(self):
        """Worker function to execute full refresh in sequence."""
        try:
            # Step 1: Fetch news and price
            self.status_var.set("🔄 Fetching real news and price...")
            news_success = self.predictor.fetch_news()
            self.root.after(0, self.update_price_display)
            self.root.after(0, self.display_news)

            if not news_success:
                self.status_var.set("⚠️ Failed to fetch real news, using mock data. AI analysis might be limited.")
                self.root.after(2000, lambda: self.status_var.set("🤖 Analyzing with Gemini AI (using mock data)..."))
            else:
                self.root.after(0, lambda: self.status_var.set("✅ Real news and price data loaded. Analyzing with Gemini AI..."))

            # Step 2: Generate predictions
            predictions_success = self.predictor.generate_predictions()
            self.root.after(0, self.display_predictions)

            if predictions_success:
                self.root.after(0, lambda: self.status_var.set("✅ Full refresh completed successfully."))
            else:
                self.root.after(0, lambda: self.status_var.set("⚠️ Full refresh completed, but AI analysis might be limited or fallback used."))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"❌ Full refresh error: {str(e)}"))

if __name__ == "__main__":
    root = tk.Tk()
    app = CryptoPredictorGUI(root)
    root.mainloop()

