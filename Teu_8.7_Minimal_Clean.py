# ==============================================================================
# Minimal Teu 8.7 — Verified Clean Base with Logging, NLTK, and Gemini AI
# ==============================================================================

import os
import logging
import nltk

# --- Logging Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("teu_minimal.log"),
        logging.StreamHandler()
    ]
)

# --- NLTK Setup ---
try:
    nltk.data.find('sentiment/vader_lexicon.zip')
except LookupError:
    logging.info("Downloading VADER lexicon...")
    nltk.download('vader_lexicon')
    logging.info("Downloaded VADER lexicon.")

# --- Gemini AI Config (Safe) ---
GEMINI_API_KEY = "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og"

try:
    import google.generativeai as genai
    if GEMINI_API_KEY and "YOUR_KEY" not in GEMINI_API_KEY:
        genai.configure(api_key=GEMINI_API_KEY)
        print("✅ Gemini AI configured.")
    else:
        print("⚠️ Gemini API key is placeholder or missing.")
except ImportError:
    print("⚠️ google.generativeai module not installed.")
except Exception as e:
    print(f"❌ Error configuring Gemini: {e}")

# --- App Placeholder ---
if __name__ == "__main__":
    logging.info("Minimal Teu 8.7 launched.")
    print("Teu 8.7 Minimal version running.")
