import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, Menu
import threading
import requests
import json
import re
import time
from datetime import datetime, timedelta
from urllib.parse import urljoin
import random
import logging
import os
import pytz # For timezone handling in news timestamps and global market times

# Third-party library imports
# Ensure these are installed: pip install requests feedparser beautifulsoup4 nltk yfinance newsapi-python google-generativeai matplotlib pytz
import feedparser
from bs4 import BeautifulSoup
import nltk
from nltk.sentiment.vader import SentimentIntensityAnalyzer
import yfinance as yf
from newsapi import NewsApiClient
import google.generativeai as genai

# Imports for Matplotlib Charting
import matplotlib
matplotlib.use('TkAgg') # Specify the backend for tkinter
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk # Added NavigationToolbar2Tk

# ==============================================================================
# SECTION 2: INITIAL SETUP AND CONFIGURATION
# ==============================================================================

# --- Logging Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s',
    handlers=[
        logging.FileHandler("teu_8_6_upgraded_financial_analysis.log"),
        logging.StreamHandler()
    ]
)

# --- NLTK Data Download (if not already present) ---
try:
    nltk.data.find('sentiment/vader_lexicon.zip')
# Changed to catch a general Exception to resolve Pylint 'no-member' warning
except Exception:
    logging.info("Downloading NLTK VADER lexicon...")
    nltk.download('vader_lexicon')
    logging.info("NLTK VADER lexicon downloaded.")

# --- API Keys and Configuration ---
# IMPORTANT: Replace with your actual API keys.
# It is recommended to load API keys from environment variables for security.
# For simplicity, they are embedded here as requested, but be cautious in production.
NEWS_API_KEY = "3a0889c670a44d03998b671191ec557b"  # Your NewsAPI Key - REPLACE WITH YOUR ACTUAL, VALID NEWSAPI KEY
GEMINI_API_KEY = "AIzaSyBwX5yW-X7sL-tW9oZ-a4b5c6d7e8f9g0" # Your Gemini API Key - REPLACE WITH YOUR ACTUAL KEY
os.environ["GOOGLE_API_KEY"] = GEMINI_API_KEY
genai.configure(api_key=GEMINI_API_KEY)

# --- Global Market Data Source (example: Binance, KuCoin, etc. - simplified) ---
GLOBAL_MARKET_URL = "https://api.coingecko.com/api/v3/simple/price"
GLOBAL_MARKET_PARAMS = {
    "ids": "bitcoin,ethereum,ripple,solana,cardano,dogecoin,binancecoin,polkadot",
    "vs_currencies": "usd",
    "include_24hr_change": "true"
}

# ==============================================================================
# SECTION 3: CORE FINANCIAL ANALYZER LOGIC
# ==============================================================================

class FinancialAnalyzer:
    def __init__(self):
        self.news_api = NewsApiClient(api_key=NEWS_API_KEY)
        self.sentiment_analyzer = SentimentIntensityAnalyzer()
        self.gemini_model = genai.GenerativeModel('gemini-pro')
        self.news_data = []
        self.stock_data = {}
        self.market_data = {} # For global crypto/market data
        self.predictions = {}
        self.gemini_full_analysis = ""
        self.chat_history = [] # For Gemini chat context

    def fetch_news(self, query="cryptocurrency OR blockchain OR bitcoin OR ethereum") -> bool:
        """Fetches recent news articles using NewsAPI and RSS feeds."""
        self.news_data = [] # Clear previous news data
        try:
            # Fetch from NewsAPI
            top_headlines = self.news_api.get_everything(
                q=query,
                language='en',
                sort_by='relevancy',
                page_size=20
            )
            if top_headlines and top_headlines.get('articles'):
                self.news_data.extend(top_headlines['articles'])
                logging.info(f"Fetched {len(top_headlines['articles'])} articles from NewsAPI.")
            else:
                logging.warning("NewsAPI returned no articles or an empty response.")

            # Also fetch from RSS feeds (e.g., CoinDesk, decrypt)
            rss_feeds = [
                "https://feeds.coindesk.com/coindesk/headlines",
                "https://decrypt.co/feed"
            ]
            for feed_url in rss_feeds:
                try:
                    feed = feedparser.parse(feed_url)
                    for entry in feed.entries:
                        # Basic de-duplication: check if title or URL already exists
                        if not any(a.get('title') == entry.title or a.get('url') == entry.link for a in self.news_data):
                            self.news_data.append({
                                'source_name': entry.get('feed', {}).get('title', 'RSS Feed'),
                                'title': entry.title,
                                'description': entry.summary if hasattr(entry, 'summary') else entry.title,
                                'url': entry.link,
                                'published_at': entry.published if hasattr(entry, 'published') else datetime.now().isoformat(),
                                'content': entry.content[0].value if hasattr(entry, 'content') else ''
                            })
                    logging.info(f"Fetched articles from RSS feed: {feed_url}. Total articles: {len(self.news_data)}")
                except Exception as e:
                    logging.warning(f"Failed to fetch RSS feed {feed_url}: {e}")

            if not self.news_data:
                logging.warning("No news data was fetched from any source.")
                return False
            return True
        except Exception as e:
            logging.error(f"Error fetching news: {e}", exc_info=True)
            self.news_data = []
            return False

    def fetch_stock_data(self, ticker="BTC-USD") -> bool:
        """Fetches historical stock/crypto data using yfinance."""
        try:
            stock = yf.Ticker(ticker)
            hist = stock.history(period="7d") # Last 7 days data
            if not hist.empty:
                # Reset index to turn 'Date' from index into a regular column
                # Then convert to dictionary records
                self.stock_data[ticker] = hist.reset_index().to_dict('records')
                logging.info(f"Fetched data for {ticker}.")
                return True
            logging.warning(f"No historical data found for {ticker}.")
            return False
        except Exception as e:
            logging.error(f"Error fetching stock data for {ticker}: {e}", exc_info=True)
            self.stock_data[ticker] = []
            return False

    def fetch_global_market_data(self) -> bool:
        """Fetches global cryptocurrency market data."""
        try:
            response = requests.get(GLOBAL_MARKET_URL, params=GLOBAL_MARKET_PARAMS, timeout=10)
            response.raise_for_status()
            data = response.json()
            for coin_id, values in data.items():
                price = values.get('usd')
                change_24h = values.get('usd_24h_change')
                self.market_data[coin_id] = (price, change_24h)
            logging.info(f"Fetched global market data for {len(self.market_data)} coins.")
            return True
        except requests.exceptions.RequestException as e:
            logging.error(f"Error fetching global market data: {e}", exc_info=True)
            return False

    def fetch_all_data(self) -> bool:
        """Fetches all necessary data: news, stock/crypto, and global market."""
        news_success = self.fetch_news()
        stock_success = self.fetch_stock_data()
        market_success = self.fetch_global_market_data()
        return news_success and stock_success and market_success

    def analyze_sentiment(self, text: str) -> float:
        """Analyzes the sentiment of a given text."""
        return self.sentiment_analyzer.polarity_scores(text)['compound']

    def generate_main_predictions(self) -> bool:
        """Generates comprehensive financial predictions using Gemini AI."""
        try:
            # Prepare context for Gemini
            context_parts = []
            context_parts.append("As a sophisticated financial AI, provide a comprehensive analysis and prediction for the cryptocurrency market based on the following data:\n\n")

            if self.news_data:
                context_parts.append("--- RECENT NEWS ARTICLES ---\n")
                for i, article in enumerate(self.news_data[:10]): # Limit to top 10 articles for brevity
                    sentiment = self.analyze_sentiment(article.get('description', '') or article.get('title', ''))
                    context_parts.append(f"Article {i+1}:\nTitle: {article.get('title', 'N/A')}\nSource: {article.get('source_name', 'N/A')}\nSnippet: {article.get('description', 'N/A')}\nSentiment: {sentiment:.2f}\n\n")
                context_parts.append("\n")

            if self.market_data:
                context_parts.append("--- GLOBAL CRYPTOCURRENCY MARKET DATA (Current Price & 24h Change) ---\n")
                for coin, (price, change) in self.market_data.items():
                    context_parts.append(f"{coin.replace('-', ' ').title()}: Price=${price:,.2f}, 24h Change={change:.2f}%\n")
                context_parts.append("\n")

            if self.stock_data.get("BTC-USD"):
                context_parts.append("--- BITCOIN (BTC-USD) LAST 7 DAYS HISTORY ---\n")
                # Displaying simplified OHLC for last few days
                for entry in self.stock_data["BTC-USD"][-3:]: # Last 3 days
                    date = entry['Date'].strftime('%Y-%m-%d') if isinstance(entry['Date'], datetime) else entry['Date']
                    context_parts.append(f"Date: {date}, Open: {entry['Open']:.2f}, High: {entry['High']:.2f}, Low: {entry['Low']:.2f}, Close: {entry['Close']:.2f}\n")
                context_parts.append("\n")

            context_parts.append("Based on the above, provide:\n")
            context_parts.append("1. A general overview of the current market sentiment (e.g., bullish, bearish, neutral).\n")
            context_parts.append("2. Key factors influencing the market (from the news and data provided).\n")
            context_parts.append("3. Short-term (1-3 days) price prediction for Bitcoin and Ethereum, with reasoning.\n")
            context_parts.append("4. Potential risks and opportunities.\n")
            context_parts.append("5. Any other significant insights.\n\n")
            context_parts.append("Format your response as a professional financial report, using clear headings and bullet points where appropriate. DO NOT use markdown code blocks for the entire response. Start directly with the report. Do not include any pre-amble like 'Here is your report'.")

            prompt = "".join(context_parts)
            logging.info(f"Sending prompt to Gemini (length: {len(prompt)} characters).")

            response = self.gemini_model.generate_content(prompt)
            self.gemini_full_analysis = response.text
            self.predictions = {"gemini_analysis": self.gemini_full_analysis}
            logging.info("Gemini analysis generated.")
            return True
        except Exception as e:
            logging.error(f"Error generating Gemini predictions: {e}", exc_info=True)
            self.gemini_full_analysis = f"Failed to generate analysis: {e}"
            self.predictions = {}
            return False

    def chat_with_gemini(self, user_message: str) -> str:
        """Engages Gemini in a chat conversation, maintaining history."""
        try:
            # Append user message to history
            self.chat_history.append({'role': 'user', 'parts': [user_message]})

            # Generate response
            logging.info("Sending chat message to Gemini.")
            chat = self.gemini_model.start_chat(history=self.chat_history)
            response = chat.send_message(user_message)
            ai_response = response.text
            logging.info("Gemini chat response received.")

            # Append AI response to history
            self.chat_history.append({'role': 'model', 'parts': [ai_response]})
            return ai_response
        except Exception as e:
            logging.error(f"Error during Gemini chat: {e}", exc_info=True)
            return f"Error communicating with AI: {e}"

# ==============================================================================
# SECTION 4: TKINTER GUI APPLICATION
# ==============================================================================

class TeuFinancialApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Teu 8.6 (Upgraded): Unified Financial Analysis Platform")
        self.root.geometry("1200x800")

        self.analyzer = FinancialAnalyzer()

        # Initialize NLTK VADER once for the app
        self.sentiment_analyzer = SentimentIntensityAnalyzer()

        self._setup_ui()
        self._setup_menu()

        # Initial data load in a thread
        self.refresh_all_threaded()

    def _setup_ui(self):
        # Notebook (Tabs)
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(pady=10, expand=True, fill='both')

        # Tab 1: Dashboard
        self.dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.dashboard_frame, text='📊 Dashboard')
        self._setup_dashboard_tab()

        # Tab 2: News
        self.news_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.news_frame, text='📰 News')
        self._setup_news_tab()

        # Tab 3: AI Analysis & Predictions
        self.analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analysis_frame, text='🤖 AI Analysis')
        self._setup_analysis_tab()

        # Tab 4: Chat
        self.chat_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.chat_frame, text='💬 Chat')
        self._setup_chat_tab()

        # Tab 5: Charts
        self.chart_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.chart_frame, text='📈 Charts')
        self._setup_charts_tab()

        # Status Bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready.")
        self.status_label = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X)

        # Control Bar (buttons always visible)
        control_bar = ttk.Frame(self.root, padding="5 5 5 5")
        control_bar.pack(side=tk.TOP, fill=tk.X)

        ttk.Button(control_bar, text="🔄 Refresh Data", command=self.refresh_all_threaded).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_bar, text="🤖 Generate AI Analysis", command=self.generate_predictions_threaded).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_bar, text="📄 Export Report", command=self.export_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_bar, text="❌ Exit", command=self.root.quit).pack(side=tk.RIGHT, padx=5)


    def _setup_menu(self):
        menubar = Menu(self.root)
        self.root.config(menu=menubar)

        # File Menu
        file_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Refresh All Data", command=self.refresh_all_threaded)
        file_menu.add_command(label="Export Report", command=self.export_report)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)

        # Help Menu
        help_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self._show_about)


    def _setup_dashboard_tab(self):
        # Basic layout: Header for general market, then specific tickers
        # header_font = ('Arial', 14, 'bold') # Removed unused variable
        label_font = ('Arial', 11)

        # Global Market Summary
        market_summary_frame = ttk.LabelFrame(self.dashboard_frame, text="Global Crypto Market Summary", padding="10")
        market_summary_frame.pack(pady=10, padx=10, fill='x')

        self.market_labels = {} # Store labels for dynamic update
        coins_to_display = ["bitcoin", "ethereum", "ripple", "solana", "cardano", "dogecoin", "binancecoin", "polkadot"]
        for i, coin in enumerate(coins_to_display):
            row = i // 2
            col = i % 2 * 2 # col 0 and 2

            ttk.Label(market_summary_frame, text=f"{coin.replace('-', ' ').title()}:", font=label_font).grid(row=row, column=col, sticky='w', padx=5, pady=2)
            price_label = ttk.Label(market_summary_frame, text="Fetching...", font=label_font)
            price_label.grid(row=row, column=col+1, sticky='w', padx=5, pady=2)
            self.market_labels[coin] = price_label

        # Placeholder for more detailed dashboard elements
        ttk.Label(self.dashboard_frame, text="More dashboard widgets coming soon!", font=('Arial', 10, 'italic')).pack(pady=20)


    def _setup_news_tab(self):
        news_panel = ttk.Frame(self.news_frame, padding="10")
        news_panel.pack(expand=True, fill='both')

        ttk.Label(news_panel, text="Recent News Articles:", font=('Arial', 12, 'bold')).pack(pady=5, anchor='w')

        self.news_text = scrolledtext.ScrolledText(news_panel, wrap=tk.WORD, state='disabled', height=20, font=('Arial', 10))
        self.news_text.pack(expand=True, fill='both')

    def _setup_analysis_tab(self):
        analysis_panel = ttk.Frame(self.analysis_frame, padding="10")
        analysis_panel.pack(expand=True, fill='both')

        ttk.Label(analysis_panel, text="AI Financial Analysis & Predictions:", font=('Arial', 12, 'bold')).pack(pady=5, anchor='w')

        self.analysis_text = scrolledtext.ScrolledText(analysis_panel, wrap=tk.WORD, state='disabled', height=25, font=('Arial', 10))
        self.analysis_text.pack(expand=True, fill='both')

    def _setup_chat_tab(self):
        chat_panel = ttk.Frame(self.chat_frame, padding="10")
        chat_panel.pack(expand=True, fill='both')

        self.chat_display = scrolledtext.ScrolledText(chat_panel, wrap=tk.WORD, state='disabled', height=15, font=('Arial', 10))
        self.chat_display.pack(pady=10, expand=True, fill='both')

        # Chat Input
        input_frame = ttk.Frame(chat_panel)
        input_frame.pack(fill='x', pady=5)

        self.chat_input = ttk.Entry(input_frame, font=('Arial', 10))
        self.chat_input.pack(side=tk.LEFT, expand=True, fill='x', padx=(0, 5))
        self.chat_input.bind("<Return>", self._send_chat_message) # Bind Enter key

        ttk.Button(input_frame, text="Send", command=self._send_chat_message).pack(side=tk.RIGHT)

        self.chat_history_list = [] # Store chat for display and potential re-context

    def _setup_charts_tab(self):
        chart_panel = ttk.Frame(self.chart_frame, padding="10")
        chart_panel.pack(expand=True, fill='both')

        ttk.Label(chart_panel, text="Price & Volume Charts (BTC-USD)", font=('Arial', 12, 'bold')).pack(pady=5)

        # Matplotlib Figure
        self.fig = Figure(figsize=(10, 6), dpi=100)
        self.ax1 = self.fig.add_subplot(111) # Primary axis for price
        self.ax2 = self.ax1.twinx() # Secondary axis for volume

        self.canvas = FigureCanvasTkAgg(self.fig, master=chart_panel)
        self.canvas_widget = self.canvas.get_tk_widget()
        self.canvas_widget.pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.toolbar = NavigationToolbar2Tk(self.canvas, chart_panel)
        self.toolbar.update()
        self.canvas_widget.pack(side=tk.TOP, fill=tk.BOTH, expand=1)

    def _show_about(self):
        messagebox.showinfo(
            "About Teu 8.6 (Upgraded)",
            "Teu 8.6 (Upgraded): Unified AI-Powered Financial Analysis Platform\n\n"
            "Developed by [Your Name/Organization]\n"
            "Version: 8.6 (Upgraded)\n"
            "Features: Real-time Data (News, Crypto, Stocks), AI Analysis (Gemini), "
            "Interactive Chat, Dynamic Charting, Export functionality.\n\n"
            "Disclaimer: For informational purposes only. Not financial advice."
        )

    # --- Display Update Methods ---
    def update_all_displays(self):
        self.display_market_data()
        self.display_news()
        self.display_stock_chart()

    def display_market_data(self) -> None:
        """Updates the market data display on the dashboard."""
        logging.info("Updating market data display.")
        for name, label in self.market_labels.items():
            price, change = self.analyzer.market_data.get(name, (0, 0))
            if price and price > 0:
                color = "green" if change >= 0 else "red"
                arrow = '▲' if change >= 0 else '▼'
                label.config(text=f"${price:,.2f} ({arrow} {change:.2f}%)", foreground=color)
            else:
                label.config(text="Not Available", foreground="gray")

    def display_news(self) -> None:
        """Displays fetched news articles with full details."""
        self.news_text.config(state='normal')
        self.news_text.delete(1.0, tk.END)
        if not self.analyzer.news_data:
            self.news_text.insert(tk.END, "No news articles fetched. Please ensure API key is valid and check internet connection.\n")
        else:
            for _, article in enumerate(self.analyzer.news_data): # Changed i to _ for unused variable
                self.news_text.insert(tk.END, f"TITLE: {article.get('title', 'N/A')}\n")
                self.news_text.insert(tk.END, f"SOURCE: {article.get('source_name', 'N/A')}\n")
                self.news_text.insert(tk.END, f"DATE: {article.get('published_at', 'N/A')}\n")
                self.news_text.insert(tk.END, f"LINK: {article.get('url', 'N/A')}\n")
                self.news_text.insert(tk.END, f"SNIPPET: {article.get('description', 'N/A')}\n\n")
        self.news_text.config(state='disabled')

    def display_predictions(self) -> None:
        """Displays the AI generated analysis and predictions."""
        self.analysis_text.config(state='normal')
        self.analysis_text.delete(1.0, tk.END)
        if self.analyzer.gemini_full_analysis:
            self.analysis_text.insert(tk.END, self.analyzer.gemini_full_analysis)
        else:
            self.analysis_text.insert(tk.END, "AI Analysis not available yet or failed to generate.")
        self.analysis_text.config(state='disabled')

    def display_chat_message(self, sender: str, message: str):
        """Displays a message in the chat scrolled text area."""
        self.chat_display.config(state='normal')
        self.chat_display.insert(tk.END, f"{sender}: {message}\n\n")
        self.chat_display.config(state='disabled')
        self.chat_display.see(tk.END) # Auto-scroll to bottom

    def display_stock_chart(self, ticker="BTC-USD"):
        """Displays the historical price and volume chart for a given ticker."""
        logging.info(f"Updating chart for {ticker}.")
        self.ax1.clear()
        self.ax2.clear() # Clear both axes for fresh plot

        data = self.analyzer.stock_data.get(ticker)
        if data:
            # 'Date' is now explicitly a datetime object from reset_index()
            dates = [entry['Date'] for entry in data]
            prices = [entry['Close'] for entry in data]
            volumes = [entry['Volume'] for entry in data]

            # Price plot
            self.ax1.plot(dates, prices, color='blue', label='Price')
            self.ax1.set_ylabel('Price (USD)', color='blue')
            self.ax1.tick_params(axis='y', labelcolor='blue')
            self.ax1.set_title(f'{ticker} Price and Volume (Last 7 Days)')
            self.fig.autofmt_xdate() # Rotate and align x-axis labels

            # Volume plot
            self.ax2.bar(dates, volumes, color='gray', alpha=0.3, label='Volume')
            self.ax2.set_ylabel('Volume', color='gray')
            self.ax2.tick_params(axis='y', labelcolor='gray')

            self.fig.legend(loc="upper left")
            self.canvas.draw()
            logging.info(f"Chart updated for {ticker}.")
        else:
            self.ax1.text(0.5, 0.5, "No chart data available.",
                          horizontalalignment='center', verticalalignment='center',
                          transform=self.ax1.transAxes, fontsize=12, color='red')
            self.canvas.draw()
            logging.warning(f"No chart data to display for {ticker}.")

    # --- Threading and Workers ---

    def _run_in_thread(self, target_func, on_complete=None, *args):
        """Generic function to run a task in a background thread."""
        def wrapper():
            success = False
            try:
                _ = target_func(*args) # Changed result to _ for unused variable
                success = True
            except Exception as e:
                logging.error(f"Error in background thread ({target_func.__name__}): {e}", exc_info=True)
            finally:
                if on_complete:
                    self.root.after(0, lambda: on_complete(success)) # Call callback on main thread

        thread = threading.Thread(target=wrapper, daemon=True)
        thread.start()

    def refresh_all_threaded(self):
        self.status_var.set("🔄 Refreshing all data sources...")
        self._run_in_thread(self.analyzer.fetch_all_data, self._on_refresh_complete)

    def generate_predictions_threaded(self):
        """Generates AI predictions in a separate thread."""
        self.status_var.set("🤖 Generating AI Analysis...")
        self._run_in_thread(self.analyzer.generate_main_predictions, self._on_predictions_complete)

    def _on_refresh_complete(self, success):
        self.update_all_displays()
        self.status_var.set("✅ Data refreshed. Generating AI analysis...")
        self.generate_predictions_threaded()

    def _on_predictions_complete(self, success):
        self.display_predictions()
        self.status_var.set("✅ AI Analysis Complete.")

    def _send_chat_message(self, event=None): # event=None allows it to be called by button or <Return>
        user_message = self.chat_input.get().strip()
        if not user_message:
            return

        self.display_chat_message("You", user_message)
        self.chat_input.delete(0, tk.END)
        self.status_var.set("💬 AI is typing...")

        # Run Gemini chat in a thread
        self._run_in_thread(
            lambda: self.analyzer.chat_with_gemini(user_message),
            self._on_chat_response_complete
        )

    def _on_chat_response_complete(self, success):
        if success and self.analyzer.chat_history:
            ai_response = self.analyzer.chat_history[-1]['parts'][0]
            self.display_chat_message("AI", ai_response)
            self.status_var.set("Ready for next question.")
        else:
            self.display_chat_message("AI", "Sorry, I couldn't process that. Please try again.")
            self.status_var.set("Error during chat.")

    def export_report(self):
        """Exports the current analysis to a text file."""
        if not self.analyzer.predictions and not self.analyzer.news_data and not self.analyzer.market_data:
            messagebox.showinfo("Export Report", "No data available to export. Please refresh data first.")
            return

        filepath = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("Text files", "*.txt")])
        if not filepath:
            return

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"Financial Analysis Report - {datetime.now():%Y-%m-%d %H:%M:%S}\n\n")

                # General Market Data
                f.write("="*50 + "\n")
                f.write("--- GLOBAL CRYPTOCURRENCY MARKET DATA ---\n")
                f.write("="*50 + "\n")
                if self.analyzer.market_data:
                    for coin, (price, change) in self.analyzer.market_data.items():
                        f.write(f"{coin.replace('-', ' ').title()}: Price=${price:,.2f}, 24h Change={change:.2f}%\n")
                else:
                    f.write("No market data available.\n")
                f.write("\n")

                # Recent News
                f.write("="*50 + "\n")
                f.write("--- RECENT NEWS ARTICLES ---\n")
                f.write("="*50 + "\n")
                if self.analyzer.news_data:
                    for i, article in enumerate(self.analyzer.news_data):
                        f.write(f"Title: {article.get('title', 'N/A')}\n")
                        f.write(f"Source: {article.get('source_name', 'N/A')}\n")
                        f.write(f"Published: {article.get('published_at', 'N/A')}\n")
                        f.write(f"URL: {article.get('url', 'N/A')}\n")
                        f.write(f"Description: {article.get('description', 'N/A')}\n\n")
                else:
                    f.write("No news articles available.\n")
                f.write("\n")

                # Stock/Crypto Historical Data (e.g., BTC-USD)
                f.write("="*50 + "\n")
                f.write("--- BTC-USD HISTORICAL DATA (Last 7 Days) ---\n")
                f.write("="*50 + "\n")
                if self.analyzer.stock_data.get("BTC-USD"):
                    for entry in self.analyzer.stock_data["BTC-USD"]:
                        date_val = entry['Date'].strftime('%Y-%m-%d') if isinstance(entry['Date'], datetime) else entry['Date']
                        f.write(f"Date: {date_val}, Open: {entry.get('Open', 'N/A'):.2f}, High: {entry.get('High', 'N/A'):.2f}, Low: {entry.get('Low', 'N/A'):.2f}, Close: {entry.get('Close', 'N/A'):.2f}, Volume: {entry.get('Volume', 'N/A'):,.0f}\n")
                else:
                    f.write("No BTC-USD historical data available.\n")
                f.write("\n")

                # Gemini AI Analysis
                f.write("="*50 + "\n")
                f.write("--- GEMINI AI FULL ANALYSIS & PREDICTION ---\n")
                f.write("="*50 + "\n")
                if self.analyzer.gemini_full_analysis:
                    f.write(self.analyzer.gemini_full_analysis)
                else:
                    f.write("AI analysis not generated or available.\n")
                f.write("\n")

            messagebox.showinfo("Export Successful", f"Report saved to:\n{filepath}")
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to save report: {e}")
            logging.error(f"Error saving report: {e}", exc_info=True)
            self.root.destroy() # Close the app if export fails as per user request

# ==============================================================================
# SECTION 6: MAIN EXECUTION BLOCK
# ==============================================================================
if __name__ == "__main__":
    try:
        logging.info("Application starting up...")
        root = tk.Tk()
        app = TeuFinancialApp(root)
        root.mainloop()
        logging.info("Application shutting down.")
    except Exception as e:
        logging.critical(f"A critical error occurred on startup: {e}", exc_info=True)
        messagebox.showerror("Fatal Error", f"The application encountered a fatal error and must close.\n\nDetails: {e}")
        # Ensure root window is destroyed if it exists to properly exit
        if 'root' in locals() and root.winfo_exists():
            root.destroy()